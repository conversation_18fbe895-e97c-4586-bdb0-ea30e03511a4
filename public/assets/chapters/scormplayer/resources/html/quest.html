<html xmlns="http://www.w3.org/1999/xhtml" >
<head>
	<title>Assessment</title>
	<link href="./resources/css/style.css" type="text/css" rel="stylesheet">
	<script defer type="text/javascript" src="./resources/js/jquery-3.3.1.min.js" ></script>
	<script defer type="text/javascript" src="./resources/js/jquery-ui.js" ></script>
	<script defer type="text/javascript" src="./resources/js/testcore.js" ></script>


	
	 <script src="../shared/scormfunctions.js" type="text/javascript"></script>
	 <script src="../shared/contentfunctions.js" type="text/javascript"></script>
</head>
<body>
<h1>Knowledge Check</h1>
	<script defer type="text/javascript">
		window.onload = function() {
 			window.test = new Test(new Array());
			var test = new Test(new Array());
			
			var queryString = new String(document.location.search);
			console.log(queryString);
			queryString = queryString.replace("?", "");
			console.log(queryString);
			var includeFiles = queryString.split("&");
			console.log(includeFiles);
			for (var i=0; i<includeFiles.length; i++)
				{
				var questionsFile = includeFiles[i].replace("questions=", "");
				console.log(questionsFile);
				console.log('../', questionsFile, '/questions.js');
				var newCont = '';
				newCont += '<script defer type="text/JavaScript">\n';
					//newCont += 'var isInIframe = (window.location != window.parent.location) ? true : false; alert(isInIframe);';
					newCont += '$.getScript( "'+questionsFile+'", function( data, textStatus, jqxhr )\n';
					newCont += '{\n';
					newCont += 'console.log( data ); // Data returned\n';
					newCont += 'console.log( textStatus ); // Success\n';
					newCont += 'console.log( jqxhr.status ); // 200\n';
					newCont += 'console.log( "Load was performed." );\n';
					newCont += '$("body").append(RenderTest(test));\n';
					newCont += 'AddTagLine();\n';
					newCont += '});\n';
				newCont += '<\/script>\n';
				console.log('------------------------------');
				console.log(newCont);
				console.log('------------------------------');
				//document.write(newCont);
				$('body').append(newCont);
				}
			
			
			};
	 </script>
<footer class="pie"></footer>
</body>
</html>
