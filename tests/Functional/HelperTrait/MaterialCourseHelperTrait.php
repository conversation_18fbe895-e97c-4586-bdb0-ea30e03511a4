<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\MaterialCourse;
use App\Entity\User;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait MaterialCourseHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetMaterialCourse(
        Course $course,
        ?Announcement $announcement = null,
        string $name = 'Test Material Course',
        string $fileName = 'test_material_code.pdf',
        string $originalName = 'test_material.pdf',
        string $mimeType = 'application/pdf',
        string $typeMaterial = '4',
        ?string $urlMaterial = null,
        bool $isDownload = false,
        bool $isVisible = false,
        bool $isActive = true,
        ?string $type = null,
        ?User $createdBy = null,
    ): MaterialCourse {
        $em = $this->getEntityManager();
        $materialCourse = new MaterialCourse();
        $materialCourse->setCourse($course)
            ->setAnnouncement($announcement)
            ->setFilename($fileName)
            ->setOriginalName($originalName)
            ->setName($name)
            ->setMimeType($mimeType)
            ->setTypeMaterial($typeMaterial)
            ->setUrlMaterial($urlMaterial)
            ->setIsDownload($isDownload)
            ->setIsVisible($isVisible)
            ->setIsActive($isActive)
            ->setType($type)
            ->setCreatedBy($createdBy);

        $em->persist($materialCourse);
        $em->flush();
        $em->refresh($materialCourse);

        return $materialCourse;
    }
}
