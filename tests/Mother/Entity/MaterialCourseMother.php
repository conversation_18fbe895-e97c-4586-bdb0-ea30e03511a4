<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\MaterialCourse;
use App\Entity\User;

class MaterialCourseMother
{
    public static function create(
        ?int $id = null,
        ?Course $course = null,
        ?Announcement $announcement = null,
        ?string $name = 'example.pdf',
        ?string $mimeType = 'application/pdf',
        ?string $typeMaterial = '1',
        ?string $urlMaterial = null,
        ?bool $isDownload = false,
        ?string $filename = null,
        ?string $originalName = null,
        ?string $fileSize = null,
        ?bool $isVisible = false,
        ?bool $isActive = false,
        ?string $type = null,
        ?int $parentId = null,
        ?User $createdBy = null,
    ): MaterialCourse {
        $materialCourse = new MaterialCourse();

        if (null !== $id) {
            $materialCourse->setId($id);
        }

        $materialCourse->setCourse($course)
            ->setAnnouncement($announcement)
            ->setName($name)
            ->setMimeType($mimeType)
            ->setTypeMaterial($typeMaterial)
            ->setUrlMaterial($urlMaterial)
            ->setIsDownload($isDownload)
            ->setFilename($filename)
            ->setOriginalName($originalName)
            ->setFilename($filename)
            ->setFileSize($fileSize)
            ->setIsVisible($isVisible)
            ->setIsActive($isActive)
            ->setType($type)
            ->setParentId($parentId)
            ->setCreatedBy($createdBy);

        return $materialCourse;
    }
}
