<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loading">
    <spinner />
  </div>
  <div class="NewLibrary" v-else>
    <form action="" id="new-library-form" class="NewLibrary__form" @submit.prevent>
      <div class="form__data col-12">
        <file-selector name="thumbnail" :default-image="library.thumbnail" preview-default-class="full-size" />
        <div class="form__general_data">
          <div class="form-group col-12 p-0">
            <label for="name">{{ $t('LIBRARY.NAME') }}</label>
            <input id="name" name="name" type="text" class="form-control" required v-model="library.name">
          </div>

          <div class="form-group col-12 p-0">
            <label for="description">{{ $t('LIBRARY.DESCRIPTION') }}</label>
            <input type="hidden" v-model="library.description" id="description" name="description">
            <froala tag="textarea" v-model="library.description" :config="froalaConfig"/>
          </div>

          <div class="col-12 mb-3 p-0">
            <label>{{ $t('LIBRARY.UPLOAD_CONTENT') }}</label>
            <div class="col-12 form__general_data__file_selection">
              <div class="file-types">
                <button type="button" class="link" :class="library.type === 'URL' ? 'active' : ''" @click="library.type = 'URL'">
                  <i class="fa fa-link"></i>
                  <span>URL</span>
                </button>
                <button type="button" class="file" :class="library.type === 'PDF' ? 'active' : ''" @click="library.type = 'PDF'">
                  <i class="fa fa-file-pdf"></i>
                  <span>PDF</span>
                </button>
                <button type="button" class="video" :class="library.type === 'VIDEO' ? 'active' : ''" @click="library.type = 'VIDEO'">
                  <i class="fab fa-youtube"></i>
                  <span>Video</span>
                </button>
                <button type="button" class="audio" :class="library.type === 'AUDIO' ? 'active' : ''" @click="library.type = 'AUDIO'">
                  <i class="fa fa-volume-up"></i>
                  <span>Audio</span>
                </button>
              </div>

              <div class="form__general_data__file_selection__content" :class="library.type">
                <input type="url" name="url" class="form-control w-100" v-if="library.type === 'URL'">
                <input id="upload-file" name="upload-file" type="file" :accept="acceptFileType" @change="onFileSelected($event.target)" v-else>
                <div class="file-selection-custom" v-if="library.type !== 'URL'">
                  <button @click="openFileSelector()" id="select-upload-file" type="button" class="btn btn-info">{{ $t('LIBRARY.UPLOAD_CONTENT') }}</button>
                  <span>{{ selectedFileFilename ?? $t('LIBRARY.NO_FILE_SELECTED') }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="col-12 p-0" v-if="localesLength > 1">
            <label>{{ $t('LOCALE') }}</label>
            <div class="col-12 form__general_data__locale_selection">
              <div class="locale-selection">
                <button type="button" class="locale"
                        v-for="(name, key) in locales" :class="library.locale === key ? 'active' : ''"
                        data-toggle="tooltip" data-placement="top" :title="name"
                        @click="library.locale = key" >{{ key }}</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="NewLibrary__form__permissions">
        <button-with-description
            title="LIBRARY.RATING.ALLOW"
            description="LIBRARY.RATING.DESCRIPTION"
            v-model="library.enableRating"
        />

        <button-with-description
            title="LIBRARY.COMMENTS.ALLOW"
            description="LIBRARY.COMMENTS.DESCRIPTION"
            icon="fa fa-comments"
            v-model="library.enableComments"
        />
      </div>

      <div class="NewLibrary__form__footer">
        <button type="button" class="btn btn-danger m-1" @click="cancel()">{{ $t('LIBRARY.CANCEL') }}</button>
        <button type="submit" class="btn btn-info m-1" @click="submitNewLibrary">{{ $t('LIBRARY.SAVE') }}</button>
      </div>
    </form>
  </div>
</template>

<script>
import CategoryFilter from "../../admin/components/CategoryFilter.vue";
import $ from 'jquery';
import FileSelector from "../../common/components/FileSelector.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import ButtonWithDescription from "../../common/components/ButtonWithDescription.vue";

export default {
  name: "NewLibrary",
  components: {ButtonWithDescription, Spinner, FileSelector, CategoryFilter },
  $,
  props: {
    categoryName: {
      type: String,
      default: 'CategoryName'
    }
  },
  data() {
    return {
      loading: true,
      library: {
        name: '',
        description: '',
        enableRating: false,
        enableComments: false,
        url: '',
        type: 'PDF',
        locale: 'en',
        thumbnail: null,
        categoryId: this.$route.params.id
      },
      update: false,
      selectedFileFilename: null
    };
  },
  computed: {
    acceptFileType() {
      if (this.type === 'AUDIO') return 'audio/*';
      if (this.type === 'VIDEO') return 'video/mp4';
      return 'application/pdf';
    },
    locales() {
      return this.$store.getters['localeModule/getLocales'];
    },
    defaultLocale() {
      return this.$store.getters['localeModule/getDefaultLocale'];
    },
    localesLength() {
      return Object.keys(this.locales).length;
    },
    useGlobalEventBus() {
      return this.$store.getters['contentTitleModule/getUseGlobalEventBus'];
    },
    froalaConfig() {
      return {
        ...this.$store.getters['froalaEditorModule/getDefaultConfiguration'],
        pluginsEnabled: [],
        toolbarButtons: {
          moreText: {
            buttons: ['bold', 'italic', 'underline', 'fontSize', 'textColor', 'clearFormatting'],
          },
          moreMisc: {}
        }
      };
    }
  },
  created() {
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: this.$t(this.$route.name === 'EditLibrary' ? 'LIBRARY.EDIT_LIBRARY' : 'LIBRARY.NEW_CONTENT'),
        params: this.$route.params
      }
    });

    this.$store.dispatch('contentTitleModule/setActions', {
      route: this.$route.name,
      actions: [
        {
          name: this.$t('SAVE'),
          event: 'onSubmit',
          class: 'btn btn-primary'
        }
      ]
    })
    this.handleRouteParams();
  },
  mounted() {
    $('#btn-select-image').on('click', () => {
      $('#thumbnail').click();
    });
    this.locale = this.defaultLocale;
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('onSubmit', e => {
        this.submitNewLibrary();
      });
    }
  },
  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('onSubmit');
    }
  },
  methods: {
    cancel() {},
    async handleRouteParams() {
      this.loading = true;
      if (this.$route.name === 'EditLibrary') {
        const { data, error } = await this.$store.dispatch('libraryModule/getLibrary', this.$route.params.id);
        this.library = data.library;
      } else {
        if (this.locales.length === 1) {
          this.library.locale = this.locales[Object.keys(this.locales)[0]];
        }
      }
      this.loading = false;
    },

    saveLibrary(formData, update = false) {
      const self = this;
      function save() {
        if (update) return self.$store.dispatch('libraryModule/updateLibrary', { id: self.$route.params.id, formData });
        else return self.$store.dispatch('libraryModule/newLibrary', { categoryId: self.$route.params.id, formData: formData });
      }

      this.$store.dispatch('loaderModule/setLoading', {loading: true, message: this.$t('SAVING')});
      save().then(res => {
        const { data, error } = res;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t(`LIBRARY. ${update ? 'UPDATE' : 'CREATE'}.SUCCESS`) + '')
          this.$store.dispatch('contentTitleModule/removeRouteFromContentTitle', this.$route.name);
          this.$store.dispatch('routerModule/setDeleteLastRoute', true);
          this.$router.replace({ name: 'LibraryDetail', params: {category_id: update ? this.library.categoryId : this.$route.params.id, id: data}})
        }
      }).finally(() => {
        this.$store.dispatch('loaderModule/setLoading', {loading: false});
      });
    },
    async submitNewLibrary() {
      const currentForm = document.forms['new-library-form'];
      const formData = new FormData(currentForm);
      formData.append('type', this.library.type);
      formData.append('enable-rating', this.library.enableRating);
      formData.append('enable-comments', this.library.enableComments);
      formData.append('locale', this.library.locale);

      const url = formData.get('url');
      if (this.type === 'URL' && url.length < 1) {
        this.$toast.error('URL is required');
        return;
      }

      const update = this.$route.name === 'EditLibrary';
      this.$alertify.confirmWithTitle(
          this.$t('LIBRARY.CONFIRM_SAVE'),
          update ? this.$t('LIBRARY.WARNING_UPDATE') : this.$t('LIBRARY.NEW.INFO'),
          () => {
            this.saveLibrary(formData, update);
          },
          () => {},
      );
    },

    onFileSelected(input) {
      if (input.files && input.files[0]) {
        this.selectedFileFilename = input.files[0].name;
      }
    },

    openFileSelector() {
      $('#upload-file').click();
    }
  }
}
</script>

 <style scoped lang="scss"> 
.NewLibrary {
  background-color: #F6F7F8;
  &__form {
    .form__data {
      width: 100%;
      display: flex;
      flex-flow: column;
      justify-content: center;

      @media #{min-small-screen()} {
        grid-template-columns: 320px 1fr;
        column-gap: 1rem;
      }

      @media #{min-medium-screen()} {
        display: grid;
        grid-template-columns: 400px 1fr;
        column-gap: 1rem;
        padding: 2rem;
      }

      .FileSelector {
        width: 100%;
      }

      .form__general_data {
        width: 100%;

        .form__general_data__locale_selection {
          display: flex;
          flex-flow: row wrap;
          width: 100%;
          border: 1px solid #CBD5E1;
          background-color: #ffffff;
        }

        .form__general_data__file_selection {
          width: 100%;
          display: flex;
          flex-flow: column;
          border: 1px solid #CBD5E1;
          background-color: #ffffff;

          @media #{min-small-screen()} {
            display: grid;
            grid-template-columns: [file_type] 120px [content] auto;
          }
        }


        .form__general_data__file_selection {
          &__content {
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;

            input {
              display: none;
            }

            &.link {
              input {
                display: block;
              }
            }

            .file-selection-custom {
              display: flex;
              flex-flow: column;
              align-items: center;
              justify-content: center;
              span {
                font-size: 17px;
                color: #1E293B;
              }
            }
          }
        }

        .file-types {
          width: 100%;
        }

        .file-types, .locale-selection {
          display: flex;
          flex-flow: row wrap;
          align-items: center;
          justify-content: center;

          button {
            width: 50px;
            height: 50px;
            border: 1px solid #CBD5E1;
            border-radius: 5px;
            margin: 5px;
            color: #CBD5E1;
            background-color: #ffffff;
            font-size: 18px;
            display: flex;
            flex-flow: column;
            align-items: center;
            justify-content: center;
            span {
              font-size: 12px;
            }

            &.active {
              border: 1px solid #3468ac;
              color: #3468ac;
            }
          }
        }

        .locale-selection {
          width: 100%;
          button {
            text-transform: uppercase;
          }
        }
      }
    }

    &__permissions {
      width: 100%;
      margin-top: 2rem;
      background-color: #ffffff;
      display: flex;
      flex-flow: column;
      justify-content: center;

      @media #{min-small-screen()} {
        display: grid;
        grid-template-columns: 50% 50%;
        column-gap: 1rem;
        padding: 2rem;
      }

      .ButtonWithDescription {
        width: 100%;
      }
    }

    &__footer {
      margin-top: 2rem;
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: flex-end;
    }
  }
}
</style>
