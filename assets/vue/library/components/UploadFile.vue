<template>
  <form class="UploadFile" action="" id="upload-library-file" @submit.prevent>
    <fieldset class="col-12">
      <legend class="w-100 text-center">{{ text }}</legend>
      <div class="col-12 d-flex flex-row">
        <div class="col-8 form-group" v-if="library.type === 'file'">
          <label for="upload-file">File</label>
          <input type="file" class="form-control-file" id="upload-file" name="upload-file"
                 accept="application/pdf"
          >
        </div>
        <div class="col-8 form-group" v-if="library.type === 'video'">
          <label for="upload-file">File</label>
          <input type="file" class="form-control-file" id="upload-file" name="upload-file"
                 accept="video/mp4"
          >
        </div>
        <div class="col-8 form-group" v-if="library.type === 'audio'">
          <label for="upload-file">File</label>
          <input type="file" class="form-control-file" id="upload-file" name="upload-file"
                 accept="audio/*"
          >
        </div>
        <button @click="uploadFile()" type="button" class="btn btn-success ml-auto"><i class="fa fa-upload"></i> Upload</button>
      </div>
    </fieldset>
  </form>
</template>

<script>
export default {
  name: "UploadFile",
  props: {
    library: null,
    text: {
      type: String,
      default: 'No file has been provided. Please upload the file'
    }
  },
  methods: {
    uploadFile() {
      this.$alertify.confirmWithTitle(
          'Are you sure to upload',
          '...',
          () => {
            const form = document.forms['upload-library-file'];
            const formData = new FormData(form);
            this.$store.dispatch('libraryModule/uploadLibraryFile', { id: this.library.id, formData }).then(res => {
              const { error, data } = res;
              if (error) {
                this.$toast.error(data);
                this.$emit('on-file-upload', false);
              } else {
                this.$toast.success(data);
                this.$emit('on-file-upload', true);
              }
            });
          },
          () => {}
      );
    }
  }
}
</script>

 <style scoped lang="scss"> 
.UploadFile {
  width: 100%;
}
</style>
