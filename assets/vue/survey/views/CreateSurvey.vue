<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loading">
    <spinner />
  </div>

  <div class="FormView" v-else>
    <div class="w-100 d-flex flex-row Header p-4">
      <h4>{{ $t("SURVEY.UPDATE_SURVEY") }}</h4>
    </div>
    <div class="w-100">
      <ul class="nav nav-tabs ps-4 user-select-none hideOnPrint">
        <li class="nav-item" role="presentation" aria-selected="true">
          <button
            class="nav-link active"
            id="divFicha-tab"
            data-bs-toggle="tab"
            data-bs-target="#divFicha"
            type="button"
            role="tab"
            aria-controls="divFicha"
            aria-selected="true"
          >
            <i class="fa fa-address-card"></i>
            {{ $t("SURVEY.LABEL.DATA_SHEET") }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            id="divCategories-tab"
            data-bs-toggle="tab"
            data-bs-target="#divCategories"
            type="button"
            role="tab"
            aria-controls="divCategories"
            aria-selected="false"
          >
            <i class="fa fa-deaf"></i>
            {{ $t("SURVEY.AUDIENCE_TITLE") }}
          </button>
        </li>
      </ul>
    </div>
    <div class="tab-content bg-white p-0 pt-1 pb-1">
      <div
        class="tab-pane fade show active"
        id="divFicha"
        role="tabpanel"
        aria-labelledby="divFicha-tab"
      >
        <form id="new-survey-form" class="CreateSurvey">
          <div class="p-4">
            <Translation
              v-model="locale"
              :warning="warnings"
              v-for="t in translations"
              v-if="t.locale === locale"
            >
              <template v-slot:content>
                <div class="form-group col-12 required">
                  <label>{{ $t("NAME") }}</label>
                  <input
                    type="text"
                    class="form-control"
                    v-model="t.name"
                    required
                  />
                </div>
                <div class="form-group col-12">
                  <label>{{ $t("DESCRIPTION") }}</label>
                  <froala
                    tag="textarea"
                    v-model="t.description"
                    :config="froalaDescriptionConfig"
                  ></froala>
                </div>
              </template>
            </Translation>
          </div>

          <div class="w-100 pt-2">
            <questions v-model="questions"></questions>
          </div>
        </form>
      </div>
      <div
        class="tab-pane fade m-4 p-4"
        id="divCategories"
        role="tabpanel"
        aria-labelledby="divCategories-tab"
      >
        <survey-audience v-model="survey" />
      </div>

      <div class="col-12 d-flex align-items-center justify-content-end">
        <button type="submit" class="btn btn-primary" @click="submit()">
          {{ $t("SAVE") }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from "jquery";
import { get, sync } from "vuex-pathify";
import "bootstrap";
import NewQuestion from "../components/NewQuestion.vue";
import Questions from "../components/Questions.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import SurveyAudience from "./SurveyAudience.vue";
import Translation from "../../common/components/Translation.vue";
import AddRemove from "../../common/components/select/AddRemove.vue";

export default {
  name: "CreateSurvey",
  components: {
    SurveyAudience,
    Spinner,
    Questions,
    Translation,
    AddRemove,
    NewQuestion,
  },
  $,
  data() {
    return {
      locale: null,
      activePane: "questions",
      realtime: true,
      survey: {
        applyTo: 1,
        name: "",
        description: "",
        courses: [],
        announcements: [],
      },
      coursesTab: [],
      announcementsTaB: [],
    };
  },
  computed: {
    ...get("surveyModule", [
      "getTranslations",
      "getWarnings",
      "getQuestionsMain",
      "getIsSuperAdmin",
    ]),
    locales: get("localeModule/locales"),
    userLocale: get("localeModule/userLocale"),
    loading: get("surveyModule/loading"),
    /*****************FORM*****************************/
    applyTo: sync("surveyModule/form@applyTo"),
    questions: sync("surveyModule/form@questions"),
    courses: sync("surveyModule/form@courses"),
    announcements: sync("surveyModule/form@announcements"),
    /*************************************************/
    questionTypes: get("surveyModule/questionTypes"),

    translations() {
      return this.getTranslations();
    },
    warnings() {
      return this.getWarnings();
    },
    isSuperAdmin() {
      return this.getIsSuperAdmin();
    },
    froalaConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        pluginsEnabled: ["align"],
        events: {
          "html.get": (e) => {
            this.description = e;
          },
        },
      };
    },
    froalaDescriptionConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 150,
        pluginsEnabled: [
          "align",
          "link",
          "url",
          "lists",
          "paragraphStyle",
          "paragraphFormat",
          "quote",
        ],
      };
    },

    questionsMain() {
      return this.getQuestionsMain();
    },
  },
  async created() {
    // this.loading = true;
    await this.$store.dispatch(
      "surveyModule/initForm",
      this.$route.params.id ?? -1
    );
    const isUpdate = this.$route.name === "UpdateSurvey";

    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: isUpdate
          ? this.$t("SURVEY.UPDATE_SURVEY")
          : this.$t("SURVEY.CREATE_SURVEY"),
        params: {},
      },
    });

    if (isUpdate) {
      const result = await this.$store.dispatch(
        "surveyModule/getSurvey",
        this.$route.params.id
      );
      const { survey, questions, courses, announcements } = result.data;
      this.survey = survey;
      this.survey.courses = courses;
      this.survey.announcements = announcements;
    } else {
      this.questionsMain.map((question) => {
        this.questions.push(question);
      });
    }

    this.locale = this.userLocale;
  },

  methods: {
    getQuestionTypeIcon(type) {
      const index = this.questionTypes.findIndex((item) => item.type === type);
      return this.questionTypes[index].icon;
    },
    onQuestion(question) {
      this.questions.push(question);
      this.closeModal();
    },
    openModal() {
      $("#new-question-modal").modal({
        show: true,
        static: true,
        backdrop: false,
        keyboard: false,
      });
    },
    closeModal() {
      $("#new-question-modal").modal("hide");
    },

    async submit() {
      const form = document.forms["new-survey-form"];
      const formData = new FormData(form);
      this.survey.name = this.translations.find(
        (t) => t.locale === this.locale
      ).name;

      this.survey.translations = this.translations;

      const data = {
        survey: this.survey,
        name: this.translations.find((t) => t.locale === this.locale)
          .name,
        description: this.translations.find((t) => t.locale === this.locale)
          .description,
        questions: this.questions,
        translations: this.translations,
        applyTo: this.survey.applyTo,
        courses: this.survey.courses,
        announcements: this.survey.announcements,
        locale: this.locale,
      };

      const isUpdate = this.$route.name === "UpdateSurvey";

      if (isUpdate) {
        this.$alertify.confirmWithTitle(
          this.$t(`SURVEY.${isUpdate ? "UPDATE" : "CREATE"}.CONFIRM.TITLE`),
          this.$t(
            `SURVEY.${isUpdate ? "UPDATE" : "CREATE"}.CONFIRM.DESCRIPTION`
          ),
          () => {
            this.saveSurvey(data, true);
            this.returnToList();
          },
          () => {}
        );
      } else {
        this.$store.dispatch("surveyModule/save", {formData:data});
        this.returnToList();
      }

    },

    returnToList() {
      this.$router.push({ name: "Home" });
    },

    saveSurvey(formData, update = false) {
      // const self = this;
      // function save() {
      //   if (update)
      //     return self.$store.dispatch("surveyModule/updateSurvey", {
      //       id: self.$route.params.id,
      //       formData,
      //     });
      //   else
      //     return self.$store.dispatch("surveyModule/saveNewSurvey", formData);
      // }

      this.$store.dispatch("surveyModule/saveSurvey", {id: this.$route.params.id, formData:formData}).then((r) => {
        const { error } = r;
        if (error) this.$toast.error(this.$t("SURVEY.SAVE.FAILED") + "");
        else {
          this.$toast.success(this.$t("SURVEY.SAVE.SUCCESS") + "");
          this.$store.dispatch("contentTitleModule/setContentTitle", []);
          this.$store.dispatch("routerModule/setHistory", []);
          this.$router.replace({ name: "Home" });
        }
      });
    },
  },
};
</script>

 <style scoped lang="scss"> 
.CreateSurvey {
  &--content {
    @include nav-bar-style;
  }
}
</style>
