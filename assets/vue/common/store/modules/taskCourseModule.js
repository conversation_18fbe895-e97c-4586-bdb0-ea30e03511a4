import axios from "axios";
import {make} from "vuex-pathify";

const state = {
    tasks: [],
    refresh: false,
    loadingTask: false,
    historyDeliveryTaskStates: {},// Current available states
    allowedFileTypes: []
};

export default {
    namespaced: true,
    state,
    getters: {
        ...make.getters(state),
        getIsLoadingTask(state) {
            return state.loadingTask;
        },
        getHistoryDeliveryTaskStates(state) { return state.historyDeliveryTaskStates; },
        getAllowedFileTypes(state) { return state.allowedFileTypes; }
    },
    mutations: {
        ...make.mutations(state),
        SET_IS_LOADING_TASK(state, isLoading) {
            state.loadingTask = isLoading;
        },
        SET_HISTORY_DELIVERY_TASK_STATES(state, taskStates) {
            state.historyDeliveryTaskStates = taskStates;
        },
        SET_ALLOWED_FILE_TYPES(state, types) {
            state.allowedFileTypes = types;
        }
    },
    actions: {
        ...make.actions(state),
        async loadHistoryDeliveryTaskStates({ commit }) {
            const result = await axios.get('/admin/task-course/history-delivery-states');
            const { data } = result.data;
            commit('SET_HISTORY_DELIVERY_TASK_STATES', data);
        },

        async loadTaskCourse({commit}, id) {
            commit('SET_IS_LOADING_TASK', true);
            try {
                const url = `/admin/task-course/${id}`;
                const result = await axios.get(url);
                return result.data;
            } finally {
                commit('SET_IS_LOADING_TASK', false);
            }
        },

        async loadAllowedFileTypes({ commit }) {
            const result = await axios.get('/admin/task-courses/allowed-file-types');
            const { data } = result.data;
            commit('SET_ALLOWED_FILE_TYPES', data);
        },

        async loadTaskFiles({ commit }, id) {
            const url = `/admin/task-course/${id}/files`;
            const result = await axios.get(url);
            return result.data;
        },

        async uploadTaskCourseFiles({ commit }, { id, formData}) {
            const url = `/admin/task-course/${id}/upload-files`;
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const result = await axios.post(url, formData, { headers});
            return result.data;
        },

        async loadTaskCourseHistory({ commit }, {id, page = 1} ) {
            const url = `/admin/task-course/${ id }/history/${ page }`;
            const result = await axios.get(url);
            return result.data;
        },

        async loadTaskCourseHistoryFiles({ commit }, id) {
            const url = `/admin/task-course/history-delivery/${id}/files`;
            const result = await axios.get(url);
            return result.data;
        },

        async loadTaskCourseHistoryComments({ commit }, id) {
            const url = `/admin/task-course/history-delivery/${id}/comments`;
            const result = await axios.get(url);
            return result.data;
        },

        async updateHistoryDeliveryTaskState({ commit }, {id, state }) {
            const url = `/admin/history-delivery-task/${id}/state/${state}`;
            const result = await axios.put(url);
            return result.data;
        },

        async deleteFileTask({ commit }, id) {
            const result = await axios.delete(`/admin/file-task/${id}`);
            return result.data;
        },

        async deleteTaskCourse({ commit }, id) {
            const result = await axios.delete(`/admin/task-course/${id}`);
            const { error } = result.data;
            if (!error) commit('SET_REFRESH', true);
            return result.data;
        },

        async sendMessage({ commit }, { id, message }) {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const result = await axios.post(`/admin/task-course/history-delivery/${id}/comment`, message, { headers });
            return result.data;
        },

        async setFileTaskDownloadable({ commit }, { id, downloadable }) {
            const result = await axios.patch(`/admin/file-task/${id}/downloadable`, { downloadable });
            return result.data;
        },

        async setTaskCourseVisible({ commit }, { id, visible }) {
            const result = await axios.patch(`/admin/task-course/${id}/visible`, { visible });
            return result.data;
        }
    }
}
