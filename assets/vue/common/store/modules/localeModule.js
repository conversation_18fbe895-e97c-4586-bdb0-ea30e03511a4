import {make} from "vuex-pathify";

/**
 * Share common module for current app/user locale
 */
const state = {
    locales: [],
    defaultLocale: 'en',
    userLocale: undefined
};

const getters = {
    ...make.getters(state),
    getDefaultLocale(state) { return state.defaultLocale; },
    getLocales(state) { return state.locales; },
    getUserLocale(state) { return state.userLocale ?? state.defaultLocale; },
    getLanguagesOptions(state) { return Object.entries(state.locales).map(([id, name]) => ({ id, name }));},
};

export default {
    namespaced: true,
    state,
    getters,
    mutations: {
        SET_DEFAULT_LOCALE(state, locale) {
            state.defaultLocale = locale;
        },
        SET_LOCALES(state, locales) {
            state.locales = locales;
        },
        SET_USER_LOCALE(state, locale) {
            state.userLocale = locale;
        }
    },
    actions: {
        setDefaultLocale({ commit }, locale) {
            commit("SET_DEFAULT_LOCALE", locale);
        },
        setLocales({ commit }, locales) {
            commit("SET_LOCALES", locales);
        },
        setUserLocale({ commit }, locale) {
            commit("SET_USER_LOCALE", locale)
        }
    }
}
