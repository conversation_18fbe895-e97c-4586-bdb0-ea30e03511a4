/**
 * Read locale content from #app container
 * @param store
 * @param el
 * @param i18n Is required to update current selected locale otherwise the locale won't get updated
 */
export default (store, el, i18n = null) => {
    if (el.hasAttribute('locales')) {
        const locales = JSON.parse(el.attributes['locales'].value);
        store.dispatch('localeModule/setLocales', locales);
    }

    let selectedLocale = null;
    let defaultLocale = 'en';
    if (el.hasAttribute('default-locale')) {
        const locale = el.attributes['default-locale'].value;
        store.dispatch('localeModule/setDefaultLocale', locale);

        selectedLocale = locale;
        defaultLocale = locale;
    }
    if (el.hasAttribute('user-locale')) {
        const locale = el.attributes['user-locale'].value;
        store.dispatch('localeModule/setUserLocale', locale);

        selectedLocale = locale;
    }

    if (i18n) {
        i18n.locale = selectedLocale;
        i18n.fallbackLocale = defaultLocale;
    }
};
