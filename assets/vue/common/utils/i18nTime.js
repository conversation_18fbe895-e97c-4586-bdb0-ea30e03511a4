export function formatFromSeconds(timeInSeconds, i18n, full = false) {
    if (timeInSeconds < 60) return i18n.t('SECONDS', [timeInSeconds]);
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = timeInSeconds - (minutes * 60);
    if (minutes < 60) {
        if (!full || seconds === 0) return minutes > 1 ? i18n.t('MINUTES', [minutes]) : i18n.t('MINUTE', [minutes])
        return minutes > 1 ? i18n.t('MINUTES', [minutes]) : i18n.t('MINUTE', [minutes]) + ' ' + i18n.t('SECONDS', [timeInSeconds]);
    }
    const hour = Math.floor(minutes / 60);
    const extraMinutes = minutes - (hour * 60);
    if (!full || extraMinutes === 0) return hour > 1 ? i18n.t('HOURS', [hour]) : i18n.t('HOUR', [hour]);
    return (hour > 1 ? i18n.t('HOURS', [hour]) : i18n.t('HOUR', [hour])) + ' ' + (extraMinutes > 1 ? i18n.t('MINUTES', [extraMinutes]) : i18n.t('MINUTE', [extraMinutes]));
}
