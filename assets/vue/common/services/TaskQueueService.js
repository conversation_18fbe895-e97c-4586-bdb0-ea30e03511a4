/**
 * Service to handle task queue requests throughout the application
 * Provides a unified way to send task requests to the server and handle responses
 */
import axios from 'axios';

export default {
  /**
   * Default toast configuration
   */
  toastConfig: {
    position: 'bottom-right',
    duration: 5000,
    dismissible: true,
  },

  /**
   * Enqueues a task with the server
   * @param {Object} options - Configuration options for the task request
   * @param {string} options.url - The API endpoint to send the request to
   * @param {string} [options.method='POST'] - The HTTP method to use (GET, POST, etc)
   * @param {Object} [options.data={}] - The data to send with the request
   * @param {Object} [options.messages] - Custom messages for notifications
   * @param {string} [options.messages.success] - Success message (default: "Task successfully queued")
   * @param {string} [options.messages.error] - Error message (default: "Failed to queue task")
   * @param {Function} [options.onSuccess] - Callback function called on success
   * @param {Function} [options.onError] - Callback function called on error
   * @param {Object} [options.toast] - Vue Toast instance for notifications
   * @returns {Promise} - The axios request promise
   */
  enqueueTask(options) {
    const {
      url,
      method = 'POST',
      data = {},
      messages = {},
      onSuccess,
      onError,
      toast
    } = options;

    const defaultMessages = {
      success: "Task successfully queued",
      error: "Failed to queue task"
    };

    const taskMessages = {
      ...defaultMessages,
      ...messages
    };

    const requestConfig = {
      method,
      url,
      ...(method === 'GET' ? { params: data } : { data })
    };

    return axios(requestConfig)
      .then(response => {
        const successMessage = response.data.message || response.data.data.message || taskMessages.success;
        
        if (toast) {
          toast.success(successMessage, this.toastConfig);
        }
        
        if (onSuccess && typeof onSuccess === 'function') {
          onSuccess(response);
        }
        
        return response;
      })
      .catch(error => {
        let errorMessage = taskMessages.error;
        
        if (error.response) {
          const responseData = error.response.data;
          
          if (typeof responseData === 'string') {
            errorMessage = responseData;
          } else if (responseData.detail) {
            errorMessage = responseData.detail;
          } else if (responseData.message) {
            errorMessage = responseData.message;
          } else if (responseData.data) {
            errorMessage = responseData.data;
          }
          
          if (error.response.status === 403) {
            if (toast) {
              toast.warning(errorMessage, this.toastConfig);
            }
          } else {
            if (toast) {
              toast.error(errorMessage, this.toastConfig);
            }
          }
        } else {
          if (toast) {
            toast.error(errorMessage, this.toastConfig);
          }
        }
        
        console.error('Task queue error:', error);
        
        if (onError && typeof onError === 'function') {
          onError(error);
        }
        
        return {
          error: true,
          message: errorMessage,
          originalError: error,
          serverMessage: error.response && error.response.data ? 
              (typeof error.response.data === 'string' ? error.response.data : 
              (error.response.data.detail || error.response.data.message || error.response.data.data || JSON.stringify(error.response.data))) 
              : null
        };
      });
  }
};