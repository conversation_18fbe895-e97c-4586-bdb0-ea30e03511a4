<template>
  <div class="w-100">
    <div class="d-flex flex-column align-items-center justify-content-center" v-if="uploading">
      <spinner />
      <span>{{ $t('FILE_UPLOAD.UPLOADING') }}</span>
    </div>
    <form
        class="FormFileUploader"
        :id="id"
        action=""
        @submit.prevent
        v-show="!uploading"
    >
      <div class="FormFileUploader--type">
        <label class="selector-title">{{ $t('FILE_UPLOAD.FILE_TYPE') }}</label>
        <div class="selector-container">
          <button v-for="type in fileTypes"
                  :key="type.type" class="selector"
                  @click="setType(type)"
                  :class="selectedType?.type === type.type ? 'active' : ''"
          >
            <img :src="getIcon(type)" alt=""></img>
            <span>{{ type.name }}</span>
          </button>
        </div>
      </div>
      <div class="FormFileUploader--selector">
        <file-selector
            :title="title"
            :accept="selectedType?.accept"
            :multiple="selectedType?.multiple??false"
            :btn-selector-value="btnTitle"
            :name="inputFileName"
            preview-default-class="no-background-size"
            default-image="/assets/file_selector/add.svg"
            :apply-extra-actions-style="true"
            @input="$emit('input', $event)"
        >
          <template v-slot:extra-actions>
            <button v-if="showCancel" class="btn btn-default" @click="$emit('cancel')" type="button">{{ $t('CANCEL') }}</button>
            <button v-if="showSubmit && selectedType != null" class="btn btn-primary ml-1" @click="upload()" type="button">{{ $t('FILE_UPLOAD.UPLOAD_FILES') }}</button>
          </template>
        </file-selector>
      </div>
      <div class="FormFileUploader--content">
        <slot name="content"></slot>
      </div>
    </form>
  </div>
</template>

<script>
import FileSelector from "./FileSelector.vue";
import Spinner from "../../admin/components/base/Spinner.vue";

/**
 * @event on-type-selection
 */
export default {
  name: "FormFileUploader",
  components: {FileSelector, Spinner},
  props: {
    /**
     * The id of the form, useful when referencing the form using document.forms['form-id']
     */
    id: {
      type: String,
      default: 'form-file-uploader'
    },

    /**
     * Decide the allowed file type
     * @value Is required to have the structure
     * [
     *  {
     *    name: 'Name for displaying',
     *    accept: 'type_of_files_to_accept',
     *    type: 'logic type, useful when showing or hiding elements',
     *    multiple: true|false // Allow multiple file selection by normal input or drag & drop
     *   }
     *  ]
     */
    fileTypes: {
      type: Array|Object,
      default: [
        {
          name: 'Default',
          accept: '*/*',
          type: 'default',
          multiple: false
        }
      ]
    },

    uploading: {
      type: Boolean,
      default: false
    },

    title: {
      type: String,
      default: 'FILE_UPLOAD.SELECT_FILE'
    },

    btnTitle: {
      type: String,
      default: 'FILE_UPLOAD.SELECT_FILE'
    },

    showCancel: {
      type: Boolean,
      default: false
    },
    showSubmit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedType: null
    };
  },
  computed: {
    inputFileName() {
      return this.id + '-file';
    }
  },
  watch: {
    fileTypes: {
      immediate: true,
      deep: true,
      handler: function () {
        if (this.fileTypes.length === 1) this.setType(this.fileTypes[0]);
      }
    }
  },
  methods: {
    getIcon(type) {
      let value;
      if (isNaN(type.type)) {
        value = type.type.toUpperCase();
      } else {
        value = parseInt(type.type);
      }

      if (value === 1 || value === 'PDF') return '/assets/file_selector/pdf.svg';
      if (value === 2 || value === 'VIDEO') return '/assets/file_selector/video.svg';
      if (value === 3 || value === 'COMPRESSED') return '/assets/file_selector/compressed.svg';
      if (value === 4 || value === 'IMAGE') return '/assets/file_selector/image.svg';
      if (value === 5 || value === 'OFFICE') return '/assets/file_selector/office.svg';
      if (value === 6 || value === 'TXT') return '/assets/file_selector/txt.svg';
      if (value === 7 || value === 'AUDIO') return '';
      return '/assets/file_selector/pdf.svg'
    },
    setType(type) {
      this.selectedType = type;
      this.$emit('on-type-selection', type);
    },
    upload() {
      const form = document.forms[this.id];
      const formData = new FormData(form);
      formData.append('type', this.selectedType.type);

      const files = document.getElementById(this.inputFileName).files;
      if (files.length === 0) {
        this.$toast.error(this.$t('FILE_UPLOAD.NO_FILE_SELECTED') + '');
        return;
      } else if (files.length === 1) {
        formData.delete(this.inputFileName);
        formData.append('file', files[0]);
      } else {
        formData.delete('file');
        for (let i = 0; i < files.length; i++) {
          formData.append(`file_${i}`, files[i]);
        }
        formData.append('filesLength', files.length);
      }

      this.$emit('upload', formData);
    }
  }
}
</script>

 <style scoped lang="scss"> 
.FormFileUploader {
  &--type {
    @include boxed-selector;

    .selector-container {
      border: unset;
    }

    button.selector {
      width: 100px !important;
      height: 100px !important;

      img {
        width: 70px;
        height: 70px;
      }
    }
  }

  &--selector {
    width: 100%;
    .FileSelector {
      width: 100%;
      :deep(.FileSelector__preview) {
        width: 100%;
        z-index: 10;
      }
    }
  }
}
</style>
