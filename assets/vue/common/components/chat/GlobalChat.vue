<script>
import ChatGroup from "./ChatGroup.vue";
import ForumThreads from "../forum/ForumThreads.vue";
import ForumContent from "../forum/ForumContent.vue";
import GroupSelector from './GroupSelector.vue'
import { get } from 'vuex-pathify'

export default {
  name: "GlobalChat",
  components: { GroupSelector, ForumContent, ForumThreads, ChatGroup},
  props: {
    activeContainerId: {
      type: Number|String,
      default: null
    },
    groups: {
      type: Array,
      default: []
    },
    selectedGroupId: {
      type: Number,
      default: null
    },
    /**
     * Send/Read messages in this channel. Cannot add/remove users from the channel
     * {
     *   id: <value> (required)
     *   name: <value> (required)
     * }
     */
    publicChannel: {
      type: Object|Array,
      default: null
    },

    /**
     * Cannot send/read in this channel
     * {
     *   id: <value> (required)
     *   name: <value> (required)
     * }
     */
    chatChannelParent: {
      type: Object|Array,
      default: null
    },

    /**
     * Cannot send/read in this channel
     * {
     *   id: <value> (required)
     *   name: <value> (required)
     * }
     */
    groupChannelParent: {
      type: Object|Array,
      default: null
    },

    entityId: {
      type: String|Number,
      default: null
    },

    entityType: {
      type: String|Number,
      default: null
    },

    users: {
      type: Object|Array,
      default: () =>([])
    },
  },
  data() {
    return {
      userChannels: [],
      showUserChannelList: true,
      loadingUserChannels: true,
      groupChannels: [],
      loadingGroupChannels: true,

      messages: [],
      loadingMessages: false,
      usersInActiveChannel: [],

      channels: [],
      activeThread: null,
      intervalId: null,
      groupForm: null,

      showGroupConfiguration: false,
      groupConfiguration: {
        addUsers: false,
        participantsList: false
      }
    };
  },
  computed: {
    tutors: get('announcementModule/tutors'),
    availableUsers() {
      return this.users.filter(u => {
        const found = this.usersInActiveChannel.find(ua => ua.id === u.id);
        return found === undefined;
      });
    },

    mainThread() {
      return this.publicChannel ? [this.publicChannel] : []
    },
  },
  watch: {
    mainThread: {
      immediate: true,
      deep: true,
      handler: function () {
        this.activeThread = this.mainThread.length > 0 ? this.mainThread[0] : null;
      }
    },
    chatChannelParent: {
      immediate: true,
      deep: true,
      handler: function () {
        if (this.chatChannelParent != null) {
          this.loadingUserChannels = true;
          this.$store.dispatch('baseChatModule/getChannels', {
            channelId: this.chatChannelParent.id,
            cEntityId: this.entityId,
            cEntityType: this.entityType
          }).then(r => {
            const { error } = r;
            if (!error) this.userChannels = this.users.map((user, index) => ({
              name: user.name,
              id: user.channelId || `t${index}`,
              from: null,
              to: null,
              type: 'direct',
              hasChannel: !!user.channelId,
              userId: user.id,
              groupId: user.groupId
            }));
          }).finally(() => {
            this.loadingUserChannels = false;
          });
        }
      }
    },

    groupChannelParent: {
      immediate: true,
      deep: true,
      handler: function () {
        if (this.groupChannelParent != null) {
          this.loadingGroupChannels = true;
          this.$store.dispatch('baseChatModule/getChannels', {
            channelId: this.groupChannelParent.id
          }).then(r => {
            const { error, data } = r;
            if (!error) this.groupChannels = data;
          }).finally(() => {
            this.loadingGroupChannels = false;
          });
        }
      }
    },

    activeThread: {
      deep: true,
      handler: function (val, oldVal) {
        if (val?.id === oldVal?.id) return;
        this.messages = [];
        this.getMessages();
        this.getChannelUsers();
      }
    }
  },
  methods: {
    getMessages() {
      if (this.activeThread == null || isNaN(this.activeThread.id)) return;
      if (this.loadingMessages) return;
      this.loadingMessages = true;
      this.$store.dispatch('baseChatModule/getMessages', { channelId: this.activeThread.id }).then(r => {
        const { error, data } = r;
        if (!error) this.messages = data;
      }).finally(() => {
        this.loadingMessages = false;
      })
    },
    
    newGroup(parentId) {
      this.groupForm = {
        channelId: null,
        parentId,
        name: '',
      }
    },
    
    saveGroupData() {
      if (this.groupForm.channelId) this.updateGroup()
      else this.saveNewGroup()
    },
    
    saveNewGroup() {
      this.$store.dispatch('baseChatModule/saveNewChannel', this.groupForm).then(r => {
        const { error, data } = r;
        this.$toast.clear();
        if (error) this.$toast.error(data);
        else {
          this.$toast.success('saved');
          this.groupChannels.push(data);
          this.groupForm.channelId = data.id;
        }
      })
    },
    
    updateGroup() {
      this.$store.dispatch('baseChatModule/updateChat', this.groupForm).then(r => {
        const { error, data } = r;
        this.$toast.clear();
        if (error) this.$toast.error(data);
        else {
          this.groupChannels = this.groupChannels.map(gc => {
            const newGroupChannel = gc;
            if (gc.id === this.groupForm.channelId) { gc.name = this.groupForm.name; }
            return newGroupChannel;
          });
          this.groupForm = null;
        }
      });
    },
    
    getChannelUsers() {
      this.$store.dispatch('baseChatModule/getChannelUsers', { channelId: this.activeThread.id }).then(r => {
        const { error, data } = r;
        if (!error) this.usersInActiveChannel = data;
      })
    },
    addUserToGroup(channelId, userId, message = '') {
      this.$store.dispatch('baseChatModule/addUserToChannel', {
        channelId,
        userId
      }).then(r => {
        const { error } = r;
        if (error) console.log(error)
        else if (message) this.sendMessage(message)
      })
    },
    initDirectChat(message) {
      const found = this.tutors.find((tutor) => tutor.groupId === this.activeThread.groupId);
      if (!found) return;
      this.$store.dispatch('baseChatModule/saveNewChannel', {
        channelId: null,
        parentId: this.chatChannelParent.id,
        type: this.chatChannelParent.type,
        name: '',
      }).then(({ error, data }) => {
        this.activeThread.id = data.id;
        if (!error) {
          this.addUserToGroup(data.id, this.activeThread.userId);
          this.addUserToGroup(data.id, found.tutorId, message);
        }
      })
    },
    sendMessage(data) {
      if (data.message.length < 1) return;
      if (this.activeThread == null) return;
      if (isNaN(this.activeThread.id)) return this.initDirectChat(data);
      this.$toast.clear();
      this.$toast.info(this.$t('FORUM.INFO.SENDING_MESSAGE') + '');
      
      this.$store.dispatch('baseChatModule/sendMessage', {
        channelId: this.activeThread.id,
        ...data
      }).then(r => {
        const { data, error } = r;
        this.$toast.clear();
        if (error) this.$toast.error(this.$t('FORUM.ERROR.FAILED_TO_SEND_MESSAGE') + '');
        else {
          if (data.replyTo) {
            const index = this.messages.findIndex(m => `${ m.id }` === `${ data.replyTo }`);
            if (index >= 0) this.messages[index].replies.push(data);
          } else this.messages.push(data);
          this.$toast.info(this.$t('FORUM.INFO.MESSAGE_SEND_SUCCESS') + '');
        }
      })
    },
    removeChannel(payload) {
      this.$alertify.confirmWithTitle(
        this.$t('ANNOUNCEMENT.CONFIRM_THREAD_DELETE.TITLE'),
        this.$t('ANNOUNCEMENT.CONFIRM_THREAD_DELETE.THREAD_DESC'),
        () => {
          this.$store.dispatch('baseChatModule/removeChat', {
            parentId: this.groupChannelParent.id,
            channelId: payload.id
          })
            .then(() => {
              this.groupChannels = this.groupChannels.filter((gc) => gc.id !== payload.id)
              if (this.activeThread.id === payload.id) {
                this.activeThread = this.mainThread[0] || null
              }
            })
        })
    },
    updateChannel(payload) {
      this.groupForm = {
        name: payload.name,
        parentId: this.groupChannelParent.id,
        channelId: payload.id,
      }
    },
    toggleUserChannelList() {
      if(!this.userChannels.length) return;
      this.showUserChannelList = !this.showUserChannelList
    }
  }
}
</script>

<template>
  <div class="AnnouncementChat">
    <chat-group
      v-if="groupForm != null"
      :modal="true"
      :users="users"
      :group="groupForm"
      @close="groupForm = null"
      @save="saveGroupData()"/>
    <div class="GroupChat">
      <div class="GroupChat--left">
        <GroupSelector v-if="groups.length" :groups="groups" :selected-group-id="selectedGroupId"  @selectGroup="(id) => $emit('selectGroup', id)"/>
        <div v-if="mainThread.length > 0">
          <forum-threads
              :threads="mainThread"
              :show-actions="false"
              :use-local="false"
              :prop-active="activeThread"
              @active="activeThread = $event"/>
        </div>
        <div class="GroupChat--usersThreads" v-if="chatChannelParent != null">
          <div class="title">
            <span class="cursor-pointer" @click="toggleUserChannelList">{{ $t('ITINERARY.USER.LABEL_PLURAL') }}</span>
            <i
              v-show="userChannels.length"
              @click="toggleUserChannelList"
              class="fa cursor-pointer"
              :class="{'fa-caret-down': !showUserChannelList, 'fa-caret-up': showUserChannelList }"/>
          </div>
          <forum-threads
              v-show="showUserChannelList || !userChannels.length"
              :threads="userChannels"
              :show-edit="false"
              :use-local="false"
              :show-actions="false"
              :loading-threads="loadingUserChannels"
              :loading-messages="loadingMessages"
              :prop-active="activeThread"
              @active="activeThread = $event"/>
          <span v-show="!showUserChannelList && userChannels.length">...</span>
        </div>
        <div class="GroupChat--groupsThreads" v-if="groupChannelParent != null">
          <div class="title">
            <span>{{ $t('GLOBAL_CHAT.GROUP_TITLE') }}</span>
            <button
              class="btn btn-sm btn-primary"
              @click="newGroup(groupChannelParent.id)"
              :disabled="loadingMessages"
            >
              <i class="fa fa-plus mr-2"></i>{{ $t('NEW') }}
            </button>
          </div>
          <forum-threads
              :threads="groupChannels"
              :use-local="false"
              :prop-active="activeThread"
              :loading-messages="loadingMessages"
              :loading-threads="loadingGroupChannels"
              :show-create="false"
              @active="activeThread = $event"
              @create="newGroup(groupChannelParent.id)"
              @delete="removeChannel"
              @update="updateChannel"
          />
        </div>
      </div>
      <forum-content
          :thread="activeThread"
          :loading-messages="loadingMessages"
          v-model="messages"
          @reply="sendMessage"
          header-title="GLOBAL_CHAT.MESSAGES"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.AnnouncementChat {
  ul {
    list-style: none;
    padding: 0;
    &.UserList, &.ParticipantsList {
      padding: 0 0 0 .5rem;
      li {
        padding: 0.2rem 0.2rem;
        border: 1px solid var(--color-secondary);
        border-radius: 5px;
        margin-bottom: .3rem;

        &:hover {
          border-color: var(--color-primary);
        }
      }
    }
  }
  .GroupChat {
    width: 100%;
    & > * {
      width: 100%;
    }
    
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 1rem;
      margin-bottom: 1rem;
      user-select: none;
    }

    @media #{min-medium-screen()} {
      display: grid;
      grid-template-columns: 320px 1fr;
    }

    &--left {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      width: 100%;
      background-color: var(--color-primary-lightest);

      & > * {
        width: 100%;
      }
    }
    
    &--usersThreads {
      & > span {
        display: block;
        text-align: center;
        color: var(--color-neutral-mid);
        user-select: none;
      }
    }
  }

  .GroupConfiguration {
    position: relative;
    &--info {
      font-size: 20px;
      margin-right: 1rem;
      color: var(--color-primary);
      cursor: pointer;
    }

    &--content {
      background-color: #FFFFFF;
      position: absolute;
      width: 320px;
      max-height: 500px;
      overflow: auto;
      border: 1px solid #212121;
      border-radius: 5px;
      padding: 1rem;
      z-index: 10;
    }
  }
}
</style>
