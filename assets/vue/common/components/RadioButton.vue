<script>
export default {
  name: "RadioButton",
  props: {
    id: {
      type: String,
      required: true
    },

    name: {
      type: String,
      required: true
    },

    icon: {
      type: String,
      default: null
    },

    label: {
      type: String,
      default: 'Label'
    },

    radioValue: {
      type: String|Number|Boolean,
      required: true
    },

    disabled: {
      type: Boolean,
      default: false
    },

    value: null
  },
  computed: {
    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    }
  },
}
</script>

<template>
<div class="RadioButton">
  <input type="radio" :name="name" :id="id" :value="radioValue" v-model="innerValue" :disabled="disabled">
  <label :for="id">
    <i :class="icon" v-if="icon"></i> <span :class="icon ? 'with-icon' : ''">{{ label }}</span>
  </label>
</div>
</template>

<style scoped lang="scss">
.RadioButton {
  width: 100%;
  input {
    width: 0;
    height: 0;
    position: absolute;
    left: -9999px;
  }

  label > span.with-icon {
    margin-left: 1rem;
  }

  input + label {
    width: 100%;
    cursor: pointer;
    border-radius: 5px;
    margin: 0;
    padding: .5rem 1rem;
    box-sizing: border-box;
    position: relative;
    display: inline-block;
    border: solid 2px var(--color-neutral-mid-darker);
    color: var(--color-neutral-mid-darker);
    background-color: #ffffff;
    font-size: 1rem;
    text-align: center;
    box-shadow: 0 0 0 rgba(255, 255, 255, 0);
    transition: border-color .15s ease-out, color .25s ease-out, background-color .15s ease-out, box-shadow .15s ease-out;
  }

  input:hover + label {
    border-color: var(--color-primary);
  }

  input:checked + label {
    background-color: #ffffff;
    color: var(--color-primary);
    border-color: var(--color-primary);
    z-index: 1;
    font-weight: bold;
  }

  input:focus + label {
    outline: dotted 1px #cccccc;
  }


  &.no-width {
    width: unset;

    input + label {
      width: unset;
    }
  }
}
</style>
