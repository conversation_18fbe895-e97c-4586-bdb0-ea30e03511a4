<template>
  <div class="ForumFooter">
    <div class="gap-2 d-flex align-items-center justify-content-between w-100">
      <EmojiSelector @clicked="appendEmoji"/>
      <input
          type="text"
          class="form-control"
          v-model="message"
          @keydown.enter="sendMessage()"
          :placeholder="$t('FORUM.WRITE_COMMENT')"
      >
      <button class="btn btn-sm btn-primary sendButton" @click="sendMessage()">
        <i class="fa fa-paper-plane"></i>
      </button>
    </div>
  </div>
</template>

<script>
import EmojiSelector from "../../../announcement/components/details/emojiSelector";
export default {
  name: "ForumFooter",
  components: {EmojiSelector},
  props: {
    replyToMessage: null,
    clear: {
      type: Number|String,
      default: null
    }
  },
  data() {
    return {
      message: ''
    };
  },
  watch: {
    clear(newValue) {
      this.message = '';
      this.$emit('cancel-reply');
    }
  },
  methods: {
    sendMessage() {
      this.message = this.message.replace(/\s+/g, ' ').trim();
      if (this.message.length) {
        this.$emit('reply', {
          message: this.message,
          replyTo: this.replyToMessage?.id ?? null
        });
        this.message = '';
      }
    },
    appendEmoji(value) {
      this.message += value
    }
  }
}
</script>

 <style scoped lang="scss"> 
.ForumFooter {

  display: flex;
  flex-flow: row nowrap;
  padding: 2rem 1rem;
  background-color: $forums-active-thread-footer-bg-color;
  label {
    display: flex;
    flex-flow: column;
    flex-grow: 1;
    margin-right: 1rem;

    label {
      color: $forum-footer-text-color;
    }

    .replying-to {
      @include mixin-post-reply;
    }

    input {
      @include mixin-forum-input;
    }
  }
  button.sendButton {
    @include mixin-forum-button;
    width: 40px !important;
    height: 40px !important;
    margin-top: auto;
    background-color: #019DDF !important;
    color: #FFFFFF !important;
    &:hover {
      color: #FFFFFF !important;
    }
  }
}
</style>
