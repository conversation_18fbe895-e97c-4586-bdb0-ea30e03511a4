<template>
  <div class="ForumThreads">
    <button
        class="btn btn-primary ml-auto btn-sm mb-3"
        @click="emitCreate"
        v-if="showActions && showCreate"
        :disabled="loadingMessages"
    >
      <i class="fa fa-plus mr-2"></i>{{ $t('NEW') }}
    </button>
    <div class="w-100 d-flex flex-row align-items-center justify-content-center" v-if="loadingThreads">
      <spinner />
    </div>
    <div class="threads" v-else>
      <DataNotFound text="No hay hilos disponibles" :hide-on="!threads.length"/>
      <span
          class="ForumThreads--thread line-break-anywhere"
          v-for="(thread, index) in threads"
          :key="'thread' + thread.id + '_i_' + index"
          :class="activeThread?.id === thread.id ? 'active' : ''"
          @click="setActive(thread)"
      >
      # {{ thread.name }}
      <span class="thread-actions" v-if="showActions && (showDelete || showEdit)">
        <button
            type="button"
            class="btn btn-primary btn-sm"
            @click.stop="emitUpdate(index)"
            :disabled="loadingMessages"
            v-if="showEdit"
        >
          <i class="fa fa-pencil"></i>
        </button>
        <button
            type="button"
            class="btn btn-danger btn-sm"
            @click.stop="emitDelete(index)"
            :disabled="loadingMessages"
            v-if="showDelete"
        >
          <i class="fa fa-trash"></i>
        </button>
      </span>
    </span>
    </div>
  </div>
</template>

<script>
import DataNotFound from "../../../announcement/components/details/DataNotFound";
import Spinner from "../../../admin/components/base/Spinner.vue";
export default {
  name : "ForumThreads",
  components: {Spinner, DataNotFound},
  props: {
    loadingMessages: {
      type: Boolean,
      default: false
    },
    loadingThreads: {
      type: Boolean,
      default: false
    },
    threads    : {
      type   : Object | Array,
      default: () => [],
    },
    showActions: {
      type   : Boolean,
      default: true,
    },
    showCreate: {
      type   : Boolean,
      default: true,
    },
    showDelete: {
      type   : Boolean,
      default: true,
    },
    showEdit: {
      type   : Boolean,
      default: true,
    },
    propActive: {
      type: Object|Array,
      default: null
    },
    useLocal: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      active: null,
      isUpdate: false,
    };
  },
  computed: {
    activeThread() {
      return this.useLocal ? this.active : this.propActive;
    }
  },
  watch  : {
    threads: {
      handler  : function () {
        if (this.threads.length && this.useLocal) {
          this.active = this.threads[0];
          this.setActive(this.threads[0]);
        }
      },
      immediate: true,
    },
  },
  methods: {
    setActive(active) {
      if (!this.loadingMessages) {
        this.active = active;
        this.$store.dispatch('forumModule/setSelectedForumThread', active);
        this.$emit("active", active);
      }
    },
    emitCreate() {
      if (!this.loadingMessages) {
        this.$emit('create')
      }
    },
    emitUpdate(index) {
      if (!this.loadingMessages) {
        this.$emit('update', this.threads[index])
      }
    },
    emitDelete(index) {
      if (!this.loadingMessages) {
        this.$emit('delete', this.threads[index])
      }
    }
  },
};
</script>

 <style scoped lang="scss"> 
:deep(.loader) {
  font-size: 45px;
}
.ForumThreads {
  background-color: $forum-thread-background-color;
  display: flex;
  flex-direction: column;
  padding: 0 1rem;
  gap: 1rem;

  &--thread {
    color: $forum-threads-text-color;
    font-size: 1rem;
    cursor: pointer;
    margin: 0.25rem;
    border-radius: 5px;
    padding: 5px 10px;
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: flex-start;
    background-color: #ffffff;
    border: 1px solid $forum-threads-text-color;

    &.active {
      color: $forum-threads-active-text-color;
      border-color: $forum-threads-active-text-color;
    }

    .thread-actions {
      margin-left: auto;
      display: flex;
      flex-flow: row nowrap;

      button {
        margin: 0.15rem;
      }
    }

    &:hover {
      background-color: #ffffff;
    }
  }

  .threads {
    display: flex;
    flex-direction: column;

    @media (max-width: 950px) {
      max-height: 195px;
      overflow-y: auto;
    }
  }
}
</style>
