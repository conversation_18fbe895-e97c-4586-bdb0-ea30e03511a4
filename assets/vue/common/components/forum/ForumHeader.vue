<template>
  <div class="ForumHeader">
    <slot name="left-custom-content"></slot>
    <span class="title line-break-anywhere">{{ name }}</span>
    <span class="total--comments"><i class="fas fa-comment-alt"></i> {{ totalItems }} {{ $t(headerTitle) }}</span>
  </div>
</template>

<script>
export default {
  name: "ForumHeader",
  props: {
    headerTitle: {
      type: String,
      default: 'COMMENTS'
    },
    thread: {
      type: Object|Array,
      default: () => ({})
    },
    totalItems: {
      type: Number,
      default: 0
    },
    showActions: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    name() {
      return (this.thread?.name || '').length ? `# ${this.thread.name}` : '';
    }
  }
}
</script>

 <style scoped lang="scss"> 
.ForumHeader {
  padding: 1rem 1rem 1rem 3rem;
  background-color: $forum-header-bg-color;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: flex-start;
  .title {
    color: $forum-header-text-color;
    font-size: 18px;
    font-weight: bold;
  }

  .total--comments {
    position: relative;
    padding-left: 1rem;
    color: $forum-header-text-color;
    margin-left: auto;
    i {
      color: #019DDF;
    }
  }
  .forum--actions {
    margin-left: auto;
    height: 25px;
    button {
      @include mixin-forum-button;
    }
  }
}
</style>
