<template>
  <div class="ForumSendMessage">
    <div>
      <label>
        {{ $t('FORUM.WRITE_COMMENT') }}
      </label>
      <div v-if="replyToMessage" class="replying-to">
        <span class="--title">{{ $t('FORUM.REPLY_TO') }}:</span> <span>{{ replyToMessage.message }}</span><button class="--button-close" @click="$emit('cancel-reply')"><i class="fa fa-times-circle"></i></button>
      </div>
      <input type="text" v-model="message" @keydown.enter="sendMessage()">
    </div>
    <button class="send" type="button" @click="sendMessage()"><i class="fa fa-chevron-right"></i></button>
  </div>
</template>

<script>
export default {
  name: "ForumSendMessage",
  props: {
    replyToMessage: null,
    clear: {
      type: Number|String,
      default: null
    }
  },
  data() {
    return {
      message: ''
    };
  },
  watch: {
    clear(newValue) {
      this.message = '';
      this.$emit('cancel-reply');
    }
  },
  methods: {
    sendMessage() {
      this.$emit('send-message', {
        message: this.message,
        replyTo: this.replyToMessage?.id ?? null
      })
    }
  }
}
</script>

 <style scoped lang="scss"> 
.ForumSendMessage {

  display: flex;
  flex-flow: row nowrap;
  padding: 2rem 1rem;
  background-color: $forums-active-thread-footer-bg-color;
  div {
    display: flex;
    flex-flow: column;
    flex-grow: 1;
    margin-right: 1rem;

    label {
      color: #828282;
    }

    .replying-to {
      @include mixin-post-reply;
    }

    input {
      @include mixin-forum-input;
    }
  }
  button.send {
    @include mixin-forum-button;
    width: 40px !important;
    height: 40px !important;
    margin-top: auto;
    background-color: #019DDF !important;
    color: #FFFFFF !important;
    &:hover {
      color: #FFFFFF !important;
    }
  }
}
</style>
