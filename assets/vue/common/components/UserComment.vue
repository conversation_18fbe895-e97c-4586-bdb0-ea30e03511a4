<template>
  <div class="UserComment" :class="applyVisibility ? 'enable-visibility' : ''">
    <div class="UserComment__user_avatar" :style="{
      'background-image': `url(/uploads/users/avatars/${comment.avatar ?? 'default.svg'})`
    }">
    </div>
    <div class="UserComment__content">
      <div class="UserComment__content__header">
        <h1>{{ comment.firstName + ' ' + comment.lastName }}</h1>
        <div class="rating" v-if="showRating">
          <span class="fa fa-star" v-for="index in 5" :class="index <= comment.rating && comment.rating > 0 ? 'active' : ''"></span>
        </div>
      </div>
      <p class="comment-value" :class="applyVisibility ? (comment?.visible ? 'status-visible' : 'status-hidden') : 'status-visible'">
        {{ comment.comment }}
      </p>

      <div class="UserComment__content__footer" :class="applyVisibility ? (comment?.visible ? 'status-visible' : 'status-hidden') : 'status-visible'">
        {{ comment.createdAtText }}
      </div>
    </div>
    <div class="UserComment__actions" v-if="applyVisibility">
      <div class="custom-control custom-switch">
        <input type="checkbox" class="custom-control-input" :id="'switch_comment_' + comment.id" v-model="comment.visible" @change="changeCommentVisibility()">
        <label class="custom-control-label" :for="'switch_comment_' + comment.id"></label>
      </div>
      <label>{{ $t('USER_COMMENT.VISIBLE') }}</label>
    </div>
  </div>
</template>

<script>
export default {
  name: "UserComment",
  props: {
    comment: null,
    applyVisibility: {
      type: Boolean,
      default: false
    },
    showRating: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    changeCommentVisibility() {
      if (this.comment) this.$emit('on-visibility-change', this.comment.visible);
    }
  }
}
</script>

 <style scoped lang="scss"> 
.UserComment {
  background-color: #F1FBFF;
  border-radius: 14px;
  padding: 1rem;
  display: grid;
  margin-top: 0.5rem;

  grid-template-columns: [avatar] 90px [content] auto;
  &.enable-visibility {
    grid-template-columns: [avatar] 90px [content] auto [actions] 70px;
  }

  &__user_avatar {
    width: 80px;
    height: 80px;
    background-color: #d8eef8;
    border-radius: 50%;
    margin: auto;
    background-position: center center;
    background-size: cover;
  }

  &__content {
    flex-grow: 1;
    margin-left: 1rem;
    height: 100%;
    &__header {
      display: flex;
      flex-flow: row nowrap;
      width: 100%;

      h1 {
        font-size: 20px;
        font-weight: bold;
        color: #212529;
        width: 30%;
      }

      .rating {
        background-color: #D9F0FA;
        border-radius: 6px;
        padding: 0.25rem 1rem;

        .fa-star {
          color: #212529;
        }
        .fa-star.active, .fa-star-half.active {
          color: #009BDB;
        }
      }
    }

    p.comment-value {
      font-size: 17px;
      margin-top: 0.5rem;
    }
    &__footer {
      font-size: 14px;
    }

    p.comment-value.status-visible, &__footer.status-visible {
      color: #808080;
    }

    p.comment-value.status-hidden, &__footer.status-hidden {
      color: #CBD5E1;
    }
  }

  &__actions {
    margin: auto;
    display: flex;
    flex-flow: column;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    color: #212529;
  }
}
</style>
