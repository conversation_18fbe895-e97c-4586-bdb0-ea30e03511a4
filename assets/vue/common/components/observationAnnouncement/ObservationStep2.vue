<template>
  <div class="ObservationStep2 d-flex flex-row flex-wrap">
    <div class="col-4 form-group">
      <label for="providerCost">{{
        $t("ANNOUNCEMENT_OBSERVATION.PROVIDER_COST")
      }}</label>
      <input
        type="number"
        class="form-control"
        name="providerCost"
        id="providerCost"
        v-model="providerCost"
      />
    </div>
    <div class="col-4 form-group">
      <label for="hedimaManagementCost">{{
        $t("ANNOUNCEMENT_OBSERVATION.HEDIMA_MANAGEMENT_COST")
      }}</label>
      <input
        type="number"
        class="form-control"
        name="hedimaManagementCost"
        id="hedimaManagementCost"
        v-model="hedimaManagementCost"
      />
    </div>
    <div class="col-4 form-group">
      <label for="travelAndMaintenanceCost">{{
        $t("ANNOUNCEMENT_OBSERVATION.TRAVEL_AND_MAINTENANCE_COST")
      }}</label>
      <input
        type="number"
        class="form-control"
        name="travelAndMaintenanceCost"
        id="travelAndMaintenanceCost"
        v-model="travelAndMaintenanceCost"
      />
    </div>
    <div class="col-4 form-group">
      <label for="totalCost">{{
        $t("ANNOUNCEMENT_OBSERVATION.TOTAL_COST")
      }}</label>
      <input
        type="number"
        class="form-control"
        name="totalCost"
        id="totalCost"
        v-model="totalCost"
      />
    </div>
    <div class="col-4 form-group">
      <label for="finalPax">{{
        $t("ANNOUNCEMENT_OBSERVATION.FINAL_PAX")
      }}</label>
      <input
        type="number"
        class="form-control"
        name="finalPax"
        id="finalPax"
        v-model="finalPax"
      />
    </div>
    <div class="col-4 form-group">
      <label for="maximumBonus">{{
        $t("ANNOUNCEMENT_OBSERVATION.MAXIMUM_BONUS")
      }}</label>
      <input
        type="number"
        class="form-control"
        name="maximumBonus"
        id="maximumBonus"
        v-model="maximumBonus"
      />
    </div>
    <div class="col-4 form-group">
      <label for="subsidizedAmount">{{
        $t("ANNOUNCEMENT_OBSERVATION.SUBSIDIZED_AMOUNT")
      }}</label>
      <input
        type="number"
        class="form-control"
        name="subsidizedAmount"
        id="subsidizedAmount"
        v-model="subsidizedAmount"
      />
    </div>
    <div class="col-4 form-group">
      <label for="privateAmount">{{
        $t("ANNOUNCEMENT_OBSERVATION.PRIVATE_AMOUNT")
      }}</label>
      <input
        type="number"
        class="form-control"
        name="privateAmount"
        id="privateAmount"
        v-model="privateAmount"
      />
    </div>
    <div class="col-4 form-group">
      <label for="providerInvoiceNumber">{{
        $t("ANNOUNCEMENT_OBSERVATION.PROVIDER_INVOICE_NUMBER")
      }}</label>
      <input
        type="text"
        class="form-control"
        name="providerInvoiceNumber"
        id="providerInvoiceNumber"
        v-model="providerInvoiceNumber"
        :placeholder="$t('ANNOUNCEMENT_OBSERVATION.PROVIDER_INVOICE_NUMBER')"
      />
    </div>
    <div class="col-4 form-group">
      <label for="hedimaManagementInvoiceNumber">{{
        $t("ANNOUNCEMENT_OBSERVATION.HEDIMA_MANAGEMENT_INVOICE_NUMBER")
      }}</label>
      <input
        type="text"
        class="form-control"
        name="hedimaManagementInvoiceNumber"
        id="hedimaManagementInvoiceNumber"
        v-model="hedimaManagementInvoiceNumber"
        :placeholder="
          $t('ANNOUNCEMENT_OBSERVATION.HEDIMA_MANAGEMENT_INVOICE_NUMBER')
        "
      />
    </div>
    <div class="col-4 form-group">
      <label for="invoiceStatus">{{
        $t("ANNOUNCEMENT_OBSERVATION.INVOICE_STATUS")
      }}</label>

      <select
        class="form-select"
        v-model="invoiceStatus"
        name="invoiceStatus"
        id="invoiceStatus"
      >
        <option value="">
          {{ $t("SELECT") }}
        </option>
        <option v-for="status in 3" :value="status" :key="status">
          {{ $t(`OBSERVATION_INVOICE_STATUS.${status}`) }}
        </option>
      </select>
    </div>
  </div>
</template>

<script>
export default {
  name: "ObservationStep2",
  props: {
    observation: null,
  },
  data() {
    return {
      providerCost: this.observation?.providerCost ?? 0,
      hedimaManagementCost: this.observation?.hedimaManagementCost ?? 0,
      travelAndMaintenanceCost: this.observation?.travelAndMaintenanceCost ?? 0,
      totalCost: this.observation?.totalCost ?? 0,
      finalPax: this.observation?.finalPax ?? 0,
      maximumBonus: this.observation?.maximumBonus ?? 0,
      subsidizedAmount: this.observation?.subsidizedAmount ?? 0,
      privateAmount: this.observation?.privateAmount ?? 0,
      providerInvoiceNumber: this.observation?.providerInvoiceNumber ?? "",
      hedimaManagementInvoiceNumber:
        this.observation?.hedimaManagementInvoiceNumber ?? "",
      invoiceStatus: this.observation?.invoiceStatus ?? "",
    };
  },
  watch: {
    observation(newValue) {
      this.providerCost = newValue?.providerCost ?? 0;
      this.hedimaManagementCost = newValue?.hedimaManagementCost ?? 0;
      this.travelAndMaintenanceCost = newValue?.travelAndMaintenanceCost ?? 0;
      this.totalCost = newValue?.totalCost ?? 0;
      this.finalPax = newValue?.finalPax ?? 0;
      this.maximumBonus = newValue?.maximumBonus ?? 0;
      this.subsidizedAmount = newValue?.subsidizedAmount ?? 0;
      this.privateAmount = newValue?.privateAmount ?? 0;
      this.providerInvoiceNumber = newValue?.providerInvoiceNumber ?? "";
      this.hedimaManagementInvoiceNumber =
        newValue?.hedimaManagementInvoiceNumber ?? "";
      this.invoiceStatus = newValue?.invoiceStatus ?? "";
    },
  },
};
</script>

 <style scoped lang="scss"> 
</style>
