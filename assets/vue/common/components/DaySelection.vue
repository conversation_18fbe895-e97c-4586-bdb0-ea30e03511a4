<script>


export default {
  name: "DaySelection",
  props: {
    value: {
      type: Object,
      default: () => ({
        l: false,
        m: false,
        x: false,
        j: false,
        v: false,
        s: false,
        d: false
      })
    }
  },
  computed: {
    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    }
  },
  methods: {
    activate(day) {
      const v = structuredClone(this.innerValue);
      if (`${day}` in v) {
        v[`${day}`] = !v[`${day}`];
      } else {
        v[`${day}`] = true;
      }
      this.innerValue = v;
    }
  }
}
</script>

<template>
  <div class="DaySelection">
    <div class="DaySelection--title">
      <label>Dias asignados a la convocatoria</label> <button type="button"><i class="fa fa-question"></i></button>
    </div>
    <div class="DaySelection--days">
      <div class="day" :class="innerValue['l'] ? 'active' : ''">
        <button type="button" @click="activate('l')"><span>L</span></button>
      </div>
      <div class="day" :class="innerValue['m'] ? 'active' : ''">
        <button type="button" @click="activate('m')"><span>M</span></button>
      </div>
      <div class="day" :class="innerValue['x'] ? 'active' : ''">
        <button type="button" @click="activate('x')"><span>X</span></button>
      </div>
      <div class="day" :class="innerValue['j'] ? 'active' : ''">
        <button type="button" @click="activate('j')"><span>J</span></button>
      </div>
      <div class="day" :class="innerValue['v'] ? 'active' : ''">
        <button type="button" @click="activate('v')"><span>V</span></button>
      </div>
      <div class="day" :class="innerValue['s'] ? 'active' : ''">
        <button type="button" @click="activate('s')"><span>S</span></button>
      </div>
      <div class="day" :class="innerValue['d'] ? 'active' : ''">
        <button type="button" @click="activate('d')"><span>D</span></button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.DaySelection {
  &--title {
    button {
      width: 24px;
      height: 24px;
      background-color: var(--color-primary);
      color: var(--color-neutral-lighter);
      border: none;
      border-radius: 50%;
      font-size: 12px;
      &:hover {
        outline: none;
      }
    }
  }

  &--days {
    display: grid;
    gap: 0.5rem;
    grid-template-columns: repeat(7, 35px);
    padding: 0.25rem 1rem;
    border: 1px solid var(--color-neutral-mid-darker);
    border-radius: 5px;

    .day {
      button {
        background-color: var(--color-neutral-light);
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: var(--color-neutral-mid-darker);
        cursor: pointer;
        font-size: 16px;

        border: none;
        &:hover {
          border: none;
        }
      }

      &.active {
        button {
          background-color: var(--color-primary);
          color: var(--color-neutral-lighter);
        }
      }
    }
  }
}
</style>
