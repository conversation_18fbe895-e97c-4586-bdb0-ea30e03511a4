<template>
  <div class="d-flex align-items-center justify-content-center" v-if="isLoadingTask">
    <loader :is-loaded="isLoadingTask"/>
  </div>
  <home v-else
        :title="task?.title"
        :description="task?.description"
        :use-i18n="false"
        src-thumbnail="/assets/imgs/task_course.svg"
  >
    <template v-slot:content-main>
      <div class="TaskCourse--content">
        <ul class="nav nav-tabs">
          <li class="nav-item" role="presentation">
            <button class="nav-link" :class="activePane === 'files' ? 'active' : ''" id="files-tab"
                    @click="activePane = 'files'">
              <i class="fa fa-files-o"></i> {{ $t('TASK_COURSE.FILES') }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" :class="activePane === 'history' ? 'active' : ''" id="history-tab"
                    @click="activePane = 'history'">
              <i class="fa fa-history"></i> {{ $t('TASK_COURSE.HISTORY') }}
            </button>
          </li>
        </ul>

        <div class="tab-content">
          <div class="tab-pane fade files" :class="activePane === 'files' ? 'active show' : ''" id="files" role="tabpanel"
               aria-labelledby="files-tab">
            <task-course-files :task-course-id="$route.params.id" />
          </div>
          <div class="tab-pane fade" :class="activePane === 'history' ? 'active show' : ''" id="history" role="tabpanel"
               aria-labelledby="history-tab">
            <task-course-history :task-course-id="$route.params.id"></task-course-history>
          </div>
        </div>
      </div>
    </template>
  </home>
<!--  <div class="TaskCourse" v-else>-->
<!--    <div class="TaskCourse&#45;&#45;header">-->
<!--      <div class="TaskCourse&#45;&#45;header&#45;&#45;content">-->
<!--        <h1>{{ task?.title }}</h1>-->
<!--        <p v-html="task?.description"/>-->
<!--      </div>-->
<!--      <div class="TaskCourse&#45;&#45;header&#45;&#45;banner">-->
<!--        <img src="/assets/imgs/library_home.svg" alt="">-->
<!--      </div>-->
<!--    </div>-->
<!--    <div class="TaskCourse&#45;&#45;content">-->
<!--      <ul class="nav nav-tabs">-->
<!--        <li class="nav-item" role="presentation">-->
<!--          <button class="nav-link" :class="activePane === 'files' ? 'active' : ''" id="files-tab"-->
<!--                  @click="activePane = 'files'">-->
<!--            <i class="fa fa-files-o"></i> {{ $t('TASK_COURSE.FILES') }}-->
<!--          </button>-->
<!--        </li>-->
<!--        <li class="nav-item" role="presentation">-->
<!--          <button class="nav-link" :class="activePane === 'history' ? 'active' : ''" id="history-tab"-->
<!--                  @click="activePane = 'history'">-->
<!--            <i class="fa fa-history"></i> {{ $t('TASK_COURSE.HISTORY') }}-->
<!--          </button>-->
<!--        </li>-->
<!--      </ul>-->

<!--      <div class="tab-content">-->
<!--        <div class="tab-pane fade files" :class="activePane === 'files' ? 'active show' : ''" id="files" role="tabpanel"-->
<!--             aria-labelledby="files-tab">-->
<!--          <task-course-files :task-course-id="$route.params.id" />-->
<!--        </div>-->
<!--        <div class="tab-pane fade" :class="activePane === 'history' ? 'active show' : ''" id="history" role="tabpanel"-->
<!--             aria-labelledby="history-tab">-->
<!--          <task-course-history :task-course-id="$route.params.id"></task-course-history>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
</template>

<script>
import Loader from "../../../admin/components/Loader.vue";
import TaskCourseHistory from "./TaskCourseHistory.vue";
import TaskCourseFiles from "./TaskCourseFiles.vue";
import Home from "../../../base/Home.vue";

export default {
  name: "TaskCourse",
  components: {Home, TaskCourseFiles, TaskCourseHistory, Loader},
  data() {
    return {
      task: undefined,
      activePane: 'files',
      previousRoute: null
    };
  },
  computed: {
    isLoadingTask() {
      return this.$store.getters['taskCourseModule/getIsLoadingTask'];
    },
    useGlobalEventBus() {
      return this.$store.getters['contentTitleModule/getUseGlobalEventBus'];
    }
  },
  created() {
    this.$store.dispatch('taskCourseModule/loadHistoryDeliveryTaskStates');
    this.handleRouteParams();
  },
  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('onEditTask', () => {
        // Pass current route params, to handle different posible starting points
        this.$router.push({ name: 'UpdateTaskCourse', params: this.$route.params})
      });
      this.$eventBus.$on('onDeleteTask', () => {
        this.deleteTaskCourse();
      });
    }
  },
  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('onEditTask');
      this.$eventBus.$off('onDeleteTask');
    }
  },
  watch: {
    $route(to, from) {
      this.previousRoute = from;
    }
  },
  methods: {
    async handleRouteParams() {
      this.$store.dispatch('contentTitleModule/setActions', {
        route: this.$route.name, actions: [
          {
            name: this.$t('DELETE'),
            event: 'onDeleteTask',
            class: 'btn btn-danger',
            icon: 'fa fa-trash'
          }, {
            name: this.$t('EDIT'),
            event: 'onEditTask',
            class: 'btn btn-info',
            icon: 'fa fa-pencil'
          },
        ]
      });

      const result = await this.$store.dispatch('taskCourseModule/loadTaskCourse', this.$route.params.id);
      const {data, error} = result;
      if (error) {
        this.$toast.error(this.$t(data) + '');
        this.goToParent();
        return;
      }
      this.task = data;
    },

    deleteTaskCourse() {
      this.$alertify.confirmWithTitle(
          this.$t('TASK_COURSE.DELETE.CONFIRM.TITLE'),
          this.$t('TASK_COURSE.DELETE.CONFIRM.DESCRIPTION'),
          () => {
            this.$store.dispatch('taskCourseModule/deleteTaskCourse', this.$route.params.id).then(res => {
              const { error } = res;
              if (error) this.$toast.error(this.$t('TASK_COURSE.DELETE.FAILED') + '')
              else {
                this.$toast.success(this.$t('TASK_COURSE.DELETE.SUCCESS') + '')
                this.goToParent();
              }
            })
          },
          () => {},
      );
    },
    goToParent() {
      this.$store.dispatch('contentTitleModule/removeRouteFromContentTitle', this.$route.name);
      this.$store.dispatch('routerModule/setDeleteLastRoute', true);
      const toRoute = this.$route.params.parentName ?? 'Home';
      this.$router.replace({ name: toRoute , params: { id: this.$route.params.parentId }});
    }
  }
}
</script>

 <style scoped lang="scss"> 
.TaskCourse {
  //&--header {
  //  padding: 2rem 5rem 3rem 5rem;
  //  background: #FFFFFF;
  //  @media #{small-screen()} {
  //    display: flex;
  //    flex-flow: row wrap;
  //  }
  //
  //  @media #{min-small-screen()} {
  //    display: grid;
  //    grid-template-columns: auto 300px;
  //  }
  //
  //  &--content {
  //    width: 100%;
  //
  //    h1 {
  //      font-size: 22px;
  //      color: #1E293B;
  //    }
  //
  //    p {
  //      margin-top: 1rem;
  //      font-size: 17px;
  //      color: #808080;
  //    }
  //  }
  //
  //  &--banner {
  //    width: 100%;
  //    position: relative;
  //
  //    img {
  //      width: 100%;
  //      position: absolute;
  //      top: -2rem;
  //    }
  //  }
  //}

  &--content {
    @include nav-bar-style;
    padding: 0 1rem;
    //.tab-pane {
    //  background-color: #F6F7F8;
    //  padding: 2rem 5rem 2rem 5rem;
    //}
  }
}
</style>
