import $ from 'jquery';
import 'bootstrap';

import Vue from 'vue';
import VueToast from "vue-toast-notification";
import VueAlertify from "vue-alertify";
import 'vue-toast-notification/dist/theme-sugar.css';

import initI18n from "../common/i18n";
import initConfig from "../common/utils/initConfig";
import { setActiveRoute, setBrowserHistory, setBrowserStatus, initRoute, goBack } from "./utils/checkStarterRoute";

import App from '../common/App.vue';
import ContentTitle from "../common/components/ContentTitle.vue";
import PageActions from "../common/components/PageActions.vue";
import {beforeDestroy, beforeMount, computed, created, mounted} from "./sharedItinitialConfig";
import enableSearchQueryEvent from "../common/utils/enableSearchQueryEvent";


import * as Highcharts from "highcharts";
import Stock from "highcharts/modules/stock";
Stock(Highcharts);
import HighchartsVue from "highcharts-vue";


Vue.use(HighchartsVue);
Vue.use(VueToast);

Vue.prototype.$eventBus = new Vue();// Global event bus

import '../registerBaseComponents';
import initAuthVerification from "./utils/auth";

import "../base/directives";
import "./assets/mainApp.scss";

const auth = initAuthVerification();

Vue.directive('click-outside', {
    bind: function (el, binding, vnode) {
        el.event = (event) => {
            if (!(el === event.target || el.contains(event.target))) {
                vnode.context[binding.expression](event)
            }
        };
        document.body.addEventListener('click', el.event);
    },
    unbind: function (el) {
        if (el.event) document.body.removeEventListener('click', el.event);
    }
})

export function getI18n(store) {
    return initI18n(store);
}

function initVueApp(
    {
        store,
        router,
        i18n,
        paramComputed = {},
        paramWatch = {},
        paramCreated = () => {},
        paramBeforeMount = () => {},
        paramMounted = () => {},
        paramBeforeDestroy = () => {},
        appMainTitle = null
    }
) {
    return new Vue({
        delimiters: ['${', '}'],
        components: { App, ContentTitle, PageActions },
        store,
        $,
        i18n,
        router,
        auth,

        computed: {
            ...computed,
            ...paramComputed
        },

        watch: {
            ...paramWatch,
            $route(to, from) {
                setActiveRoute(to);
                if (from.name != null)
                    this.$store.dispatch('routerModule/addRouteToHistory', from);
            },
            getContentTitle(newValue) {
                setBrowserStatus(this.getContentTitle);
            },
            history(newValue) {
                setBrowserHistory(this.history);
            }
        },

        created() {
            created(this);
            paramCreated();
        },

        beforeMount() {
            initConfig(this.$store, this.$el);
            beforeMount(this);
            enableSearchQueryEvent(this.$eventBus);
            paramBeforeMount();

            // Init alertify, fail when i18n is called directly in component initialization
            const alertifyOk = i18n.t('ALERTIFY.OK');
            const alertifyCancel = i18n.t('ALERTIFY.CANCEL');
            Vue.use(VueAlertify, {
                closable: false,
                movable: false,
                glossary: {
                    title: appMainTitle ?? 'AlertifyJS',
                    ok: alertifyOk,
                    cancel: alertifyCancel
                }
            });
        },

        mounted() {
            mounted(this);
            paramMounted();
        },

        beforeDestroy() {
            beforeDestroy(this);
            paramBeforeDestroy();
        }
    });
}

export default initVueApp;
