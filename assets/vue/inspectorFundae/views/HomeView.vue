<template>
  <div class="HomeView">
    <HeaderView/>
    <BannerView/>
    <ContentView :announcement-id="announcement?.id"/>
    <ModalInspectorContent/>
    <ModalInspectorReports :group-id="groupSelected.groupInfo?.id || 0" :announcement-type="announcement?.type" :announcement-id="announcement?.id"/>
    <ModalInspectorStudents/>
    <ModalInspectorSurveys/>
    <ModalUserConection/>
    <ModalUserDetails/>
    <ModalUserNotifications/>
    <ModalUserProgress/>
    <ModalAlerts/>
    <ModalChats :tutor-id="groupSelected.groupInfo?.tutor?.id" :only-read="true"/>
    <ModalUserAssistances :tutor-view="false" class="assistance"/>
  </div>
</template>

<script>
import $                      from 'jquery';
import HeaderView             from "./HeaderView";
import BannerView             from "./BannerView";
import ContentView            from "./ContentView";
import ModalInspectorContent  from "../components/modalInspectorContent";
import ModalInspectorReports  from "../components/modalInspectorReports";
import ModalInspectorStudents from "../components/modalInspectorStudents";
import ModalInspectorSurveys  from "../components/modalInspectorSurveys";
import ModalUserConection     from "../../announcement/components/details/modals/modalUserConection";
import ModalUserDetails       from "../../announcement/components/details/modals/modalUserDetails";
import ModalUserNotifications from "../../announcement/components/details/modals/modalUserNotifications";
import ModalUserAssistances   from "../../announcement/components/details/modals/modalUserAssistances";
import ModalUserProgress      from "../../announcement/components/details/modals/modalUserProgress";
import ModalAlerts            from "../../announcement/components/details/modals/modalAlerts";
import BaseSwitch            from "../../base/BaseSwitch.vue";
import ModalChats             from "../../announcement/components/details/modals/modalChats";
import {get}                  from "vuex-pathify";

export default {
  name : "HomeView",
  components: {
    ModalChats,
    ModalAlerts,
    ModalUserProgress,
    ModalUserAssistances,
    ModalUserNotifications,
    ModalUserDetails,
    ModalUserConection,
    ModalInspectorSurveys,
    ModalInspectorStudents,
    ModalInspectorReports,
    ModalInspectorContent,
    ContentView,
    BannerView,
    HeaderView,
    BaseSwitch
  },
  computed: {
    isLoading: get('announcementModule/isLoading'),
    groupSelected: get('announcementModule/groupSelected'),
    announcement: get('announcementModule/announcement')
  },
  mounted() {
    this.initModalJquery();
    this.$store.dispatch('announcementModule/loadAnnouncement');
    this.$store.dispatch('announcementModule/loadAnnouncementCalledUsers');
  },
  methods: {
    hideDropdowns() {
      $(`[data-bs-toggle="dropdown"] + ul`).removeClass('show');
    },
    initModalJquery() {
      const self = this;
      $(document).ready(function () {
        $('body').on('click', '[data-bs-toggle="modal"]', function (event) {
          event.stopPropagation();
          self.hideDropdowns();
          $($(event.currentTarget).attr('data-bs-target')).modal({
            backdrop: 'static',
            keyboard: false,
            show    : true,
          });
        }).on('click', '[data-bs-dismiss="modal"]', function (event) {
          event.stopPropagation();
          self.hideDropdowns();
          $($(event.currentTarget).attr('data-modal-id')).modal('hide');
        }).on('click', '[data-bs-toggle="dropdown"]', function (event) {
          event.stopPropagation();
          const ulTarget = `[aria-labelledby="${$(event.currentTarget).attr('id')}"]`;
          $(`[data-bs-toggle="dropdown"] + ul:not(${ulTarget})`).removeClass('show');
          $(ulTarget).toggleClass('show');
        });
      })
    }
  }
};
</script>

 <style scoped lang="scss"> 
@import "~bootstrap/scss/mixins";

.HomeView {

  ::v-deep {
    .d-grid { display: grid !important; }
    tr {
      border-color: var(--color-neutral-mid);
    }
    .fa-smile-o {
      font-family: "Font Awesome 5 Free", sans-serif;
      font-weight:400;
      &:before {
        content: "\f118";
      }
    }

    .text-detail {
      font-size: 0.8rem;
      color: var(--text-color-light);
    }

    .bg-info {
      color: var(--color-primary) !important;
      background-color: var(--color-primary-lightest) !important;
      border: 1px solid var(--color-primary-lighter);
    }

    .bg-danger {
      background-color: var(--color-dashboard-3) !important;
    }

    .underline {
      text-decoration: underline;
    }

    .oversize {
      margin: 0 -1.5rem;

      @media (max-width: 576px) {
        margin: 0 auto;
      }
    }

    .fragmentContent.oversize {
      .fragementHeader, .accordionBody {
        padding: 0 1.5rem;
      }
    }

    .table th, .table tbody {
      border: none !important;
    }

    .modal-header {
      background-color: var(--color-neutral-darker);
      align-items: center;

      .modal-title {
        color: white;
      }

      .btn-close {
        filter: invert(1) grayscale(100%) brightness(200%);;
      }
    }

    .modal-body {
      overflow-x: auto;
    }

    .dropdown-menu {
      left: -58%;
    }

    .text-gray {
      color: var(--color-neutral-mid-dark);
    }

    .btn-close{
      background-color: rgba(0,0,0,0) !important;
      border: none;
      width: 1.5rem;
      height: 1.5rem;
      position: relative;
      font-family: "Font Awesome 5 Free", sans-serif;

      &:before {
        content: "\f00d";
        inset: 0;
        text-align: center;
        font-size: 1.2rem;
        font-weight: 900;
        -webkit-font-smoothing: antialiased;
        display: inline-block;
        font-style: normal;
        font-variant: normal;
        text-rendering: auto;
        line-height: 1;
      }
    }

    .assistanceTable{
    width: 100%;
  }
  }

 
}
</style>
