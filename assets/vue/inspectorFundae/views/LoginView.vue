<template>
  <div class="LoginView">
    <form @submit.prevent="login()" class="Login">
      <h5 class="bg-dark py-3 px-4 text-left text-white mb-4">Bienvenido</h5>
      <div class="form-group text-left px-4">
        <label for="user" class="form-label font-weight-bold"><i class="fa fa-user mr-2"></i>User</label>
        <input type="text" class="form-control" name="user" id="user" v-model="user" required>
      </div>
      <div class="form-group text-left px-4 mb-0">
        <label for="password" class="form-label font-weight-bold"><i class="fa fa-key mr-2"></i>Password</label>
        <input type="password" class="form-control"  name="password" id="password" v-model="password" required>
      </div>
      <button class="btn btn-primary my-4 mx-auto" type="submit">Login</button>
    </form>
  </div>
</template>

<script>

export default {
  name: "LoginView",
  data() {
    return {
      user: '',
      password: ''
    };
  },
  methods: {
    async login() {
      const success = await this.$store.dispatch('authModule/login', {
        user: this.user,
        password: this.password
      });

      if (success) this.$router.push({ name: 'HomeView'}).catch(() => {})
    }
  }
};
</script>

<style scoped lang="scss">
.LoginView {
  display: grid;
  place-content: center;
  width: 100vw;
  min-height: 100vh;
  background: url("../../../images/admin/loginPattern.svg") repeat;

  .Login {
    width: clamp(300px, 100vw, 500px);
    background-color: white;
    text-align: center;
    border-radius: 7px;
    overflow: hidden;
  }
}
</style>
