<template>
<div class="modalInspectorSurveys">
  <BaseModalInspector
      :identifier="tag + 'modalInspectorSurveys'"
      :title="`${$t('INSPECTORVIEW.SURVEY_RESULTS')}`"
      padding="2rem"
      size="modal-xl">
    <DataNotFound
        :hide-on="!opinions.length"
        :text="$t('ANNOUNCEMENT.OPINIONSTAB.NOT_FOUND') || ''"
        icon="fa-star"
        :banner="true"/>
    <div class="opinionContainer">
      <Opinion v-for="opinion in opinions" :key="opinion.id" :opinion="opinion" :enable-visibility="false"/>
    </div>
  </BaseModalInspector>
</div>
</template>

<script>
import BaseModalInspector from "./BaseModalInspector";
import DataNotFound       from "../../announcement/components/details/DataNotFound";
import Opinion            from "../../common/components/opinions/Opinion";
import {get}              from "vuex-pathify";
export default {
  name: "modalInspectorSurveys",
  components: {Opinion, DataNotFound, BaseModalInspector},
  props: {
    tag: { type: String, default: '' },
  },
  computed: {
    groupSelected: get('announcementModule/groupSelected'),
    opinions() {
      return this.groupSelected?.groupInfo?.opinions || [];
    }
  }
}
</script>

 <style scoped lang="scss"> 
.modalInspectorSurveys {
  .opinionContainer {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 1rem;

    @media (max-width: 576px) {
      margin: 0 auto;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
  }
}
</style>
