<template>
<div class="groupContent content p-3">
  <div class="groupContentHeader bg-dark d-flex flex-wrap align-items-center justify-content-between py-3 px-5">
    <div class="d-flex">
      <div class="groupName text-center">
        <i class="fa fa-users"></i>
        <p class="mb-0">{{ $t('ANNOUNCEMENT.GROUP') }}: {{ groupNumber }}</p>
      </div>
      <div class="mx-4">
        <p class="mb-0">{{ group.name }}</p>
        <p class="subtitle mb-0">{{ $t('INSPECTORVIEW.TOTAL_USERS', [users.length]) }}</p>
      </div>
    </div>
    <div>
      <button class="btn btn-sm btn-primary mb-2" type="button"
             @click="goToContents()">
        <i class="fa fa-book"></i> {{ $t('INSPECTOR.CONTENTS') }}
      </button>
      <button class="btn btn-sm btn-primary mb-2" type="button"
              @click="setGroupSelected('#modalInspectorStudents')">
        <i class="fa fa-users"></i> {{ $t('ANNOUNCEMENT.FORM.STEPS.STUDENTS') }}
      </button>
      <button class="btn btn-sm btn-primary mb-2" type="button"
              @click="setGroupSelected('#modalInspectorReports')">
        <i class="fa fa-copy"></i> {{ $t('ANNOUNCEMENT.MODALS.REPORT_TITLE') }}
      </button>
      <button class="btn btn-sm btn-primary mb-2 mr-3" type="button"
              @click="setGroupSelected('#modalInspectorSurveys')">
        <i class="fa fa-list"></i> {{ $t('INSPECTORVIEW.SURVEY_RESULTS') }}
      </button>
      <i v-show="isOpened" @click="isOpened=false" class="fa fa-chevron-up cursor-pointer mb-2"></i>
      <i v-show="!isOpened" @click="isOpened=true" class="fa fa-chevron-down cursor-pointer mb-2"></i>
    </div>
  </div>
  <div v-show="isOpened">
    <div class="groupContentBody bg-white w-100 p-4 justify-content-between flex-wrap d-flex">
      <div class="data d-table">
        <ResumeTag icon="fa-id-card" :title="`${$t('ANNOUNCEMENT.FORM.ENTITY.GROUP.GROUP_ID')}:`" answer="DFF0041MZ"/>
      </div>
      <div class="data d-table">
        <ResumeTag icon="fa-barcode" :title="`${$t('ANNOUNCEMENT.FORM.ENTITY.GROUP.CODE')}:`" answer="DFF0045CC"/>
      </div>
      <div class="data d-table">
        <ResumeTag icon="fa-user-tag" :title="`${$t('INSPECTORVIEW.TUTOR')}:`" :answer="groupInfo.tutor.fullName"/>
      </div>
      <div class="data d-table">
        <button class="btn btn-sm btn-primary mb-2 mr-3" @click="setTutor">
          <i class="fa fa-eye"></i>
        </button>
      </div>
    </div>
    <div class="groupContentBody bg-white w-100 p-4 justify-content-between flex-wrap d-flex" v-if="sessions.length > 0">
      <assistance-table :group-id="groupInfo.id" :session-list="sessions" :tutor-view="false"/>
    </div>
  </div>
</div>
</template>

<script>
import ResumeTag from "./resumeTag";
import $ from "jquery";
import axios from "axios";
import AssistanceTable from "../../announcement/components/details/assistanceTable.vue";
export default {
  name: "groupContent",
  components: {AssistanceTable, ResumeTag},
  props: {
    opened: {
      type: Boolean,
      default: false
    },
    groupNumber: {
      type: Number,
      default: 1
    },
    group: {
      type: Object,
      default: () => ({})
    },
    announcementId: { type: Number, default: undefined },
  },
  computed: {
    users() {
      return (this.group?.users || []);
    },
    groupInfo() {
      return (this.group?.groupInfo || {});
    },
    tutor() {
      return (this.groupInfo?.tutor || {});
    },
    sessions() {
      return (this.group?.sessions || []);
    }
  },
  data() {
    return {
      isOpened: false
    }
  },
  mounted() {
    this.isOpened = this.opened;
  },
  methods: {
    setGroupSelected(modalId) {
      this.$store.dispatch("announcementModule/setGroupSelected", this.group);
      $(modalId).modal({ backdrop: 'static', keyboard: false, show: true });
    },
    setTutor() {
      this.$store.dispatch("announcementModule/setTutorSelected", this.groupInfo.tutor || {});
      console.log(this.groupInfo.tutor || {});
      $('#tutorProfile').modal({ backdrop: 'static', keyboard: false, show: true });
    },
    async goToContents() {
      const result = await axios.post('/inspector/go-to-campus', {
        announcementId: this.announcementId
      });
      if(result.data?.data){
        window.open(result.data.data.url, '_blank', 'noreferrer')?.focus();
      }
    },
  }
}
</script>

 <style scoped lang="scss"> 
.groupContent {
  .groupContentHeader, .groupContentBody {
    gap: 0.75rem;
  }
  .data .resumeTag {
    height: 2.5rem;
  }
  .groupName {
    i {
      font-size: 2rem;
    }
    p {
      font-size: 0.8rem;
    }
  }
  .subtitle {
    color: var(--color-primary-light);
    font-size: 0.8rem;
  }
}
</style>
