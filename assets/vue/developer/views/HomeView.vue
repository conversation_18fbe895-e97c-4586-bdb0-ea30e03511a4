<template>
  <div class="home">
    <home
      title="SECTION.HOME.TITLE"
      description="SECTION.HOME.DESCRIPTION"
      src-thumbnail="/assets/imgs/survey_app.svg"
    >
      <template v-slot:content-main>
        <div
            class="col-12 d-flex flex-row align-items-center justify-content-center"
            v-if="loading"
        >
          <spinner />
        </div>

        <div class="CreateSurvey--content">
          <ul class="nav nav-tabs">
            <li class="nav-item" role="presentation">
              <button
                  type="button"
                  class="nav-link"
                  :class="activePane === 'homefilter' ? 'active' : ''"
                  id="homefilter-tab"
                  @click="activePane = 'homefilter'"
              >
                <i class="fa fa-comments"></i> Filter
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                  type="button"
                  class="nav-link"
                  :class="activePane === 'userCourse' ? 'active' : ''"
                  id="userCourse-tab"
                  @click="activePane = 'userCourse'"
              >
                <i class="fa fa-filter"></i> User Course
              </button>
            </li>
          </ul>

          <div class="tab-content">
            <div class="tab-pane fade"
                 :class="activePane === 'homefilter' ? 'active show' : ''"
                 id="homefilter"
                 role="tabpanel"
                 aria-labelledby="homefilter-tab"
            >

              <button class="btn btn-primary right" @click="executeAllServices()">
                Execute all services
              </button>

              <table class="table table-condensed mt-3">
                <thead>
                <tr>
                  <th>Service</th>
                  <th>Description</th>
                  <th></th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(service, index) in catalogs" :key="index">
                  <td>{{ service.name }}</td>
                  <td>{{ service.description }}</td>
                  <td class="col-1">
                    <button
                        class="btn btn-outline-secondary"
                        @click="executeService(service.service)"
                    >
                      execute
                    </button>
                  </td>
                </tr>
                </tbody>
              </table>

            </div>
          </div>

          <div class="tab-pane fade"
               :class="activePane === 'userCourse' ? 'active show' : ''"
               id="userCourse"
               role="tabpanel"
               aria-labelledby="userCourse-tab"
          >
            <button class="btn btn-primary right" @click="executeUpdateUserCourse()">
              Update User Course Finished
            </button>
          </div>
        </div>
      </template>
    </home>
  </div>
</template>

<script>
import { get } from "vuex-pathify";

import Home from "../../base/Home.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import BaseSwitch from "../../base/BaseSwitch.vue";

export default {
  components: {
    Home,
    BaseSwitch,
    Spinner,
  },

  data() {
    return {
      activePane: 'homefilter'
    };
  },

  computed: {
    ...get("developerModule", ["loading", "getServices", "getCatalogs"]),
    
    servicies() {
      return this.getServices();
    },

    catalogs() {
      return this.getCatalogs() ?? [];
    },
  },

  async created() {
    await this.$store.dispatch("developerModule/fetchCatalogs");
  },

  methods: {
    async executeService(service) {
      await this.$store.dispatch("developerModule/executeServices", {
        endpoint: "/admin/developer/services",
        requestData: { services: service },
      });
    },

    async executeAllServices() {
      console.log("executeAllServices");
      await this.$store.dispatch("developerModule/executeAllServices", {
        endpoint: "/admin/developer/services/all",
      });
    },

    async executeUpdateUserCourse(){
      console.log('ExecuteUpdateUserCourse');
      await this.$store.dispatch("developerModule/executeUpdateUserCourse", {
        endpoint: "/admin/developer/usercourse/finished",
      });
    }
  },
};
</script>

 <style scoped lang="scss"> 
.home {
  .sort-asc::after,
  .sort-desc::after {
    content: " ▼";
    font-size: 10px;
    opacity: 0.5;
    margin-left: 5px;
  }

  .sort-desc::after {
    content: " ▲";
  }

  th {
    cursor: pointer;
  }

  th.sort-asc:hover::after,
  th.sort-desc:hover::after {
    opacity: 1;
    cursor: pointer;
  }
}
</style>
