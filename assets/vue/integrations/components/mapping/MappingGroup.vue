
<script>
import ButtonWithDescription from "../../../common/components/ButtonWithDescription.vue";
import MappingModal from "./MappingModal.vue";
import {sync} from "vuex-pathify";
import Mappings from "./Mappings.vue";

export default {
  name: "MappingGroup",
  components: {Mappings, MappingModal, ButtonWithDescription},
  data() {
    return {
      previewMapping: null
    };
  },
  computed: {
    group: sync('integrationMappingModule/current@group'),
  },
  methods: {
    saveGroup() {
      this.$store.dispatch('integrationMappingModule/saveMappingGroup').then(r => {
        this.$toast.success("Información guardada");
      })
    }
  }
}
</script>

<template>
  <div class="MappingGroup w-100" v-if="group">
    <div class="col-12 MappingGroup--BasicInfo">
      <button-with-description
          title="Habilitar"
          description="Habilitar ejecucion del grupo"
          v-model="group.active"
      />
      <div class="w-100 form-group mb-0 pb-0">
        <label>Group Name</label>
        <input type="text" class="form-control" v-model="group.name">
      </div>
      <button type="button" class="btn btn-sm btn-success" @click="saveGroup()"><i class="fa fa-check-square"></i></button>
    </div>

    <mappings />

    <div class="MappingView" v-if="previewMapping">
      <button
          type="button"
          class="btn-close ml-auto mb-1"
          aria-label="Close"
          @click="previewMapping = null"
      >
      </button>
      <pre>{{ JSON.stringify(previewMapping, null, 2) }}</pre>
    </div>

  </div>
</template>

<style scoped lang="scss">
.MappingGroup {
  position: relative;
  &--BasicInfo {
    display: grid;
    grid-template-columns: 1fr 1fr 35px;
    gap: 5px;
    align-items: flex-end;
  }

  .MappingView {
    width: 320px;
    height: 320px;
    position: absolute;
    top: 0;
    right: 0;
    background-color: #FFFFFF;
    border: 1px solid #212121;
    border-radius: 5px;
    overflow-y: auto;
    display: flex;
    flex-flow: column;
    padding: .5rem .2rem .5rem .2rem;
  }
}
</style>
