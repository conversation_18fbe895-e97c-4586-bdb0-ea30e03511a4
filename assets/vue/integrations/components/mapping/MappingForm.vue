<script>
import { get } from 'vuex-pathify';
import Multiselect from "vue-multiselect";
import 'vue-multiselect/dist/vue-multiselect.min.css'
import AutocompleteInput from "../../../common/components/AutocompleteInput.vue";

export default {
  name: "MappingForm",
  components: {AutocompleteInput, Multiselect},
  props: {
    value: {
      type: Object,
      default: () => ({
        entity: null,
        identifier: '',
        type: 'ENTITY',
        main: false,
        mapping: []
      })
    }
  },
  directives: {
    noSpace: {
      bind: (el, binding, vnode) => {
        el.addEventListener('input', function () {
          const currentValue = el.value;
          const cleanedValue = currentValue.replace(/ /g, "");
          if (currentValue !== cleanedValue) {
            el.value = cleanedValue;
            vnode.elm?.dispatchEvent(new CustomEvent('input'));
          }
        });
      }
    }
  },
  data() {
    return {
    };
  },
  computed: {
    categories: get('configModule/config@categories'),
    entities: get('configModule/config@entities'),
    fields: get('configModule/config@fields'),
    identifiers: get('configModule/config@identifiers'),
    filterTags: get('configModule/config@filterTags'),
    remoteAttributes: get('configModule/config@remoteAttributes'),
    availableRemoteAttributes() {
      return this.remoteAttributes ?? [];
    },
    entityIdentifiers() {
      const ids = this.identifiers[this.mapping.entity] ?? [];
      if (ids.length === 0) this.mapping.identifier = null;
      return ids;
    },
    type: {
      get() {
        return this.mapping.type;
      },
      set(newValue) {
        this.mapping = Object.assign({}, this.mapping, {type: newValue});
      }
    },
    mapping: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    }
  },
  watch: {
    type: {
      handler: function (val, oldVal) {
        if (oldVal == null || val == null) return;
        // Clean to avoid conflicts
        for (let i = 0; i < this.mapping.mapping.length; i++) {
          this.mapping.mapping[i].local = null;
        }
      }
    }
  },
  methods: {
    addMapping() {
      const mapping = this.mapping.mapping ?? [];
      mapping.push({local: '', remote: ''})
      this.mapping = {...this.mapping, mapping };
    }
  }
}
</script>

<template>
<div class="MappingForm">
  <div class="col-12 d-flex flex-row flex-nowrap align-items-end">
    <div class="form-group col-4 mb-0">
      <label>Type</label>
      <select class="form-select" v-model="type" required>
        <option value="FILTER">Filtros</option>
        <option value="ENTITY">Entity</option>
      </select>
    </div>
    <div class="form-group col-4 mb-0" v-if="type && type !== 'FILTER'">
      <label>Entity</label>
      <select class="form-select" v-model="mapping.entity">
        <option :value="null" disabled>Seleccionar</option>
        <option v-for="entity in entities" :key="entity" :value="entity">{{ entity }}</option>
      </select>
    </div>
    <div class="form-group col-4 mb-0" v-if="type && type === 'FILTER'">
      <label>Tag (Campo remoto idéntico, data diferente)</label>
      <select class="form-select" v-model="mapping.entity">
        <option :value="null">No Tag</option>
        <option v-for="t in filterTags" :key="t" :value="t">{{ t }}</option>
      </select>
    </div>
    <div class="form-group col-4 mb-0" v-if="type && type !== 'FILTER' && entityIdentifiers.length > 0">
      <label>Identifier</label>
      <select class="form-select" v-model="mapping.identifier">
        <option :value="null" disabled>Seleccionar</option>
        <option v-for="identifier in entityIdentifiers" :key="identifier" :value="identifier">{{ identifier }}</option>
      </select>
    </div>
  </div>
  <div class="col-12 MappingForm--Content" v-if="type != null">
    <div class="Header">
      <div class="MappingForm--Content--Element">
        <span class="w-100 text-center">
          <strong v-if="type === 'FILTER'">Categorías</strong>
          <strong v-else>Campo Local (No Spaces)</strong>
        </span>
        <span class="w-100 text-center"><strong>Campo Remoto</strong></span>
        <button type="button" class="btn btn-primary" @click="addMapping()"><i class="fa fa-plus"></i></button>
      </div>
    </div>
    <div class="Body">
      <div class="MappingForm--Content--Element" v-for="(m, i) in mapping.mapping" :key="i">
        <div class="w-100">
          <Multiselect
              v-if="type === 'FILTER'"
              v-model="m.local"
              :options="categories"
              :searchable="true"
              :multiple="true"
              track-by="id"
              label="name"
              :placeholder="$t('MULTISELECT.PLACEHOLDER')"
              :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
          ></Multiselect>
          <select class="form-select" v-model="m.local" v-else>
            <option :value="null" selected disabled>Seleccionar un campo permitido</option>
            <option v-for="field in fields[mapping.entity]" :key="`${i}-${field}`" :value="field">{{ field }}</option>
          </select>
          <div class="w-100">
            <autocomplete-input v-model="m.remote" :suggestions="availableRemoteAttributes"/>
          </div>

          <button type="button" class="btn btn-danger" @click="mapping.mapping.splice(i, 1)"><i class="fa fa-minus"></i></button>
        </div>

        <div class="w-100">
          <input type="text" class="form-control" required v-model="m.remote">
        </div>

        <button type="button" class="btn btn-danger" @click="mapping.mapping.splice(i, 1)"><i class="fa fa-minus"></i></button>
      </div>
    </div>
  </div>
</div>
</template>

<style scoped lang="scss">
.MappingForm {
  background-color: #FFFFFF;
  border: 1px dashed #d0d0d0;
  padding: 1rem;
  border-radius: 5px;

  &--Content {
    display: grid;
    row-gap: .5rem;
    .Body {
      display: flex;
      row-gap: 0.5rem;
      min-height: 270px;
      max-height: 400px;
      overflow-y: auto;
      align-items: flex-start;
      flex-flow: column;
    }
    &--Element {
      width: 100%;
      display: grid;
      gap: .5rem;
      grid-template-columns: 1fr 1fr 40px;
    }
  }
}
</style>
