<script>
import { sync } from 'vuex-pathify';
import ButtonWithDescription from "../../../common/components/ButtonWithDescription.vue";
import LdapRegister from "./LdapRegister.vue";
import Spinner from "../../../base/BaseSpinner.vue";

export default {
  name: "LdapView",
  components: {Spinner, LdapRegister, ButtonWithDescription},
  computed: {
    loading: sync('ldapModule/loading'),
    enabled: sync('ldapModule/configuration@enabled')
  },
  methods: {
    save() {
      this.$store.dispatch('ldapModule/save').then(r => {
        this.$toast.success('Configuration saved');
      }).catch(e => {
        this.$toast.error('Failed to save configuration');
      })
    }
  }
}
</script>

<template>
  <div class="d-flex w-100 align-items-center justify-content-center" v-if="loading">
    <spinner />
  </div>
  <div class="LdapView" v-else>
    <div class="col-12 d-flex flex-row">
      <button-with-description
          title="Enable LDAP"
          description="Habilitar SSO por LDAP"
          icon="fa fa-address-book"
          v-model="enabled"
      />
      <button type="button" class="btn btn-primary ml-auto align-items-start" @click="save()">Save</button>
    </div>
    <div class="col-12">
      <ldap-register />
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
