<script>
export default {
  name: "HomeView",
  data() {
    return {
      activeTab: 'LdapView'
    };
  },
  watch: {
    activeTab: {
      immediate: true,
      handler: function () {
        if (this.$route.name !== this.activeTab) {
          this.$router.push({ name: this.activeTab });
        }
      }
    }
  },
  created() {
    this.$store.dispatch('ldapModule/getConfiguration');
    this.$store.dispatch('integrationMappingModule/getGroups');
  }
}
</script>

<template>
  <div class="HomeView">
    <ul class="nav nav-tabs ps-4">
      <li class="nav-item" role="presentation">
        <button
            type="button"
            class="nav-link"
            :class="activeTab === 'LdapView' ? 'active' : ''"
            @click="activeTab = 'LdapView'"
        >
          <i class="fa fa-file"></i> LDAP
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
            class="nav-link"
            :class="activeTab === 'MappingView' ? 'active' : ''"
            @click="activeTab = 'MappingView'"
        >
          <i class="fa fa-file"></i> Mapping
        </button>
      </li>
    </ul>
    <div class="tab-content">
      <div class="tab-pane active">
        <router-view />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
