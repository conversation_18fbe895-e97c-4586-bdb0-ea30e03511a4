<template>
  <div v-if="loading" class="d-flex align-items-center justify-content-center">
    <spinner />
  </div>
  <form id="create-documentation-form" class="CreateDocumentation" @submit.prevent="submit()" v-else>
    <div class="CreateDocumentation--content">
      <FileSelector name="thumbnail"
                    preview-default-class="full-size"
                    :default-image="documentation.thumbnail ? `/${filePath}/${documentation.thumbnail}`: defaultImage "
      />
      <div class="CreateDocumentation--content--info">
        <div class="col-9 form-group required p-0">
          <label class="form-label" for="title">{{ $t('TITLE') }}</label>
          <input type="text" class="form-control" id="title" name="title" v-model="documentation.title">
        </div>
        <div class="col-3 form-group required pl-1 pt-0 pb-0 pr-0">
          <label class="form-label" for="position">{{ $t('POSITION') }}</label>
          <input type="number" min="0" step="1" class="form-control" name="position" id="position" v-model="documentation.position">
        </div>
        <div class="form-group col-12 p-0">
          <label class="form-label">{{ $t('DESCRIPTION') }}</label>
          <froala v-model="documentation.description" tag="textarea" :config="froalaConfig"/>
        </div>
        <div class="col-12 p-0 locale-selector" v-if="multilingual">
          <locale-selector v-model="documentation.locale"></locale-selector>
        </div>
      </div>
    </div>
    <div class="col-12 d-flex flex-row flex-wrap">
      <div class="col-md-6 col-xs-12">
        <label>{{ $t('USER.ROLES.TITLE') }}</label>
        <select class="form-select" multiple size="5" aria-label="multiple select roles" v-model="documentation.roles">
          <option :value="role.id" v-for="role in rolesKeyValue" :key="role.id">{{ role.name }}</option>
        </select>
      </div>
      <div class="col-md-6 col-xs-12">
        <div class="col-12 mb-3 p-0">
          <label>{{ $t('DOCUMENTATION.UPLOAD_CONTENT') }}</label>
          <div class="col-12 info--file-selector">
            <div class="info--file-selector--types">
              <button type="button"
                      v-for="type in types"
                      class="link"
                      :class="documentation.type === type.type ? 'active' : ''"
                      @click="documentation.type = type.type; selectedType = type">
                <i :class="type.icon"></i>
                <span>{{ type.name }}</span>
              </button>
            </div>

            <div class="info--file-selector--info" :class="selectedType?.type">
              <div class="p-1" v-if="documentation.type === 'url'">
                <input placeholder="URL" type="url" name="url" class="url form-control w-100" v-model="documentation.url">
              </div>
              <input id="file" name="file" type="file" :accept="selectedType?.accept" @change="onFileSelected($event.target)" v-else>
              <div class="info--file-selector--info--file" v-if="selectedType && selectedType?.type !== 'url'">
                <button @click="openFileSelector()" id="select-upload-file" type="button" class="btn btn-primary">{{ $t('DOCUMENTATION.SELECT_CONTENT') }}</button>
                <span>{{ documentation.originalName ?? $t('DOCUMENTATION.NO_CONTENT_SELECTED') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </form>
</template>

<script>
import $ from 'jquery';
import {get} from "vuex-pathify";

import FileSelector from "../../common/components/FileSelector.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import LocaleSelector from "../../common/components/LocaleSelector.vue";

export default {
  name: "CreateDocumentation",
  components: {LocaleSelector, FileSelector, Spinner},
  $,
  data() {
    return {
      loading: false,
      documentation: {
        title: '',
        type: '',
        description: '',
        position: 0,
        roles: [],
        locale: null,
        url: '',
        originalName: null,
        thumbnail: null,
      },
      selectedType: null,
      filename: null
    };
  },
  computed: {
    types: get('configModule/config@types'),
    roles: get('configModule/config@roles'),
    multilingual: get('configModule/config@multilingual'),
    defaultLocale: get('localeModule/defaultLocale'),
    filePath: get('configModule/config@filePath'),
    defaultImage: get('configModule/defaultImageB64'),
    rolesKeyValue() {
      const data = [];
      const keys = Object.keys(this.roles);
      keys.forEach(item => {
        const name = this.roles['' + item]
        data.push({ id: item, name: this.$t(name.toUpperCase()) })
      })
      return data;
    },
    froalaConfig() {
      return {
        ...this.$store.getters['froalaEditorModule/getDefaultConfiguration'],
        pluginsEnabled: ['align', 'link', 'url']
      };
    }
  },
  async created() {
    this.loading = true;
    await this.$store.dispatch('contentTitleModule/setActions', {
      route: this.$route.name,
      actions: [
        {
          name: this.$t('SAVE'),
          event: 'onSubmit',
          class: 'btn btn-primary'
        }
      ]
    });
    const isUpdate = this.$route.name === 'UpdateDocumentation';
    await this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: isUpdate ? this.$t('DOCUMENTATION.CREATE_DOCUMENTATION') : this.$t('DOCUMENTATION.CREATE_DOCUMENTATION'),
        params: {}
      }
    });

    try {
      this.documentation.locale = this.defaultLocale;
      if (isUpdate) {
        const result = await this.$store.dispatch('documentationModule/getDocumentation', this.$route.params.id);
        const { data } = result;
        this.documentation = data;
        const index = this.types.findIndex(item => item.type === this.documentation.type); 
        this.selectedType = index >= 0 ? this.types[index] : null;
      }
    } finally {
      this.loading = false;
    }
  },
  mounted() {
    this.$eventBus.$on('onSubmit', () => {
      this.submit();
    })
  },
  beforeDestroy() {
    this.$eventBus.$off('onSubmit');
  },
  methods: {
    submit() {
      const isUpdate = this.$route.name === 'UpdateDocumentation';
      const form = document.forms['create-documentation-form'];
      const formData = new FormData(form);
      formData.append('description', this.documentation.description);
      formData.append('locale', this.documentation.locale);
      formData.append('roles', JSON.stringify(this.documentation.roles));
      formData.append('type', this.documentation.type);
      if (!isUpdate && this.documentation.type !== 'url' && document.getElementById('file').files.length < 1) {
        this.$toast.error('No file has been selected');
        return;
      }

      this.$alertify.confirmWithTitle(
          this.$t('DOCUMENTATION.SAVE.CONFIRM.TITLE'),
          this.$t('DOCUMENTATION.SAVE.CONFIRM.DESCRIPTION'),
          () => {
            this.saveDocumentation(formData, isUpdate);
          },
          () => {},
      )
    },

    saveDocumentation(formData, isUpdate = false) {
      const self = this;
      function save() {
        if (isUpdate) return self.$store.dispatch('documentationModule/updateDocumentation', { id: self.$route.params.id, formData});
        else return self.$store.dispatch('documentationModule/saveNewDocumentation', formData);
      }
      this.$store.dispatch('loaderModule/setLoading', { loading: true, message: this.$t('SAVING')});
      save().then(res => {
        const { error, data } = res;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('DOCUMENTATION.SAVE.SUCCESS') + '');
          this.$store.dispatch('contentTitleModule/removeRouteFromContentTitle', this.$route.name);
          this.$store.dispatch('routerModule/setDeleteLastRoute', true);
          this.$router.replace({ name: 'Home' });
        }
      }).finally(() => {
        this.$store.dispatch('loaderModule/setLoading', { loading: false});
      })
    },

    openFileSelector() {
      $('#file').click();
    },

    onFileSelected(input) {}
  }
}
</script>

 <style scoped lang="scss"> 
.CreateDocumentation {
  :deep(.FileSelector__preview) {
    background-size: cover !important;
  }

  &--content {
    width: 100%;
    display: flex;
    flex-flow: column;
    justify-content: center;

    @media #{min-small-screen()} {
      grid-template-columns: 320px 1fr;
      column-gap: 1rem;
    }

    @media #{min-medium-screen()} {
      display: grid;
      grid-template-columns: 400px 1fr;
      column-gap: 1rem;
      padding: 2rem;
    }

    .FileSelector {
      width: 100%;
    }

    &--info {
      display: flex;
      flex-flow: row wrap;
    }

  }

  .info--file-selector {
    border: 1px solid $base-border-color;
    background-color: #ffffff;

    &--types {
      width: 100%;
      display: flex;
      flex-flow: row wrap;
      align-items: center;
      justify-content: center;

      button {
        width: 50px;
        height: 50px;
        border: 1px solid #CBD5E1;
        border-radius: 5px;
        margin: 5px;
        color: #CBD5E1;
        background-color: #ffffff;
        font-size: 18px;
        display: flex;
        flex-flow: column;
        align-items: center;
        justify-content: center;
        span {
          font-size: 12px;
        }

        &.active {
          border: 1px solid #3468ac;
          color: #3468ac;
        }
      }
    }

    &--info {
      input:not(.url) {
        display: none;
      }

      &.url {
        display: block;
      }

      &--file {
        width: 100%;
        display: flex;
        flex-flow: column;
        align-items: center;
        justify-content: center;
        span {
          font-size: 17px;
          color: #1E293B;
        }
      }
    }
  }
}
</style>
