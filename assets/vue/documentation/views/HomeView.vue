<template>
  <home title="DOCUMENTATION.HOME.TITLE"
        description="DOCUMENTATION.HOME.DESCRIPTION"
        :allow-filters="true"
        @apply-filters="loadDocumentation()"
        @clear-filters="loadDocumentation()"
  >
    <template v-slot:content-filters>
      <div class="col-md-4 col-xs-12">
        <label>{{ $t('LOCALES') }}</label>
        <select class="form-select" aria-label="multiple select roles" v-model="locale">
          <option :value="key" v-for="(locale, key) in locales" :key="key">{{ locale }}</option>
        </select>
      </div>
    </template>
    <template v-slot:content-actions>
      <router-link class="btn btn-primary mr-auto" :to="{ name: 'CreateDocumentation' }">{{ $t('DOCUMENTATION.CREATE_DOCUMENTATION') }}</router-link>
    </template>
    <template v-slot:content-main>
      <div v-if="loading" class="d-flex align-items-center justify-content-center">
        <spinner />
      </div>
      <div v-else class="col-12">
        <div class="Items">
          <div class="Item" v-for="item in items" :key="item.id" @click.stop="(event)=>openViewer(item, event)">
            <div class="Item--content" :style="{
              'background-image': `url(${(item.thumbnail ? ('/'+ filePath + '/' + item.thumbnail) : defaultImage)})`
            }">
              <span :class="iconType(item.type)"></span>
              <p>
                <span>{{ item.title }}</span>
              </p>
            </div>
            <div class="Item--footer d-flex align-items-center justify-content-start">
              <span><i class="fa fa-calendar"></i> {{ item.createdAt }} </span>
              <div class="ml-auto">
                <router-link :to="{name: 'UpdateDocumentation', params: {id: item.id}}" type="button" class="btn btn-sm btn-primary"><i class="fa fa-pencil"></i></router-link>
                <button @click.stop="deleteDocumentation(item)" type="button" class="btn btn-sm btn-danger"><i class="fa fa-trash"></i></button>
                <div v-if="item.type === 'video'">    
                  <!-- modal -->
                   <div class="modal fade" :id="item.id" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                      <div class="modal-dialog" role="document">
                        <div class="modal-content dimension">
                          <div class="header-modal">
                            <h5> {{ item.title }}</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                              <span aria-hidden="true">&times;</span>
                            </button>
                          </div>
                          <div class="modal-body">                  
                            <vue-vimeo-player :video-url="item.url" :width="600" :height="400"></vue-vimeo-player>
                          </div>
                        </div>
                      </div>
                  </div>                  
                  <!-- modal -->  
                </div>
                <div v-else>
                  <viewer :custom="false" :base-path="filePath" :file="item" :modal="true" :open="item.id === openId" @close="openId = -1"/>
                </div>               
              </div>
            </div>
          </div>           
        </div>
        <base-not-result v-if="items.length === 0" />
        <pagination v-if="items.length > 0" :total-items="totalItems" :prop-current-page="page" @current-page="onPageChange"/>
      </div>
    </template>
  </home>
</template>

<script>
import {get, sync} from "vuex-pathify";
import Home from "../../base/Home.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import Viewer from "../../common/components/viewer/Viewer.vue";
import Pagination from "../../admin/components/Pagination.vue";
import BaseNotResult from "../../base/BaseNotResult.vue";
import { Modal } from "bootstrap";

import { vueVimeoPlayer } from 'vue-vimeo-player'

export default {
  name: "HomeView",
  components: {BaseNotResult, Pagination, Viewer, Spinner, Home, vueVimeoPlayer},
  data() {
    return {
      openId: -1,
      uniqueModal:null,
    }
  },
  computed: {
    loading: get('documentationModule/loading'),
    items: get('documentationModule/items'), 
    page: sync('documentationModule/pagination@page'),
    totalItems: get('documentationModule/pagination@totalItems'),
    defaultImage: get('configModule/defaultImageB64'),
    filePath: get('configModule/config@filePath'),
    locales: get('localeModule/locales'),
    locale: sync('documentationModule/filters@locale')
  },
  created() {
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: this.$t('DOCUMENTATION.TITLE'),
        params: {}
      }
    });

    this.loadDocumentation();
  },
  methods: {
    onPageChange(page) {
      this.page = page;
      this.loadDocumentation();
    },
    openViewer(item, event) {
      if (item.type === 'url') {
        const popup = window.open(item.url, "_blank");
        if (popup == null || typeof popup == "undefined") {
          this.$toast.error("Failed to open url. Check browser permissions");
        } else {
          popup.focus();
        }
      } else if (item.type === 'video') {
        if(!this.uniqueModal){
          this.uniqueModal = new Modal(document.getElementById(item.id),{ keyboard: false });
          this.uniqueModal.show();
        }else{
          this.uniqueModal.hide();
          this.uniqueModal=null;
        }
      }else {
        this.openId = item.id;
      }

      if (event) {
        event.preventDefault();
        if(event.srcElement._prevClass === "btn-close") this.openId=-1;

      }
    },
    loadDocumentation() {
      this.$store.dispatch('documentationModule/getDocumentationItems', this.filters);
    },
    deleteDocumentation(documentation) {
      this.$alertify.confirmWithTitle(
          this.$t('DOCUMENTATION.DELETE.CONFIRM.TITLE'),
          this.$t('DOCUMENTATION.DELETE.CONFIRM.DESCRIPTION'),
          () => {
            this.$store.dispatch('documentationModule/deleteDocumentation', documentation.id).then(res => {
              const { error } = res;
              if (error) this.$toast.error(this.$t('DOCUMENTATION.DELETE.FAILED') + '')
              else this.$toast.error(this.$t('DOCUMENTATION.DELETE.SUCCESS') + '')
            })
          },
          () => {},
      )
    },
    iconType(type) {
      switch (type) {
        case 'video': return 'fab fa-youtube'
        case 'url': return 'fa fa-link'
        default: return 'fa fa-file-pdf'
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 
.Home {
  .Items {
    display: grid;
    grid-template-columns: repeat(auto-fit, 250px);
    gap: 1rem;

    .Item {
      cursor: pointer;
      padding: 0.25rem;
      width: 100%;
      background-color: #ffffff;
      border: 1px solid $base-border-color;

      &--content {
        background-size: cover;
        background-position: center center;
        height: 150px;
        position: relative;
        display: flex;
        color: #ffffff;
        width: 100%;

        > span {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 23px;
          color: #ffffff;
          cursor: pointer;
          margin: 0.25rem;
        }

        p {
          font-size: 17px;
          color: #FFFFFF;
          width: 100%;
          overflow: hidden;
          text-shadow: 1px 1px 3px #000000;
          background: linear-gradient(0deg, rgba(2,0,36,0.3) 0%, rgba(217,214,214,0.3) 32%, rgba(217,214,214,0.3) 69%, rgba(4,4,4,0.3) 100%);
          height: 100%;
          display: flex;

          span {
            margin: auto 0.5rem 0.5rem 0.5rem;
            color: #ffffff;
          }
        }
      }
      &--footer {
        margin-top: 0.5rem;
      }
    }
  }
}

.header-modal{
    display: flex;
    justify-content: space-between;
    background: #000;
    padding: 10px;

    h5, span{
      color: white;
    }
}

.dimension{
    height: 500px;
    width:  700px;
    border: 1px solid;
  }
</style>
