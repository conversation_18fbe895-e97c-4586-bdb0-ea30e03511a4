export const modalMixin = {
	methods: {
		openModal(idModal) {
			const modal = document.getElementById(idModal);
			modal.classList.add("show");
			modal.style.display = 'block';

			modal.setAttribute("aria-modal", "true");
			modal.setAttribute('role', 'dialog');
			modal.removeAttribute('aria-hidden');
			modal.setAttribute('style', 'display: block; padding-right: 17px;');
		},

		closeModal(idModal){
			const modal = document.getElementById(idModal);
			modal.classList.remove("show");
			modal.style.display = 'none';

			modal.setAttribute("aria-modal", "false");
			modal.setAttribute('role', 'dialog');
			modal.setAttribute('aria-hidden', 'true');
			modal.setAttribute('style', 'display: none;');
			modal.removeAttribute('aria-modal');
			modal.removeAttribute('role');
			modal.removeAttribute('style');

			// Elimina la propiedad backdrop-filter del CSS
			const modalBackdrop = document.querySelector('.modal-backdrop');
			modalBackdrop?.parentNode?.removeChild(modalBackdrop);
			modalBackdrop?.remove();

			//agregar scroll al body
			document.body.removeAttribute("style");
			document.body.classList.remove("modal-open");	

			// Elimina el modal del DOM
			
		}
	},
};
