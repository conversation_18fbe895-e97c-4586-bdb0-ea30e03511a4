/**
 * Vue mixin for task queue functionality
 * Provides methods to enqueue tasks from any Vue component
 */
import TaskQueueService from '../common/services/TaskQueueService';

export default {
  methods: {
    /**
     * Enqueues a task and handles the response
     * @param {Object} options - Task configuration options
     * @param {string} options.url - The API endpoint
     * @param {Object} [options.data={}] - The data to send with the request
     * @param {Object} [options.messages] - Custom success/error messages
     * @param {Function} [options.onSuccess] - Success callback
     * @param {Function} [options.onError] - Error callback
     * @returns {Promise} - The request promise
     */
    enqueueTask(options) {
      // Add the toast instance to the options
      const taskOptions = {
        ...options,
        toast: this.$toast
      };
      
      return TaskQueueService.enqueueTask(taskOptions);
    },
    
    // /**
    //  * Enqueues a stats export task
    //  * @param {string} endpoint - The stats export endpoint
    //  * @param {Object} data - Filter data for the export
    //  * @param {Object} [messages] - Custom success/error messages
    //  * @returns {Promise} - The request promise
    //  */
    // enqueueStatsExport(endpoint, data, messages = {}) {
    //   const defaultMessages = {
    //     success: this.$t ? this.$t('EXPORT.SUCCESS') : "Export task queued successfully",
    //     error: this.$t ? this.$t('EXPORT.ERROR') : "Failed to generate the report"
    //   };
      
    //   return this.enqueueTask({
    //     url: endpoint,
    //     data,
    //     messages: { ...defaultMessages, ...messages }
    //   });
    // },
    
    // /**
    //  * Enqueues a course stats export task
    //  * @param {number|string} courseId - The course ID
    //  * @param {Object} data - Filter data for the export
    //  * @returns {Promise} - The request promise
    //  */
    // enqueueCourseStatsExport(courseId, data) {
    //   const endpoint = `/admin/api/v1/course-stats/${courseId}/xlsx`;
    //   const messages = {
    //     success: this.$t ? 
    //       `${this.$t('COURSE_STATS.EXPORT.SUCCESS')}<br/>(${this.$t('COURSE_STATS.EXPORT.ZIP_DIR')})` : 
    //       "Export task queued successfully"
    //   };
      
    //   return this.enqueueStatsExport(endpoint, data, messages);
    // },
    
    // /**
    //  * Enqueues an itinerary users export task
    //  * @param {number|string} itineraryId - The itinerary ID
    //  * @param {Object} data - Filter data for the export
    //  * @returns {Promise} - The request promise
    //  */
    // enqueueItineraryUsersExport(itineraryId, data) {
    //   const endpoint = `/admin/itinerary/${itineraryId}/export-users`;
    //   const messages = {
    //     success: this.translations?.export?.success || "Export task queued successfully",
    //     error: this.translations?.export?.failure || "Failed to generate the report"
    //   };
      
    //   return this.enqueueStatsExport(endpoint, data, messages);
    // }
  }
};