import {make} from "vuex-pathify";
import axios from "axios";
const INIT_ENDPOINT = '/admin/settings-saml'

const state = {
    loading: false,
    saving: false,
    basicConfiguration: {
        enabled: false,
        destination: '',
        issuer: '',
        logout_url: ''
    },
    loadingUserConfiguration: false,
    userConfiguration: {
        requiredAttributes: {},
        optionalAttributes: {},
        userExtraAttributes: {},
        mainProperty: 'email'
    },
    userFiltersAttributes: {},
    roles: {
        enabled: false,
        attribute: '',
        roles: {}
    },
    signatureValidation: {
        enabled: false,
        certGenerated: false
    }
};

const getters = {
    ...make.getters(state)
};

const mutations = {
    ...make.mutations(state),
    SET_CERTIFICATE_GENERATED(state, generated = false) {
        state.signatureValidation.certGenerated = generated;
    }
};

const actions = {
    ...make.actions(state),
    enableSaml({ commit }, enable)
    {
        return axios.post(`${INIT_ENDPOINT}/enable`, { enable }).then(r => (r.data))
            .catch(e => ({
                error: true,
                data: 'Request failed'
            }));
    },

    loadGeneralInfoConfigurations({ commit }) {
        commit('SET_LOADING', true);
        axios.get(`${INIT_ENDPOINT}/basic-configuration`).then(r => {
            const { basicConfiguration, signatureValidation } = r.data.data;
            commit('SET_BASIC_CONFIGURATION', basicConfiguration);
            commit('SET_SIGNATURE_VALIDATION', signatureValidation);
            //basicConfiguration: {
            //         enabled: false,
            //         destination: '',
            //         issuer: '',
            //     },
            //     signatureValidation: {
            //         enabled: false,
            //         certGenerated: false
            //     }
        }).finally(() => {
            commit('SET_LOADING', false);
        });
    },

    loadUsersConfiguration({ commit })
    {
        commit('SET_LOADING_USER_CONFIGURATION', true);
        axios.get(`${INIT_ENDPOINT}/users`).then(r => {
            const { data } = r.data;
            const { requiredAttributes, optionalAttributes, userExtraAttributes, mainProperty, roles} = data;

            commit('SET_USER_CONFIGURATION', {
                requiredAttributes,
                optionalAttributes,
                userExtraAttributes : Object.keys(userExtraAttributes).length === 0 ? {} : userExtraAttributes,
                mainProperty
            });
            commit('SET_ROLES', roles);
        }).finally(() => {
            commit('SET_LOADING_USER_CONFIGURATION', false);
        });
    },

    loadFiltersConfiguration({ commit }) {
        commit('SET_LOADING', true);
        axios.get(`${INIT_ENDPOINT}/filters`).then(r => {
            const { data } = r.data;
            commit('SET_USER_FILTERS_ATTRIBUTES', data);
        }).finally(() => {
            commit('SET_LOADING', false);
        });
    },

    saveConfiguration({ commit }, data)
    {
        commit('SET_SAVING', true);
        return axios.post(`${INIT_ENDPOINT}/save-configuration`, data).then(r => (r.data))
            .catch(e => ({
                error: true,
                data: 'Request failed'
            }))
            .finally(() => {
                commit('SET_SAVING', false);
            });
    },

    saveBasicConfig({ commit }, data)
    {
        commit('SET_SAVING', true);
        return axios.post(`${INIT_ENDPOINT}/save-basic-config`, data).then(r => (r.data))
            .catch(e => ({
                error: true,
                data: 'Request failed'
            }))
            .finally(() => {
                commit('SET_SAVING', false);
            });
    },

    saveRoleConfiguration({ getters, commit })
    {
        commit('SET_SAVING', true);
        const roles = getters['roles'];
        return axios.post(`${INIT_ENDPOINT}/save-role-configuration`, roles).then(r => (r.data))
            .catch(() => ({
                error: true,
                data: 'Request failed'
            }))
            .finally(() => {
                commit('SET_SAVING', false);
            });
    },

    uploadIdpMetadata({ commit, getters }, formData) {
        const headers = {
            'Content-Type': 'multipart/form-data',
        };
        return axios.post(`${INIT_ENDPOINT}/upload-idp-metadata`, formData, { headers }).then(r => {
            const { data, error } = r.data;
            if (!error) {
                const { basicConfiguration } = getters;
                console.log(basicConfiguration);
                basicConfiguration.destination = data.singleSignOnUrl;
                commit('SET_BASIC_CONFIGURATION', basicConfiguration);
            }
        })
    },

    uploadIdpCertificate({}, formData) {
        const headers = {
            'Content-Type': 'multipart/form-data',
        };
        return axios.post(`${INIT_ENDPOINT}/upload-idp-certificate`, formData, { headers })
            .then(r => (r.data));
    },

    activateSignatureValidation({}, enabled) {
        return axios.post(`${INIT_ENDPOINT}/activate-signature-validation`, { enabled} )
            .then(r => (r.data));
    },

    generateSigningCertificate({ commit }) {
        axios.post(`${INIT_ENDPOINT}/generate-signing-certificate`).then(r => {
            const created = r.status === 201;
            commit('SET_CERTIFICATE_GENERATED', created);
        })
    }
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
