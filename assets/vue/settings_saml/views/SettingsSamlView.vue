<script>
import RequiredConfig from "../components/RequiredConfig.vue";
import {get, sync} from "vuex-pathify";
import UserAttributes from "../components/UserAttributes.vue";
import FilterAttributes from "../components/FilterAttributes.vue";
import Spinner from "../../base/BaseSpinner.vue";
import Loader from "../../admin/components/Loader.vue";

export default {
  name: "SettingsSamlView",
  components: {Loader, Spinner, FilterAttributes, UserAttributes, RequiredConfig},
  data() {
    return {
      activePane: 'general'
    };
  },
  computed: {
    loading: get('settingsSamlModule/loading'),
    saving: get('settingsSamlModule/saving'),
    samlEnabled: get('settingsSamlModule/basicConfiguration@enabled'),
    rolesValues: sync("settingsSamlModule/roles@roles"),
    userRoles: get('configModule/config@userRoles'),
  },
  created() {
    this.$store.dispatch('settingsSamlModule/loadGeneralInfoConfigurations');
    this.$store.dispatch('settingsSamlModule/loadUsersConfiguration');
    this.$store.dispatch('settingsSamlModule/loadFiltersConfiguration');
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: 'Configuración SAML',
        params: {}
      }
    });
  }
}
</script>

<template>
  <div class="col-12 pt-3">
    <div class="col-12 d-flex align-content-center justify-content-center" v-if="saving">
      <loader :is-loaded="saving" style="padding: .1rem !important;"/>
    </div>
    <ul class="nav nav-tabs ps-4">
      <li class="nav-item" role="presentation">
        <button
            class="nav-link"
            :class="activePane === 'general' ? 'active' : ''"
            id="info-tab"
            @click="activePane = 'general'"
        >
          <i class="fa fa-file"></i> Info basica
        </button>
      </li>

      <li class="nav-item" role="presentation" v-if="samlEnabled">
        <button
            class="nav-link"
            :class="activePane === 'users' ? 'active' : ''"
            id="info-tab"
            @click="activePane = 'users'"
        >
          <i class="fa fa-file"></i> Usuarios
        </button>
      </li>

      <li class="nav-item" role="presentation" v-if="samlEnabled">
        <button
            class="nav-link"
            :class="activePane === 'filters' ? 'active' : ''"
            id="info-tab"
            @click="activePane = 'filters'"
        >
          <i class="fa fa-file"></i> Filtros
        </button>
      </li>
    </ul>
    <div class="tab-content bg-white p-1 p-sm-4">
      <div class="tab-pane active" v-if="loading">
        <spinner />
      </div>
      <div class="tab-pane active" v-else>
        <required-config v-if="activePane === 'general'"/>
        <user-attributes v-if="activePane === 'users'" />
        <filter-attributes v-if="activePane === 'filters'" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
