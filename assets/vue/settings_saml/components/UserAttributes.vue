<script>
import {get, sync} from "vuex-pathify";
import codesMixin from "../codesMixin";
import ButtonWithDescription from "../../common/components/ButtonWithDescription.vue";
import Multiselect from "vue-multiselect";
import 'vue-multiselect/dist/vue-multiselect.min.css'
import UserExtraFields from "./user/UserExtraFields.vue";
import Spinner from "../../base/BaseSpinner.vue";

export default {
  name: "UserAttributes",
  components: {Spinner, UserExtraFields, ButtonWithDescription, Multiselect},
  mixins: [codesMixin],
  data() {
    return {
      selectedProperty: '',
      selectedUserRole: '',
      newRolValue: ''
    };
  },
  computed: {
    saving: get('settingsSamlModule/saving'),
    loading: sync("settingsSamlModule/loading"),
    loadingUserConfiguration: get('settingsSamlModule/loadingUserConfiguration'),
    requiredAttributes: sync("settingsSamlModule/userConfiguration@requiredAttributes"),
    mainProperty: sync("settingsSamlModule/userConfiguration@mainProperty"),
    sourceProperties: get('configModule/config@userProperties'),
    mainPropertyValues () {
      return ['code', 'email'];
    },
    availableProperties() {
      return this.sourceProperties.filter(p => this.requiredAttributes[p] == null);
    },

    // Role configuration
    userRoles: get('configModule/config@userRoles'),
    enableRole: sync("settingsSamlModule/roles@enabled"),
    roleAttribute: sync("settingsSamlModule/roles@attribute"),
    rolesValues: sync("settingsSamlModule/roles@roles"),
  },
  mounted() {
    const keys = Object.keys(this.userRoles);
    keys.forEach(k => {
      if (this.rolesValues[k] == null) this.rolesValues[k] = [];
    })
  },
  methods: {
    add()
    {
      this.requiredAttributes[this.selectedProperty + ''] = '';
      this.selectedProperty = '';
    },

    saveUserConfiguration() {
      this.saveConfiguration({
        code: this.codeUserRequiredAttrs,
        value: this.requiredAttributes,
        mainProperty: this.mainProperty
      });
    },

    // saveConfiguration(code, value)
    saveConfiguration(data)
    {
      this.$store.dispatch('settingsSamlModule/saveConfiguration', data).then(r => {
        const { error } = r;
        if (error) this.$toast.error('Error al guardar');
        else this.$toast.success('Cambios guardados')
      })
    },

    // principal email -> code opcional
    // si principal code -> email opcional (si viene vacio crear user[employeeid]@user.com
    isDeletable(property) {
      switch (property){
        case 'email':case 'firstName': case 'lastName': case 'code':
          return false;
        default:
          return property !== this.mainProperty;
      }
    },

    isEditable(property) {
      switch (property){
        case 'email':case 'firstName': case 'lastName': case 'code':
          return false;
        default:
          return true;
      }
    },

    validateMainProperty() {
      if (this.requiredAttributes[this.mainProperty] == null) this.requiredAttributes[this.mainProperty] = '';
    },

    deleteProperty(property) {
      let attrs = this.requiredAttributes;
      delete attrs[property + ''];
      this.requiredAttributes = attrs;
    },

    addNewRoleValue() {
      if (this.newRolValue.length < 1) return;
      this.rolesValues[this.selectedUserRole].push(this.newRolValue);
      this.newRolValue = '';
    },

    saveRoleConfiguration() {
      this.$store.dispatch('settingsSamlModule/saveRoleConfiguration').then(r => {
        const { error, data} = r;
        this.$toast.clear();
        if (error) this.$toast.error(data);
        else this.$toast.success('Cambios aplicados');
      })
    }
  }
}
</script>

<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loadingUserConfiguration">
    <spinner />
  </div>
  <div class="col-12" v-else>
    <form @submit.prevent="saveUserConfiguration()" class="col-12 UserConfiguration">
      <h5 class="w-100 text-center">Atributos y propiedades: Relación entre Usuario y atributos SAML</h5>
      <div class="col-12 d-flex flex-row flex-nowrap gap-1">
        <div class="form-group col-md-4 mr-auto p-0 m-0">
          <label>Propiedad principal a usar</label>
          <div class="w-100 d-flex flex-row flex-nowrap gap-1 align-content-center p-1">
            <strong class="text-center" :class="mainProperty === 'email' ? 'color-primary' : ''">email</strong>
              <div class="form-check form-switch pl-0">
                <input class="form-check-input" type="checkbox" role="switch"
                       id="flexSwitchCheckDefault"
                       true-value="code" false-value="email"
                       v-model="mainProperty"
                >
                <label class="form-check-label" for="flexSwitchCheckDefault"></label>
              </div>
            <strong class="text-center" :class="mainProperty === 'code' ? 'color-primary' : ''">code</strong>
          </div>
        </div>
        <div class="col-md-4 ml-auto">
          <Multiselect
              v-model="selectedProperty"
              :options="availableProperties"
              :searchable="true"
              :placeholder="$t('MULTISELECT.PLACEHOLDER')"
              :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
          ></Multiselect>
          <button class="btn btn-primary" type="button" @click="add()"><i class="fa fa-plus"></i> Agregar</button>
        </div>

      </div>
      <div class="w-100 d-flex flex-row flex-wrap">
        <div class="col-xs-12 col-md-3 d-flex flex-row flex-nowrap" v-for="(attribute, property) in requiredAttributes">
          <div class="form-group w-100 mb-0 p-0">
            <label>{{ property }}</label>
            <input :name="property" required type="text"
                   class="form-control" placeholder="Nombre del atributo"
                   v-model.trim="requiredAttributes[property]" :readonly="!isEditable(property)">
          </div>
          <div v-if="isDeletable(property)" class="mt-auto">
            <button type="button" class="btn btn-danger" @click="deleteProperty(property)"><i class="fa fa-trash"></i></button>
          </div>
        </div>
      </div>
      <div class="col-12 d-flex flex-row justify-content-end">
        <button :disabled="saving" type="submit"
                class="btn btn-primary"><i class="fa fa-save"></i> Aplicar cambios</button>
      </div>
    </form>

    <user-extra-fields />

    <form @submit.prevent="saveRoleConfiguration()" class="RoleConfiguration">
      <h5 class="text-center w-100">Configuración de roles</h5>
      <div class="col-12 d-flex flex-row flex-wrap mt-1">
        <div class="col-xs-12 col-md-6">
          <button-with-description
              name="enable-saml"
              title="Lectura ROLE desde atributo"
              description="Permitir lectura del rol en datos saml"
              v-model="enableRole"/>
        </div>

        <div class="col-xs-12 col-md-6" v-if="enableRole">
          <div class="w-100 form-group p-0 mb-0">
            <label>Atributo a leer el rol</label>
            <input required type="text" class="form-control" v-model="roleAttribute">
          </div>
        </div>

        <div class="w-100 form-group RoleConfiguration--selection" v-if="enableRole">
          <div class="d-grid">
            <button type="button" class="btn" :class="selectedUserRole === roleKey ? 'btn-primary' : 'btn-default'"
                    v-for="(roleName, roleKey) in userRoles" :key="roleKey" @click="selectedUserRole = roleKey"
            >
              {{ $t(roleName.toUpperCase()) }}
            </button>
          </div>
          <div class="RoleConfiguration--selection--values">
            <div class="RoleConfiguration--selection--values--item-list" v-if="selectedUserRole.length > 0">
              <span class="rol-value-item" v-for="(value, i) in rolesValues[selectedUserRole]">
                {{ value }}
                <i class="text-danger ml-1" @click="">&times;</i>
              </span>
            </div>
            <div class="form-group">
              <input type="text" class="form-control" v-model="newRolValue"
                     placeholder="Enter para agregar valor" @keydown.enter.prevent="addNewRoleValue()">
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 d-flex justify-content-end">
        <button :disabled="saving" type="submit"
                class="btn btn-primary"><i class="fa fa-save"></i> Aplicar cambios</button>
      </div>
    </form>
  </div>
</template>

<style scoped lang="scss">
.color-primary {
  color: var(--color-primary);
}
form {
  border: 1px dashed var(--color-primary);
  padding: 1rem;
  border-radius: 5px;
  margin-bottom: 1rem;
}
.RoleConfiguration {
  &--selection {
    width: 100%;
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: .5rem;

    &--values {
      width: 100%;
      padding: 1rem 0;
      &--item-list {
        width: 100%;
        display: flex;
        flex-flow: row wrap;
        align-items: flex-start;
        justify-content: flex-start;
        gap: .25rem;
        max-height: 250px;
        overflow: auto;

        .rol-value-item {
          border: 1px solid var(--color-primary);
          padding: .1rem .25rem;
          border-radius: 5px;
          i {
            cursor: pointer;
            &:hover {
              color: #ff0000;
              transform: scale(1.1);
              transition: all ease-in-out .2s;
            }
          }
        }
      }
      .form-group {
        margin-top: auto;
      }
    }
  }
}
</style>
