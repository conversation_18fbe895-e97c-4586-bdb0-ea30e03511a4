export default class ChapterDetailsData {
  constructor(type) { this.type = type; }

  get detailsType() {
    if (this.type === 'MemoryMatch') return 'CoupleType'
    if ([
      'HigherLower',
      'SearchWord',
      'FillGaps',
      'GuessWord',
      '<PERSON><PERSON>',
      'HiddenPic',
      'HiddenWords',
      'TrueOrFalse',
      'LettersWheel'
    ].includes(this.type)) return 'StatementType'
    return 'DefaultType'
  }

  get showAnswerEnum() {
    return !!(['HigherLower', 'SearchWord', 'FillGaps'].includes(this.type))
  }
}