import ChapterDetailsData from './ChapterDetailsData'

function twoDigits(number = 0) {
  return String(number).padStart(2, '0')
}
export function secondsToTime(totalSeconds) {
  const hours = Math.trunc(totalSeconds / 3600)
  let seconds = totalSeconds - (hours * 3600)
  const minutes = Math.trunc(seconds / 60)
  seconds = seconds - (minutes * 60)

  return `${twoDigits(hours)}h ${twoDigits(minutes)}m ${twoDigits(seconds)}s`
}

function getStatusData(status = '') {
  const STATUS_LIST = {
    finished: { className: 'badge badge-success', textKey: 'FINISHED' },
    started: { className: 'badge badge-warning', textKey: 'IN_PROCESS' },
    no_started: { className: 'badge badge-light', textKey: 'NO_STARTING' }
  }
  return STATUS_LIST[status || 'no_started'] || STATUS_LIST.no_started
}

function getStringDateTime(date) {
  if (isNaN(date.getTime())) return '- -'

  const dateString = `${twoDigits(date.getDate())}-${twoDigits(date.getMonth() + 1)}-${date.getFullYear()}`
  const timeString = `${twoDigits(date.getHours())}:${twoDigits(date.getMinutes())}`
  return `${dateString} ${timeString}`
}

export default class UserStatsModel {
  constructor({
    id = 0,
    email = '',
    firstname = '',
    lastname = '',
    chapterStarted = 0,
    chaptersFinished = 0,
    totalChapters = 0,
    chapterDetails = [],
    urlView = '',
    type = '',
    category = '',
    start = '',
    end = '',
    survey = '',
    course = {}
  } = {}) {
    this.id = id || 0
    this.email = email || ''
    this.firstName = firstname || ''
    this.lastName = lastname || ''
    this.chapterStarted = chapterStarted || 0
    this.chaptersFinished = chaptersFinished || 0
    this.totalChapters = totalChapters || 0
    this.status = String('finished' || '').toLowerCase()
    this.chapterDetails = (chapterDetails || []).map((chapter) => new chapterDetailsModel(chapter))
    this.urlView = urlView || ''
    this.type = type || ''
    this.category = category || ''
    this.startString = start || ''
    this.endString = end || ''
    this.surveyString = survey || ''
    this.start = new Date(start || '-')
    this.end = new Date(end || '-')
    const surveyDate = isNaN(this.start.getTime()) || isNaN(this.end.getTime()) ? '-' : survey;
    this.survey = new Date(surveyDate)
    this.course = course
  }
  get fullName() {
    return `${this.firstName} ${this.lastName}`.trim()
  }
  get progress() {
    if (!this.totalChapters) return 0;
    return +((this.chapterStarted ? this.chapterStarted : this.chaptersFinished) * 100 / this.totalChapters).toFixed(2)
  }
  get statusData() {
    return getStatusData(this.status)
  }
  get isFinished() {
    return this.status === 'finished'
  }

  get startDateTime() { return getStringDateTime(this.start).split(' ') }
  get endDateTime() { return getStringDateTime(this.end).split(' ') }
  get surveyDateTime() { return getStringDateTime(this.survey).split(' ') }
}

export class chapterDetailsModel extends ChapterDetailsData {
  constructor({
    name = '',
    type = '',
    icon = '',
    start = '',
    end = '',
    status = '',
    image = '',
    timeSpent = undefined,
    attempts = []
  } = {}) {
    super(type || '')
    this.name = name || ''
    this.icon = icon || ''
    this.start = new Date(start || '')
    this.end = new Date(end || '')
    if (timeSpent !== undefined) this.timeDiff = timeSpent || 0
    else this.timeDiff = (isNaN(this.start.getTime()) || isNaN(this.end.getTime())) ? 0 : ((this.end.getTime() - this.start.getTime()) / 1000)
    this.status = String(status || '').toLowerCase()
    this.image = image || ''
    this.attempts = (attempts || []).map((attempt) => new attemptsDetailsModel(attempt))
  }

  get time() {
    return secondsToTime(this.timeDiff)
  }
  get statusData() {
    return getStatusData(this.status)
  }
  get startDateTimeArray() {
    return getStringDateTime(this.start).split(' ')
  }
  get endDateTimeArray() {
    return getStringDateTime(this.end).split(' ')
  }

  get showAttemptsCardDetails() {
    return this.type !== 'VideoQuiz'
  }
}

class attemptsDetailsModel {
  constructor({
     state = '',
     start = '',
     end = '',
     questions = []
  } = {}) {
    this.success = String(state || '').toLowerCase() === 'ok'
    this.start = new Date(start || '')
    this.end = new Date(end || '')
    this.questions = (questions || []).map(question => new attemptQuestions(question))
  }

  get time() {
    if (isNaN(this.start.getTime()) || isNaN(this.end.getTime())) return '00h 00m 00s...'
    return secondsToTime((this.end.getTime() - this.start.getTime()) / 1000)
  }
  get startDateTimeArray() {
    return getStringDateTime(this.start).split(' ')
  }
  get endDateTimeArray() {
    return getStringDateTime(this.end).split(' ')
  }
}

export class attemptQuestions {
  constructor({ question = '', answers = [] } = {}) {
    this.text = `${question}`.replace(/\[(.*?)]/g, '______')
    this.answers = (answers || []).map((answer) => ({
      id: answer.id,
      text: answer.hasOwnProperty('userAnswer') ? (answer.userAnswer || '-') : answer.answer,
      answer: answer.answer,
      correct: answer.correct,
      incorrect: answer.incorrect
    }))
  }
}