<template>
  <div class="CourseStatsOpinions">
    <div class="row mx-0">
      <div
        class="col-12 pt-3 d-flex flex-wrap align-items-center justify-content-between"
      >
        <span v-if="loadingData" :class="{ 'm-auto': !totalItems }"
          >{{ $t("LOADING") }}...</span
        >
        <span v-else class="m-auto">{{
          totalItems ? "" : $t("ANNOUNCEMENT.OPINIONSTAB.NOT_FOUND")
        }}</span>
        <b v-show="totalItems">{{
          $t("COMMON.PAGINATION_INFO", [items.length, totalItems])
        }}</b>
      </div>
      <div
        v-for="item in items"
        :key="'opinion_' + item.id + item.created_at"
        class="col-lg-4 col-md-6 col-sm-12 pt-3"
      >
        <Opinion
          :opinion="item"
          class="h-100"
          :read-only="readOnly"
          @open-details="setCurrentOpinion"
          @on-opinion-visibility-change="changeOpinionVisibility"
          @on-opinion-highlight-change="changeOpinionHighlight"
        />
      </div>
      <div class="col-12">
        <Pagination
          :total-items="totalItems"
          :page-size="pageSize"
          :prop-current-page="currentPage"
          :disabled="loadingData"
          @current-page="setCurrentPage"
        />
      </div>
      <OpinionDetails :opinion="currentOpinion" />
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Opinion from "../../common/components/opinions/Opinion.vue";
import initStore from "../../../vue/common/store";
import Pagination from "../components/Pagination.vue";
import OpinionDetails from "../../admin/components/Opinions/OpinionDetails.vue";
import opinionModule from "../../opinions/store/module/opinionModule";

const store = initStore({
  modules: {
    opinionModule
  }
});
export default {
  name: "CourseStatsOpinions",
  store,
  props: {
    readOnly: { type: Boolean, default: false }
  },
  components: { OpinionDetails, Pagination, Opinion },
  data: () => ({
    currentPage: 1,
    pageSize: 12,
    items: [],
    totalItems: 0,
    loadingData: false,
    currentOpinion: {},
  }),
  computed: {
    courseId () {
      return this.$route?.params?.id || courseId
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      if (this.loadingData) return null;
      this.loadingData = true;
      let tabCall = "CourseStatsOpinions";
      const { data } = await axios.get(
        `admin/nps/opinions?page=${this.currentPage}&pageSize=${this.pageSize}&courseId=${this.courseId}&tabCall=${tabCall}`
      );
      this.items = (data?.data?.items || []).map(item => {
        item.visibleOptions = !(!item.text.length && data.hideEmptyOpinions)
        return item;
      });
      this.totalItems = data?.data?.["total-items"] || 0;
      this.loadingData = false;
    },
    setCurrentPage(value) {
      if (this.loadingData) return null;
      this.currentPage = value;
      this.loadData();
    },
    setCurrentOpinion(opinion = {}) {
      this.currentOpinion = { ...opinion }
    },
    changeOpinionVisibility(opinion) {
      console.log(opinion)
      this.$store
        .dispatch("opinionModule/setOpinionVisibility", {
          id: opinion.id,
          toPost: opinion.visible,
        })
        .then((res) => {
          const { error } = res;
          if (error) this.$toast.error(this.$t("OPINION.TO_POST.FAILED") + "");
          else this.$toast.success(this.$t("OPINION.TO_POST.SUCCESS") + "");
        });
    },
    changeOpinionHighlight(opinion) {
      this.$store
        .dispatch("opinionModule/setOpinionHighlight", {
          id: opinion.id,
          toPost: opinion.highlight,
        })
        .then((res) => {
          const { error } = res;
          if (error)
            this.$toast.error(this.$t("OPINION.HIGHLIGHT.FAILED") + "");
          else this.$toast.success(this.$t("OPINION.HIGHLIGHT.SUCCESS") + "");
        });
    },
  }
}
</script>
<style lang="scss"  scoped>
@import url('../../../../assets/css/courseStats.scss');
</style>
