<template>
  <div class="CourseStatsFilter">
    <div class="text-right pt-3 px-3 pb-0">
      <button
        class="btn btn-sm btn-primary"
        @click="showFilters = !showFilters"
      >
        <i class="fa fa-filter" /> {{ $t("USER.LABEL.FILTERS") }}
      </button>
    </div>
    <div class="course-filters p-3 my-3" v-show="showFilters">
      <div class="row">
        <div
          class="col-md-4 col-sm-12 mt-2"
          v-for="item in filterList"
          :key="item.key"
        >
          <label class="mb-0" :for="name + item.key">{{ item.name }}</label>
          <select
            v-if="item.filters.length "
            :id="name + item.key"
            v-model="formData[item.key]"
            class="form-control"
          >
            <option value="" selected="selected">{{ $t("SELECT") }}</option>
            <option
              v-for="option in item.filters"
              :key="'filter_' + item.id + '_option_' + option.id"
              :value="option.id"
            >
              {{ option.name }}
            </option>
          </select>
          <input
            v-else
            :id="name + item.key"
            v-model="formData[item.key]"
            autocomplete="off"
            class="form-control"
          />
        </div>
        <div class="col-md-4 col-sm-12 mt-2">
          <label class="mb-0" for="dateFrom">{{
            $t("ANNOUNCEMENT.FORM.ENTITY.START_AT")
          }}</label>
          <input
            id="dateFrom"
            v-model="formData.dateFrom"
            type="date"
            autocomplete="off"
            class="form-control"
          />
        </div>
        <div class="col-md-4 col-sm-12 mt-2">
          <label class="mb-0" for="dateTo">{{
            $t("ANNOUNCEMENT.FORM.ENTITY.FINISH_AT")
          }}</label>
          <input
            id="dateTo"
            v-model="formData.dateTo"
            type="date"
            autocomplete="off"
            class="form-control"
          />
        </div>
        <div class="col-md-4 col-sm-12 mt-2">
          <label class="mb-0" for="dateTo">{{
            $t("USER.LABEL_IN_PLURAL")
          }}</label>
          <select v-model="formData.userStatus" class="form-control">
            <option value="all">{{ $t("USERS.ALL") }}</option>
            <option value="active" selected>{{ $t("USERS.ACTIVE") }}</option>
            <option value="inactive">{{ $t("USERS.INACTIVE") }}</option>
          </select>
        </div>

        <div class="row">
          <div
            class="col-md-4 col-sm-12 py-3 d-flex gap-2 align-self-center mt-2"
          >
            <BaseSwitch
              :tag="name + 'courseStartedIntime'"
              v-model="formData.courseStartedIntime"
              @input="setCourseStartedIntime"
            />
            <label :for="name + 'courseStartedIntime'" class="mb-0">{{
              translations?.course_started_in_period_title || ""
            }}</label>
          </div>
          <div
            class="col-md-4 col-sm-12 py-3 d-flex gap-2 align-self-center mt-2"
          >
            <BaseSwitch
              :tag="name + 'courseFinishedIntime'"
              v-model="formData.courseFinishedIntime"
              @input="setCourseFinishedIntime"
            />
            <label :for="name + 'courseFinishedIntime'" class="mb-0">{{
              translations?.course_finished_in_period_title || ""
            }}</label>
          </div>
        </div>

        <div class="col-4 mt-2 mb-4" v-if="announcementId === 0 && !itineraryId">
          <label class="my-0">{{ $t("SURVEY.AUDIENCE_COURSE") }}</label>
          <Multiselect
            :options="options"
            track-by="id"
            label="name"
            :multiple="true"
            :close-on-select="false"
            :preselect-first="true"
            :show-labels="false"
            :placeholder="$t('SELECT')"
            v-model="formData.source"
          >
            <template v-slot:noResult>{{
              $t("SURVEY.AUDIENCE_COURSE_NO_RESULTS")
            }}</template>
          </Multiselect>
        </div>

        <div class="col-md-4 col-sm-12 mt-2" style="margin-bottom: 20px;">
          <label class="mb-0" for="dateTo">{{
            $t("FILTER.USER.COURSE.STATE")
          }}</label>
          <select v-model="formData.userCourseStatus" class="form-control">
            <option value="" selected="selected">{{ $t("SELECT") }}</option>
            <option :value="courseStatus[0]">{{ $t("NO_STARTING") }}</option>
            <option :value="courseStatus[1]" selected>
              {{ $t("IN_PROCESS") }}
            </option>
            <option :value="courseStatus[2]">{{ $t("FINISHED") }}</option>
          </select>
        </div>

        <div class="col-4 mt-2 mb-4" v-if="!itineraryId">
          <label class="my-0">{{ $t("NAME") }} / {{ $t("USER.EMAIL") }} </label>
          <input
            id="nameMailSearch"
            type="text"
            class="form-control"
            v-model="formData.userNameMail"
            :placeholder="$t('NAME') + ' / ' + $t('USER.EMAIL')"
          />
        </div>
      </div>

      <div class="row">
        <div class="col-12">
          <button
            type="button"
            class="action-new btn btn-primary"
            :class="{ disabled: loadingData }"
            @click="submit"
          >
            <i
              class="fa mr-1"
              :class="loadingData ? 'fa-spinner' : 'fa-check'"
            ></i>
            {{ $t("APPLY_FILTERS") }}
          </button>
          <button
            type="button"
            class="action-new btn btn-danger"
            :class="{ disabled: loadingData }"
            @click="resetData"
          >
            <i
              class="fa mr-1"
              :class="loadingData ? 'fa-spinner' : 'fa-times'"
            ></i>
            {{ $t("CATALOG.SETTING.CLEAR_FILTERS") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseSwitch from "../../base/BaseSwitch.vue";
import Multiselect from "vue-multiselect";

export default {
  name: "CourseStatsFilter",
  components: { Multiselect, BaseSwitch },
  props: {
    translations: { type: Object, default: () => ({}) },
    course: { type: Object, default: () => ({ id: 0, locale: "es" }) },
    loadingData: { type: Boolean, default: false },
    name: { type: String, default: "" },
    announcementId: { type: Number, default: 0 },
    itineraryId: { type: String, default: undefined },
  },
  data: () => ({
    formData: {
      dateFrom: "",
      dateTo: "",
      userStatus: "active",
      courseStartedIntime: false,
      courseFinishedIntime: false,
      source: [],
      userName: null,
      userEmail: null,
      userCourseStatus: "",
    },
    downloadMessage: {
      open: false,
      success: true,
    },
    showFilters: false,
    options: [],
    courseStatus,
  }),
  computed: {
    filterList() {
      return filters || [];
    },
  },
  watch: {
    filterList() {
      this.filterList.forEach((item) => (this.formData[item.key] = ""));
    },
  },
  mounted() {
    this.filterList.forEach((item) => (this.formData[item.key] = ""));
    this.resetData();
    if (this.itineraryId) {
      this.formData.source = { id: "itinerary", name: this.$t("ITINERARY.LABEL.PLURAL") };
    }
    //Comment option of itinerary in the meanwhile
    this.options = [
      { id: "filter", name: this.$t("FILTERS") },
      {
        id: "open",
        name: `${this.$t("USERS.TABLE_TITLE2")} (${this.$t(
          "USER.LABEL.OPEN_CAMPUS"
        )})`,
      },
    ];
  },
  methods: {
    submit() {
      if (this.loadingData) return null;
      this.submitActiveFilters();
    },
    resetData() {
      if (this.loadingData) return null;
      Object.keys(this.formData).forEach((key) => {
        if (key === "userStatus") this.formData.userStatus = "active";
        else if (key === "courseStartedIntime")
          this.formData.courseStartedIntime = false;
        else if (key === "courseFinishedIntime")
          this.formData.courseFinishedIntime = false;
        else this.formData[key] = "";
      });
      this.submitActiveFilters();
    },
    setCourseStartedIntime(val) {
      this.formData.courseStartedIntime = val;
    },
    setCourseFinishedIntime(val) {
      this.formData.courseFinishedIntime = val;
    },
    submitActiveFilters() {
      this.$emit(
        "apply",
        Object.keys(this.formData)
          .filter(
            (key) =>
              !!(key === "source"
                ? this.formData.source.length
                : this.formData[key])
          )
          .reduce(
            (acc, cur) => ({
              ...acc,
              [cur]:
                cur === "source"
                  ? this.formData[cur].map((source) => source.id)
                  : `${this.formData[cur]}`,
            }),
            {}
          )
      );
    },
  },
};
</script>
<style scoped lang="scss">
select.form-control {
  appearance: auto;
}
</style>
