<template>
  <div class="defaultType">
    <p class="my-0">
      <b class="text-uppercase">{{indexValue}}{{ $t('SURVEY.STATEMENT') }}:</b> {{ question.text }}
    </p>
    <p v-if="correctAnswer.answer" class="my-0"><b class="text-uppercase">{{ $t('SURVEY.SOLUTION') }}:</b> {{ correctAnswer.answer }}</p>
    <ol v-show="showResponse" class="mt-1" type="A">
      <li
        v-for="(answer, answerIndex) in question.answers"
        class="ml-2 position-relative"
        :class="{'text-success': answer.correct, 'text-danger': answer.incorrect}"
        :key="'attempt_question_' + index + '_answer_' + answerIndex"
      >
        <span class="answer-icon" v-if="details">
          <i class="fa fa-check-circle text-success" v-if="answer.correct"/>
          <i class="fa fa-times-circle text-danger" v-else-if="answer.incorrect"/>
        </span>
        {{ answer.text }}
      </li>
    </ol>
  </div>
</template>

<script>
import { attemptQuestions } from '../../models/UserStatsModel'
import { GeneralStatsQuestionsModel } from '../../models/GeneralStatsModel'

export default {
  name: "defaultType",
  props: {
    index: { type: Number, default: 0 },
    details: { type: Boolean, default: false },
    showResponse: { type: Boolean, default: false },
    question: {
      type: [attemptQuestions, GeneralStatsQuestionsModel], // Puede ser de cualquiera de estos tipos
      default: function () {
        return new attemptQuestions(); // Valor por defecto
      }
    }
  },
  computed: {
    indexValue() {
      return this.index ? `${this.index}. ` : ''
    },
    correctAnswer() {
      return this.question.answers.find((answer) => answer.correct || answer.incorrect) || { answer: '' }
    }
  }
}
</script>