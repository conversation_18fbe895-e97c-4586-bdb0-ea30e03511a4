import axios  from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({
	loading   : false,
	videoQuiz : undefined,
	urlChapter: undefined,

});

const state = () => getDefaultState();

export const getters = {
	isLoading    : (state) => () => state.loading,
	getVideoQuiz : (state) => () => state.videoQuiz,
	getUrlChapter: (state) => () => state.urlChapter,
};

export const mutations = {
	...make.mutations(state),
};

export const actions = {
	setBlock(context, data) {
		const url = "/admin/chapter/videoquiz/set-block";
		const options = {
			headers: {'Content-Type': 'multipart/form-data'},
		};

		const formData = new FormData();
		formData.append('chapterId', data.chapterId);
		formData.append('title', data.title);
		formData.append('duration', data.duration);
		formData.append('url', data.url);

		const {result} = axios.post(url, formData, options);

		return result;
	},

	editBlock(context, data) {
		const url = "/admin/chapter/videoquiz/edit-line";
		const options = {
			headers: {'Content-Type': 'multipart/form-data'},
		};

		const formData = new FormData();
		formData.append('id', data.id);
		formData.append('title', data.title);
		formData.append('duration', data.duration);
		formData.append('url', data.url);

		const {result} = axios.post(url, formData, options);

		return result;
	},

	reloadBlock(context, data) {
		const url = "/admin/chapter/videoquiz/reload-block";
		return axios.post(url, data);
	},

	deleteLine(context, data) {
		const url = "/admin/chapter/videoquiz/delete-line";
		return axios.post(url, data);
	},

	reloadBlocksPreguntas(context, data) {
		const url = "/admin/chapter/videoquiz/reload-block-preguntas";
		return axios.post(url, data);
	},

	setBlockPreguntas(context, data) {
		const url = "/admin/chapter/videoquiz/set-block-preguntas";
		const options = {
			headers: {'Content-Type': 'multipart/form-data'},
		};

		const formData = new FormData();
		formData.append('videoquizId', data.videoquizId);
		formData.append('currenttime', data.currenttime);
		formData.append('texto', data.texto);
		formData.append('image', data.image);
		formData.append('respuestas', data.respuestas);

		const {result} = axios.post(url, formData, options);

		return result;
	},

	editBlockPreguntas(context, data) {
		const url = "/admin/chapter/videoquiz/edit-line-preguntas";
		const options = {
			headers: {'Content-Type': 'multipart/form-data'},
		};

		const formData = new FormData();
		formData.append('id', data.id);
		formData.append('currenttime', data.currenttime);
		formData.append('texto', data.texto);
		formData.append('image', data.image);
		formData.append('respuestas', data.respuestas);

		const {result} = axios.post(url, formData, options);

		return result;
	},

	deleteLinePreguntas(context, data) {
		const url = "/admin/chapter/videoquiz/delete-line-preguntas";
		return axios.post(url, data);
	},

	async fetchVideoQuiz({ commit }, idChapter) {
		try {
			commit('SET_LOADING', true);
			const url = `/admin/videoquiz/${idChapter}`;

			const { data } = await axios.get(url);

			commit('SET_VIDEO_QUIZ', data?.data?.videoQuiz);

			commit('SET_URL_CHAPTER', data?.data?.routeChapter);
		} catch (e) {
			console.log(e);
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async createVideoQuiz({commit}, request) {
		try {
			commit('SET_LOADING', true);

			console.log(request.domainVimeo);
			const url = `${request.domainVimeo}/admin/videoquiz/create`;
			const options = {
				headers: { "Content-Type": "multipart/form-data" },
			  };

			const { data } = await axios.post(url, request.formData, options);
			commit('SET_VIDEO_QUIZ', data?.data?.videoquiz);
			commit('SET_URL_CHAPTER', data?.data?.routeChapter);
		} catch (e) {
			console.log(e);
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async updateVideoQuiz({ commit }, request) {
		try {
			commit('SET_LOADING', true);
			const url = `/admin/videoquiz/update`;

			const options = {
				headers: { "Content-Type": "multipart/form-data" },
			  };

			const { data } = await axios.post(url, request.formData, options);
			commit('SET_VIDEO_QUIZ', data?.data?.videoquiz);
			commit('SET_URL_CHAPTER', data?.data?.routeChapter);
		} catch (e) {
			console.log(e);
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async deleteVideoQuiz({ commit }, formData) {
		try {
			commit('SET_LOADING', true);
			const url = `/admin/videoquiz/delete`;

			await axios.post(url, formData);
		}
		catch (e) {
			console.log(e);
		}
		finally {
			commit('SET_LOADING', false);
		}
	}
};

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions,
};
