import axios from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({
    courses: [],
    chapters: [],
});

const state = () => getDefaultState();

export const getters = {};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
    async fetchCourses({commit}, user) {
        const url = `/admin/user/${user}/courses`;
        let courses;

        try {
            const {data} = await axios.get(url);
            courses = data.data.courses;
            commit('SET_COURSES', courses);
        } finally {
        }

        return courses;
    },

    async fetchChapters({commit}, user) {
        const url = `/admin/user/${user}/chapters`;
        let chapters;

        try {
            const {data} = await axios.get(url);
            chapters = data.data.chapters;
            commit('SET_CHAPTERS', chapters);
        } finally {
        }

        return chapters;
    },

    async fetchMessages({commit}, user) {
        const url = `/admin/user/${user}/messages`;
        let messages;

        try {
            const {data} = await axios.get(url);
            messages = data.data.messages;
        } finally {
        }

        return messages;
    },


    async fetchCoursesList({commit}, user) {
        const url = `/admin/user/${user}/courses-list`;
        let courses;

        try {
            const {data} = await axios.get(url);
            courses = data.data.courses;
        } finally {
        }

        return courses;
    },


    async fetchUserLogins({commit}, user) {
        const url = `/admin/user/${user}/logins`;
        let logins;

        try {
            const {data} = await axios.get(url);
            logins = data.data.logins;
        } finally {
        }

        return logins;
    },

    async fetchTimeSpentByType({commit}, user) {
        const url = `/admin/user/${user}/time-spent`;
        let timeSpent;

        try {
            const {data} = await axios.get(url);
            timeSpent = data.data.time;
        } finally {
        }

        return timeSpent;
    },

    async fetchUserItinerary({commit}, user) {
        const url = `/admin/user/${user}/itinerary`;
        try {
            const {data} = await axios.get(url);
            return {
                itineraries: data.data.itineraries,
                tableTitlesTranslations: data.data.tableTitlesTranslations
            };
        } finally {

        }
    },

    async deleteUser({commit}, user) {
        const url = `/admin/user/${user}/delete`;
        try {
            const { data } =  await axios.delete(url);
            return data.url;
        } catch (e) {console.log(e)}
    },

    async fetchGeneralData({comit}, user) {
        try {
            const { data } = await axios.get(`/admin/user/detail-stats/${user}`)
            return data.data
        } catch (e) {}
    }
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
