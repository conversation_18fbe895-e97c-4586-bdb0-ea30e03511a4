import axios from "axios";
import {make} from 'vuex-pathify';

const state = {
    loading: false,
    categoryFilterOptions: undefined,
    allCourses: [],
};

export const getters = {};

export const mutations = {
...make.mutations(state),
};

export const actions = {
    async fetchItineraryCourses({ commit }, itineraryId) {
        try {
            const url = `/admin/itinerary/${itineraryId}/load-courses`;
            const {data, error} = await axios.get(url);
            if (error) throw new Error(data);

            const itineraryCourses = data?.data;
            // commit('SET_ITINERARY_COURSES', itineraryCourses);

            return itineraryCourses;
        } catch (error) {
            return undefined;
        }
    },

    async fetchCategoryFilterOptions({ commit }) {
        try {
            commit('SET_LOADING', true);

            const url = '/admin/itinerary/course-categories';
            const {data, error} = await axios.get(url);
            if (error) throw new Error(data);

            //console.log(data);
            const {categoryFilterOptions} = data?.data;
            commit('SET_CATEGORY_FILTER_OPTIONS', categoryFilterOptions);
        } catch (error) {
            // console.log(error);
        } finally {
            commit('SET_LOADING', false);
        }
    },

    async fetchAvailableCourses({ commit }) {
        try {
            commit('SET_LOADING', true);

            const url = '/admin/itinerary/find-courses';
            // TODO change to axios.get
            const {error, data} = await axios.post(url);
            if (error) throw new Error(data);

            const courses = data?.data;
            commit('SET_ALL_COURSES', courses);
        } catch (error) {
            // console.log(error);
        } finally {
            commit('SET_LOADING', false);
        }
    },

    async removeUser({commit}, params) {
        let result = null;
        try {
            result = await axios.post(params.url, params.data)
        } finally {

        }

        return result;

    },  
    

    async userProgressDetails({commit}, params) {
        let result = null;
        try {
         result =  await axios.get(`/admin/itinerary/${params.itineraryId}/user-proogress-details/${params.userId}`);

        } catch (error) {

        }

        return result;
    },

};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
