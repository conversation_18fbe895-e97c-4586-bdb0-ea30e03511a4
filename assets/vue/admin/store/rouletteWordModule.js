import axios from 'axios';
import { TrackOpTypes } from 'vue';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
    loading: false,
    letters: [],
    timeChapter: 0,
});

const PATH = 'admin/chapter/roulette-words';

const state = () => getDefaultState();

export const getters = {
    ...make.getters(state),

    getPreviousLetterAvailable: (state) => (letter) => {
        const letters = state.letters.map(l => l.letter);
        const orderLetters = [...letters].sort();
        const currentLetterIndex = orderLetters.findIndex(l => l === letter);
        const previousLetter = orderLetters[currentLetterIndex - 1] ?? null;

        return previousLetter;
    },

    getTimeChapter: (state) => () => state.timeChapter,    
};

export const mutations = {
    ...make.mutations(state),

    UPDATE_LETTER(state, letter) {
        const index = state.letters.findIndex(l => l.id === letter.id);
        const firstSlice = state.letters.slice(0, index);
        const lastSlice = state.letters.slice(index + 1);

        state.letters = [...firstSlice, letter, ...lastSlice];
    },

    ADD_LETTER(state, letter) {
        state.letters = [...state.letters, letter];
    },

    REMOVE_LETTER(state, letterId) {
        state.letters = state.letters.filter(l => l.id !== letterId);
    }
};

export const actions = {
    async setLetter({ commit }, letter) {
        try {
            commit('SET_LOADING', true);

            const { data:response } = await axios.post(`${PATH}/set-letter`, letter);
            const { data, error } = response;

            if (error) throw new Error(data);

            const action = (letter.id) ? 'UPDATE' : 'ADD';
            commit(`${action}_LETTER`, data.letter);

            window.Vue.$toast.success(data.message);
        } catch (e) {
            // console.log(e);
        } finally {
            commit('SET_LOADING', false);
        }

    },

    async removeLetter({ commit }, { letterId, chapterId }) {
        try {
            commit('SET_LOADING', true);

            const { data:response } = await axios.post(`${PATH}/remove-letter`, { letterId, chapterId });
            const { data, error } = response;

            if (error) throw new Error(data);

            commit('REMOVE_LETTER', letterId);

            window.Vue.$toast.success(data);
        } catch (e) {
            // console.log(e);
        } finally {
            commit('SET_LOADING', false);
        }
    },


    async fetchTimeChapter({ commit }, chapterId) {
        try {
            commit('SET_LOADING', true);
            console.log("obteniendo time chapter", chapterId);
                    

            const { data } = await axios.get(`admin/time-game/${chapterId}`);
            console.log("obteniendo time chapter",data?.data?.time);
           
            if (data.error) throw new Error(data.error);

            commit('SET_TIME_CHAPTER', data?.data?.time);
        } catch (error) {
            
        }
        finally {
            commit('SET_LOADING', false);
        }

    },

    async saveTimeChapter({ commit }, formaData) {
        try {
            commit('SET_LOADING', true);

            const { data } = await axios.post('admin/time-game/save', formaData);
            commit('SET_TIME_CHAPTER', data?.data?.time);        
        }
        catch (error) {
        }
        finally {  
            commit('SET_LOADING', false);
        }

    }
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
