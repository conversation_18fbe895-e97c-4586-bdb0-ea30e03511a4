import Vue from 'vue';
import store from './store';
import axios from "axios";
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';
import CategoryFilter from "./components/CategoryFilter.vue";

window.Vue  = new Vue({
    delimiters: ['${', '}'],
    components: {CategoryFilter},
    store,
    data() {
        return {
            managers: [],
            courseManagers: [],
            searchManager: '',

            professionalCategories: [],
            courseProfessionalCategories: [],
            searchProfessionalCategory: '',

            filterCategories: [],
            courseFilters: [],
            selectIdFilterCategory: null,
            filters: [],
            searchFilter: '',
            isLoaded: true,
        }
    },
    async created() {
        // await this.$store.dispatch('accessLevelModule/fetchFilterCategories', {})
        // .then(filterCategories => {
        //     this.filterCategories = filterCategories;
        //     if (filterCategories.length>0) {
        //         this.selectIdFilterCategory = filterCategories['0']['id'];
        //         this.getFilters(this.selectIdFilterCategory);
        //     }
        // });

        await this.$store.dispatch('accessLevelModule/fetchManagers', {})
            .then(managers => {
                this.managers = managers;
            });

        await this.$store.dispatch('accessLevelModule/fetchCourseManagers', {courseId})
            .then(managers => {
                this.courseManagers = managers;
            });


        await this.$store.dispatch('accessLevelModule/fetchProfessionalCategories', {})
            .then(professionalCategories => {
                this.professionalCategories = professionalCategories;
            });

        await this.$store.dispatch('accessLevelModule/fetchCourseProfessionalCategories', {courseId})
            .then(professionalCategories => {
                this.courseProfessionalCategories = professionalCategories;
            });
    },

    methods: {
        addManagerToCourse: function (manager) {
            if (this.courseManagers.find(courseManager => courseManager.id === manager.id) === undefined) {
                this.courseManagers.push(manager);
                this.courseManagers?.sort((a, b) => (a.fullName > b.fullName) ? 1 : -1);
            }

            window.Vue.$toast.success(
                `Manager ${manager.fullName} Agregado`
                );

            this.save();
            window.scrollTo(0,document.body.scrollHeight);
        },

        removeManagerFromCourse: function(manager) {
            let index = this.courseManagers.findIndex(courseManager => courseManager.id === manager.id);
            this.courseManagers.splice(index, 1);

            window.Vue.$toast.warning(
                `Manager ${manager.fullName} removido`
                );

            this.save();
            window.scrollTo(0,document.body.scrollHeight);

            this.save();
        },

        addProfessionalCategoryToCourse: function (professionalCategory) {
            if (this.courseProfessionalCategories.find(courseProfessionalCategory => courseProfessionalCategory.id === professionalCategory.id) === undefined) {
                this.courseProfessionalCategories.push(professionalCategory);
            }
        },

        removeProfessionalCategoryFromCourse: function(professionalCategory) {
            let index = this.courseProfessionalCategories.findIndex(courseProfessionalCategory => courseProfessionalCategory.id === professionalCategory.id);
            this.courseProfessionalCategories.splice(index, 1);
        },


        save: async function(){
            let payload = {
                course: courseId,
                managers: this.courseManagers,
                professionalCategories: this.courseProfessionalCategories,
                selectIdFilterCategory: this.selectIdFilterCategory,
                courseFilters: this.courseFilters,
            }

            await axios.post('/admin/courses/save-access-level', payload);
                /* .then(response => {
                    location.reload();
                }) */
        },

        normalize(string) {
            return string.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, "")
        },

        // getFilters: async function(idFilterCategory) {
        //     this.selectIdFilterCategory = idFilterCategory;
        //
        //     let url = '/admin/filters/categories/' + this.selectIdFilterCategory;
        //     await axios.get(url)
        //         .then(response => {
        //             this.isLoaded = true;
        //             this.filters = response.data.data.filters;
        //             this.getCourseFilters(courseId,this.selectIdFilterCategory);
        //         })
        // },

        // getCourseFilters: async function(courseId,filterCategoryId){
        //     let url = '/admin/courses/' + courseId + '/filters/' + filterCategoryId;
        //     await axios.get(url)
        //         .then(response => {
        //             this.courseFilters = response.data.data.courseFilters;
        //             this.isLoaded = false;
        //         })
        // },

        // addFilterToCourse: function (filter) {
        //     if (this.courseFilters.find(courseFilter => courseFilter.id === filter.id) === undefined) {
        //         this.courseFilters.push(filter);
        //         this.courseFilters?.sort((a, b) => (a.name > b.name) ? 1 : -1);
        //     }
        //
        //     window.Vue.$toast.success(
        //         `Filtro ${filter.name} Agregado`
        //       );
        //
        //   this.save();
        // },

        // removeFilterFromCourse: function(filter) {
        //     let index = this.courseFilters.findIndex(courseFilters => courseFilters.id === filter.id);
        //     this.courseFilters.splice(index, 1);
        //
        //     window.Vue.$toast.warning(
        //         `Filtro ${filter.name} removido`
        //       );
        //     this.save();
        // },
    },
    watch: {},

    computed: {
        filteredManagers() {
            return this.filteredManagersNoAdd.filter(manager => {
                const searchTerm = this.normalize(this.searchManager);
                const managerFullName = this.normalize(manager.fullName);

                let filtered = true

                if (searchTerm.length > 0) {
                    filtered = (managerFullName.includes(searchTerm) || manager.email.includes(searchTerm));
                }

                return filtered;
            })
        },

        filteredManagersNoAdd() {
            let managers = this?.managers;

            let managersAssigned = this?.courseManagers;

            return managers?.filter((fil) => {
              let ok = true;
              for (let i = 0; i < managersAssigned?.length && ok; i++) {
                let managerAssigned = managersAssigned[i];
                if (managerAssigned["id"] == fil["id"]) ok = false;
              }
              return ok;
            });
        },

        filteredProfessionalCategories() {
            return this.professionalCategories.filter(professionalCategory => {
                const searchTerm = this.normalize(this.searchProfessionalCategory);
                const professionalCategoryName = this.normalize(professionalCategory.name);

                let filtered = true

                if (searchTerm.length > 0) {
                    filtered = (professionalCategoryName.includes(searchTerm));
                }

                return filtered;
            })
        },

        // filteredFilters() {
        //     return this.filtersNoAdd.filter(filter => {
        //         const searchTerm = this.normalize(this.searchFilter);
        //         const filterName = this.normalize(filter.name);
        //
        //         let filtered = true
        //
        //         if (searchTerm.length > 0) {
        //             filtered = (filterName.includes(searchTerm));
        //         }
        //
        //         return filtered;
        //     })
        // },

        // filtersNoAdd() {
        //     let filters = this?.filters;
        //
        //     let filtersAssigned = this?.courseFilters;
        //
        //     return filters?.filter((fil) => {
        //       let ok = true;
        //       for (let i = 0; i < filtersAssigned?.length && ok; i++) {
        //         let filterAssigned = filtersAssigned[i];
        //         if (filterAssigned["id"] == fil["id"]) ok = false;
        //       }
        //       return ok;
        //     });
        //   },

    }
}).$mount('#access-level')

Vue.use(VueToast, {
    duration: 3000,
    position: 'top-right',
});
