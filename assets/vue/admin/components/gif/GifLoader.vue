<template>
  <div
    class="gif"
    :style="`background-image: url('${gif.images.fixed_width.url}')`"
    @click="copy()"
  >
    <input
      ref="url"
      class="gif-input"
      readonly
      type="text"
      style="flex: 1"
      :value="gif.images.original.url"
    >
  </div>
</template>

<script>
export default {
  name: 'SearchedGif',
  props: ['gif'],
  data() {
    return {
      url: '',
    };
  },
  methods: {
    copy() {
      /* Select the text field */
      this.$refs.url.select();
      this.$refs.url.setSelectionRange(0, 99999); /* For mobile devices */
      /* Copy the text inside the text field */
      document.execCommand('copy');
      // this.$refs.url.value = 'Copied!!!';
      this.$store.dispatch('forumModule/addGif', this.$refs.url.value);
    },
  },
};
</script>

 <style scoped lang="scss"> 
.gif {
  width: 240px;
  height: 240px;
  display: inline-flex;
  justify-content: center;
  align-items: flex-end;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  flex: 0 0 auto;
  margin: 0 10px;
  &:first-child{
    margin-left: 0;
  }
  &:hover{
    border:3px solid var(--primary-color);
  }
}

.gif-input{
  display: none;
}
</style>
