<template>
  <div
    id="gifList"
  >
    <GifLoader
      v-for="gif in gifs"
      :key="gif.id"
      :gif="gif"
    />
  </div>
</template>

<script>
import GifLoader from './GifLoader';

export default {
  name: 'GifList',
  components: { GifLoader },
  props: ['gifs'],
};
</script>

 <style scoped lang="scss"> 
  #gifList {
    margin-top: 20px;
    display: flex;
    overflow-x: auto;
    &:empty{
      display: none;
    }
  }
</style>
