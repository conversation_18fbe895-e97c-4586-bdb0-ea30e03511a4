<template>
  <div class="w-100 d-flex justify-content-center" v-if="loadingCategories">
    <loader :is-loaded="loadingCategories"/>
  </div>

  <div class="CategoryFilter" v-else>
    <nav class="mt-3 mb-2">
      <div class="nav" id="nav-tab">
        <button
          v-for="category in filterCategories"
          :class="['btn', {'text-primary' : selectedCategoryId === category.id}]"
          @click="selectedCategoryId = category.id"
        >
          {{ category.name  }}
        </button>
      </div>
    </nav>
    <div class="tab-content mb-3">
      <div class="available-filters col">
        <div class="form-group">
          <input type="text" class="form-control" v-bind:placeholder="filtersSearch" v-model="searchQuery">
        </div>

        <div class="w-100 d-flex justify-content-center" v-if="loadingFilters">
          <loader :is-loaded="loadingFilters"></loader>
        </div>

        <div v-if="!loadingFilters" class="card" v-for="(filter, index) in filteredFilters" :key="filter.id">
          <div class="card-information">
            <span class="card-firstName">{{ filter.name }}</span>
          </div>

          <button class="ml-auto btn text-primary" @click="add(filter)"><i class="fa fa-plus"></i></button>
        </div>
      </div>
      <div class="col-md-2 d-flex flex-column p-0" v-if="allowAddRemoveAll">
        <button class="btn btn-sm btn-primary w-100" @click="addAll()">
          {{ filtersAddAll }}
          <i class="fa fa-angle-double-right"></i>
        </button>
        <button class="btn btn-sm btn-default w-100 mt-1" @click="removeAll()">
          <i class="fa fa-angle-double-left"></i>
          {{ filtersRemoveAll }}
        </button>
      </div>
      <div class="selected-filters col">
        <div class="form-group">
          <input type="text" class="form-control" v-bind:placeholder="filtersSearch" v-model="searchQuerySelected">
        </div>
        <div class="w-100 d-flex justify-content-center" v-if="loadingSelectedFilters">
          <loader :is-loaded="loadingSelectedFilters"></loader>
        </div>
        <div v-if="!loadingSelectedFilters" class="card" v-for="(filter, index) in selectedFiltersFiltered" :key="filter.id">
          <div class="card-information">
            <span class="card-firstName">{{ filter.name }}</span>
          </div>

          <button class="ml-auto btn text-danger" @click="remove(filter)"><i class="fa fa-minus"></i></button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Loader from "./Loader";
import VueToast from "vue-toast-notification";
import 'vue-toast-notification/dist/theme-sugar.css';

export default {
  name: "CategoryFilter",
  components: {Loader, VueToast},
  props: {
    urlCategories: {
      type: String,
      default: '/admin/filter-categories'
    },
    urlLoadFilters: {
      type: String,
      default: '/admin/filters/categories'
    },
    urlSelectedFilters: {
      type: String,
      default: null
    },
    urlAddFilter: {
      type: String,
      default: null
    },
    urlRemoveFilter: {
      type: String,
      default: null
    },
    saveInRealtime: {
      type: Boolean,
      default: true
    },
    externSelectedFilters: [],//Used for passing local selected data

    // Optional for adding and removing all selected filters per category
    allowAddRemoveAll: {
      type: Boolean,
      default: true
    },
    urlAddAll: {
      type: String,
      default: ''
    },
    urlRemoveAll: {
      type: String,
      default: ''
    },
    // If true, call post when adding, call delete when removing
    useRestMethods: {
      type: Boolean,
      default: false
    },
    useI18n: {
      type: Boolean,
      default: false,
    },
    translations: {},
    filtersAddAll:{
      type: String,
      default: '',
    },
    filtersRemoveAll:{
      type: String,
      default: '',
    },
    filtersSearch:{
      type: String,
      default: '',
    }
  },
  data() {
    return {
      searchQuery: '',
      searchQuerySelected: '',
      filterCategories: [],
      selectedCategoryId: null,
      availableFilters: [],
      selectedFilters: [],

      loadingCategories: true,
      loadingFilters: true,
      loadingSelectedFilters: true,

      localCategorySelectedFilters: []
    };
  },
  watch: {
    async selectedCategoryId(categoryId) {
      this.loadingSelectedFilters = true;
      this.loadingFilters = true;
      if (this.saveInRealtime) {
        this.loadSelectedFilters(categoryId);
      } else {
        this.loadLocalSelectedFilters(categoryId)
      }
      this.getFilters(categoryId);
    },
    localCategorySelectedFilters(values) {
      if (!this.saveInRealtime) {
        const index = values.findIndex(category => category.id === this.selectedCategoryId);
        this.selectedFilters = values[index]?.selected;
      }
    }
  },
  computed: {
    selectedFiltersFiltered() {
      if (this.searchQuerySelected.length > 0) {
        const query = this.normalize(this.searchQuerySelected);
        return this.selectedFilters.filter(filter => this.normalize(filter.name).includes(query));
      }
      return this.selectedFilters;
    },
    filteredFilters() {
      const filters = this.excludeSelected;
      if (this.searchQuery.length > 0) {
        const query = this.normalize(this.searchQuery);
        return filters.filter(filter => this.normalize(filter.name).includes(query));
      }
      return filters;
    },
    excludeSelected() {
      const filters = Array.from(this.selectedFilters);
      return this.availableFilters.filter((filter) => {
        let found = false;
        let index = -1;
        for (let i = 0; i < filters.length; i++) {
          if (filters[i].id === filter.id) {
            found = true;
            index = i;
            break;
          }
        }
        if (found && index >= 0) {
          filters.splice(index, 1);
        }

        return !found;
      });
    }
  },
  async mounted() {
    this.getFilterCategories();
  },
  methods: {
    getFilterCategories() {
      this.loadingCategories = true;
      try {
        axios.get(this.urlCategories).then(r => {
          const { filterCategories } = r.data.data;
          this.filterCategories = filterCategories;
          if (this.filterCategories.length > 0) {
            this.selectedCategoryId = this.filterCategories[0].id;
          }
          if (!this.saveInRealtime) {
            this.initLocalSelectedData();
          }
          this.loadingCategories = false;
        });
      } finally {

      }
    },

    initLocalSelectedData() {
      this.localCategorySelectedFilters = [];
      if (this.externSelectedFilters) {
        this.localCategorySelectedFilters = this.externSelectedFilters;
      } else {
        this.filterCategories.forEach(item => {
          this.localCategorySelectedFilters.push({id: item.id, selected:[]});
        })
      }
    },

    getFilters(categoryId) {
      const url = `${this.urlLoadFilters}/${categoryId}`
      this.loadingFilters = true;
      try {
        axios.get(url).then(r => {
          const {filters} = r.data.data;
          this.availableFilters = filters;
          this.loadingFilters = false;
        })
      } finally {

      }
    },

    normalize(string) {
      return string.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, "")
    },

    async addAll() {
      const filters = this.filteredFilters;
      if (this.saveInRealtime) {
        this.loadingSelectedFilters = true;
        this.loadingFilters = true;
        try {
          const { data } = await axios.post(this.urlAddAll, filters);
          const { status, message } = data;

          if (status === 200 || status === 201) {
            filters.forEach(item => {
              this.selectedFilters.push(item);
            })
            this.$toast.success(message);
            this.$emit('filter-updated')
          } else {
            this.$toast.error(message);
          }
        } finally {
          this.loadingSelectedFilters = false;
          this.loadingFilters = false;
        }
      } else  {
        this.localCategorySelectedFilters.every(category => {
          if (category.id === this.selectedCategoryId) {
            filters.forEach(item => {
              if (category.selected.find(selected => selected.id === item.id) === undefined) {
                category.selected.push(item);
              }
            })
            return false;
          }
          return true;
        });
        this.$emit('on-local-filter-updated', this.localCategorySelectedFilters);
      }

    },

    async add(filter) {
      if (this.saveInRealtime) {
        this.loadingSelectedFilters = true;
        // this.loadingFilters = true;
        try {
          let result;
          if (this.useRestMethods) {
            let url = `${this.urlAddFilter}/${filter.id}`;
            result = await axios.post(url);
          } else {
            result = await axios.post(this.urlAddFilter, filter);
          }
          const { data } = result;
          const { status, message } = data;
          if (status === 200 || status === 201) {
            this.selectedFilters.push(filter);
            this.$toast.success(message);
            this.$emit('filter-updated')
          } else {
            this.$toast.error(message);
          }
        } finally {
          this.loadingSelectedFilters = false;
          this.loadingFilters = false;
        }
      } else {
        this.localCategorySelectedFilters.every(category => {
          if (category.id === this.selectedCategoryId) {
            if (category.selected.find(selected => selected.id === filter.id) === undefined) {
              category.selected.push(filter);
            }
            return false;
          }
          return true;
        });
        this.$emit('add-filter', filter);
        this.$emit('on-local-filter-updated', this.localCategorySelectedFilters);
      }
    },

    async removeAll() {
      const filters = Array.from(this.selectedFiltersFiltered);
      if (this.saveInRealtime) {
        this.loadingSelectedFilters = true;
        this.loadingFilters = true;

        try {
          const { data } = await axios.post(this.urlRemoveAll, filters);
          const { status, message } = data;
          if (status === 200) {
            filters.forEach(filter => {
              this.selectedFilters = this.selectedFilters.filter(item => item.id !== filter.id);
            });
            this.$toast.success(message);
            this.$emit('filter-updated')
          } else {
            this.$toast.error(message);
          }
        } finally {
          this.loadingSelectedFilters = false;
          this.loadingFilters = false;
        }
      } else {
        this.localCategorySelectedFilters.every(category => {
          if (category.id === this.selectedCategoryId) {
            filters.forEach(filter => {
              category.selected = category.selected.filter(item => item.id !== filter.id)
              this.selectedFilters = this.selectedFilters.filter(item => item.id !== filter.id);
            })
            return false;
          }
          return true;
        });
        this.loadLocalSelectedFilters(this.selectedCategoryId);
        this.$emit('on-local-filter-updated', this.localCategorySelectedFilters);
      }
    },

    async remove(filter) {
      if (this.saveInRealtime) {
        this.loadingSelectedFilters = true;
        this.loadingFilters = true;
        try {
          let result;
          if (this.useRestMethods) {
            let url = `${this.urlRemoveFilter}/${filter.id}`;
            result = await axios.delete(url)
          } else {
            result = await axios.post(this.urlRemoveFilter, filter);
          }

          const { data } = result;
          const { status, message } = data;
          if (status === 200) {
            const index = this.selectedFilters.findIndex((item) => item.id === filter.id);
            this.selectedFilters.splice(index, 1);
            this.$toast.success(message);
            this.$emit('filter-updated')
          } else {
            this.$toast.error(message);
          }
        } finally {
          this.loadingSelectedFilters = false;
          this.loadingFilters = false;
        }
      } else {
        this.localCategorySelectedFilters.every(category => {
          if (category.id === this.selectedCategoryId) {
            let index = category.selected.findIndex(selected => selected.id === filter.id);
            if (index >= 0) category.selected.splice(index, 1);
            return false;
          }
          return true;
        });
        this.$emit('remove-filter', filter);
        this.$emit('on-local-filter-updated', this.localCategorySelectedFilters);
      }
    },

    loadSelectedFilters(categoryId) {
      if (this.urlSelectedFilters == null) return;
      const url = `${this.urlSelectedFilters}/${categoryId}`;
      this.loadingSelectedFilters = true;
      try {
        axios.get(url).then(r => {
          this.selectedFilters = r.data.data;
          this.loadingSelectedFilters = false;
        });
      } finally {

      }
    },

    loadLocalSelectedFilters(categoryId) {
      this.loadingSelectedFilters = true;
      try {
        const index = this.localCategorySelectedFilters.findIndex(category => category.id === categoryId);
        this.selectedFilters = this.localCategorySelectedFilters[index].selected;
      } finally {
        this.loadingSelectedFilters = false;
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 
.CategoryFilter {
  #nav-tab {
    justify-content: flex-end;
  }

  .tab-content {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    gap: 1rem;
    // justify-content: space-around;
  }

  .available-filters, .selected-filters {
    // border: 1px solid var(--color-neutral-mid-light);
    // border-radius: 5px;
    padding: 0.5rem;
    background: var(--color-neutral-lighter);
    display: grid;
    gap: 0.25rem;
    align-content: flex-start;
    height: 50vh;
    overflow-y: auto;

    .card {
      padding: 0.75rem;
      flex-direction: row;
      align-items: center;
      box-shadow: none;

      .card-information{
        display: grid;
        flex: 1;
        gap: 0.125rem;
        font-size: .9rem;

        .card-firstName{
          font-weight: 500;
        }
      }
    }
  }
}
</style>
