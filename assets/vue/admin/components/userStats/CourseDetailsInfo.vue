<template>
  <div class="CourseDetailsInfo">
    <div class="attemps">
      <CourseDetailsAttemp
          v-for="(attempt, index) in gameSelected"
          :key="'gs' + index"
          :attempt="attempt"
          :active="attemptActive === index"
          @click="setAttempt(index)"/>
    </div>
    <div class="details">
      <div class="d-flex flex-wrap justify-content-end gap-1">
        <span class="circleContainer error">{{ resultsCounter[0] }}</span>
        <span class="circleContainer success">{{ resultsCounter[1] }}</span>
        <span class="circleContainer bordered">{{ totalQuestions }}</span>
      </div>
      <div class="results">
        <ol type="1" class="question mb-2">
          <li v-for="(question, index) in attemptSelected.questions" :key="'question_' + index">
            <p class="my-0">{{ question.question }}</p>
            <ol type="A">
              <li v-for="(answer, indexA) in question.answers"
                  :key="'answer_' + indexA"
                  :class="{'text-success': answer.correct, 'text-danger': answer.incorrect}">
                {{ answer.answer }}
              </li>
            </ol>
          </li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script>
import CourseDetailsAttemp from "./CourseDetailsAttemp";
export default {
  name: "CourseDetailsInfo",
  components: {CourseDetailsAttemp},
  props: {
    gameSelected: {
      type   : Array,
      default: () => ([])
    }
  },
  data() {
    return {
        attemptActive: 0
    }
  },
  computed: {
    attemptSelected() { return this.gameSelected[this.attemptActive] || {} },
    resultsCounter() {
      return this.attemptSelected?.questions?.reduce((acc, cur) => {
        const [error, success] = cur.answers.reduce((acc2, cur2) => (
            [acc2[0] + (cur2.incorrect ? 1 : 0), acc2[1] + (cur2.correct ? 1 : 0)]), [0, 0])
        return [acc[0] + error, acc[1] + success, acc[2] + (success + error > 0 ? 1 : 0)]
      }, [0, 0, 0])
    },
    totalQuestions() {
      return `${this.resultsCounter[2]}/${this.attemptSelected?.questions?.length}`
    }
  },
  methods: {
    setAttempt(index) {
      this.attemptActive = index
    }
  }
}
</script>

 <style scoped lang="scss"> 
.CourseDetailsInfo {
  display: grid;
  grid-template-columns: 12rem auto;
  gap: 0.5rem;

  .attemps, .details {
    height: 100%;
  }

  .attemps {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding-right: 0.25rem;
    overflow-y: auto;
  }

  .details {
    overflow: hidden;
    padding: 1rem;
    background-color: var(--color-neutral-lightest);
    border-radius: 0.5rem;

    .results {
      height: 100%;
      overflow-y: auto;
      padding: 1rem 0.5rem 2rem 0;
    }

    .circleContainer {
      width: 2.5rem;
      height: 2.5rem;
      font-size: 0.75rem;
      border-radius: 50%;
      display: grid;
      place-items: center;

      &.error {
        color: white;
        background-color: var(--color-dashboard-3);
      }

      &.success {
        color: white;
        background-color: var(--color-dashboard-1);
      }

      &.bordered {
        border: 2px solid var(--color-dashboard-1);
      }
    }
  }
}
</style>
