<template>
    <div class="UserDiplomas">
      <BaseModal
        identifier="userDiplomasModal"
        size="modal-md"
        padding="1"
        :title="title">
          <div class="row mx-0">
            <div class="col-12">
              <div class="row">
                    <div class="col-lg-12" style="margin-top: 10px; margin-bottom: 10px;">
                        <label for="filename" class="text-left">{{ $t('MODAL_FILENAME') }}</label>
                        <input type="text" v-model="filename" name="filename" class="form-control" id="filename" maxlength="25"/>
                    </div>
                </div>
              <div class="row">
                <div class="col-lg-12">
                    <div class="form-group text-nowrap">
                        <label for="typeOfCourse" class="text-left">{{ $t('MULTISELECT.PLACEHOLDER') }}</label> 
                        <select id="typeOfDiploma" v-model="typeOfDiploma" name="typeOfDiploma" class="form-select" >
                            <option :value=0 >{{ $t('ITINERARY.HOME.TITLE') }}</option>
                            <option :value=1>{{ $t('FILTER.HOME.TITLE') }}</option>
                            <option :value=2>{{ $t('ANNOUNCEMENT.HOME.TITLE') }}</option>
                        </select>
                    </div>
                  </div>
              </div>
              <div class="row">
                <button @click="generateDiplomas" class="btn btn-sm btn-primary">{{ $t('DIPLOMAS_GENERATE') }}</button>
              </div>
            </div>
          </div>
        </BaseModal>
    </div>
  </template>
  
  <script>
  import BaseModal from "../../../base/BaseModal";
  import axios from 'axios'
  import TaskQueueMixin from '../../../mixins/TaskQueueMixin';
  
  export default {
    name      : "UserDiplomas",
    components: { BaseModal },
    props: {
      title: {
        type   : String,
        default: ''
      },
      userId: {
        type: Number,
        default: 0
      },
      userName: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        typeOfDiploma: 0,
        filename: 'Diplomas ' + this.userName + ' ' + this.dateFormat(new Date())
      }
    },
    mixins: [
        TaskQueueMixin
    ],
    methods: {
      async generateDiplomas() {
        if (this.filename === '') {
            return this.$toast.error('El nombre del archivo es requerido');
        }
        const data = {
            userId: this.userId,
            typeOfDiplomas: this.typeOfDiploma,
            filename: this.filename
        };
        try {
            await this.enqueueTask({
                url: '/admin/api/v1/course-stats/diplomas-user',
                data,
                messages: {
                    success: 'Diplomas generados correctamente',
                    error: 'Error al generar los diplomas'
                },
                onSuccess: () => {
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                }
            });
        } catch (error) {
            console.error('Error:', error);
        }
      },
      dateFormat(date) {
            return `${date.getDate()}`.padStart(2, '0') + '-' +`${(date.getMonth() + 1)}`.padStart(2, '0') + '-' + date.getFullYear();
        },
    }
  }
  </script>
  