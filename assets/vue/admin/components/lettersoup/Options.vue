<template>
  <div class="GameBlock">
    <div class="options">
      <div class="question">
        <label for="words_array">Palabras: </label>
        <div class="row">
          <div class="col-sm-2"></div> 
          <div class="col-sm-10">
            <div v-for="(word, index) in words_array">
              <label id="labelIndex">{{index+1}}</label>
              <label class="labelWord">{{word}}</label>
              <div class="btnWord" @click="editarWord(word, index)"><i class="fa fa-pen"></i> Modificar </div>
              <div class="btnWord" @click="borrarWord(index)"><i class="fa fa-trash"></i> Borrar </div>
            </div>             
          </div>     
        </div> 
        <div class="row">
          <div class="col-sm-2"></div> 
          <div class="col-sm-8"> 
            <div v-if="newWordActive">
              <input type="text" id="newWord" v-model="newWord" placeholder="Nueva palabra" class="word"></input> 
               <div class="btnWord" @click="addWord()"><i class="fa fa-plus-circle"></i> Guardar Palabra </div> 
               <div class="btnWord" @click="cancellWord()"><i class="fa fa-times"></i> Cancelar </div>              
            </div>

            <button @click="adicionarWord" class="btn btn-primary ml-1" v-if="adicionarPalabraActive">
              <i class="fa fa-plus"></i> Adicionar Palabra
            </button>
            <button @click="modifyWord" class="btn btn-primary ml-1" v-if="adicionarPalabraActive">
              <i class="fa fa-times"></i> Cerrar
            </button>
          </div>  
          <div class="col-sm-2"></div>   
        </div>
      </div>


      <div class="question">
        <label class="labelMensaje" v-if="mensajeActivo">{{mensaje}}</label>
      </div> 
    </div>
  </div>

</template>

<script>


export default {

  props: {
    chapterId: {
      type: Number,
      default: 0,
    },

    currentLine: {
      type: String,
      default: '',
    },
  },

  created() {
    if(this.currentLine.length>0)
      this.words_array = this.currentLine.split(',');
    else 
     this.words_array = [];
  },

  data() {
    return {
      words_array: [],
      time: 10,
      title: undefined,
      editionActive: false,
      intermedioWord: undefined,
      newWord: '',
      indexModificar: -1,
      newWordActive: false,
      adicionarPalabraActive: true,
      mensaje:'',
      mensajeActivo:false,
      caracteresMinimosWord:2,
      caracteresMaximosWord:12,
      minimoPalabras:2,
      maximoPalabras:10,
    };
  },

  methods: {
    setAdicionaPalabraActive: function(){  
      if(this.words_array.length>11)
       this.adicionarPalabraActive=false; 
      else 
        this.adicionarPalabraActive=true;
    },
    editarWord: function(word, index){     
      this.newWord = word;      
      this.indexModificar = index;
      this.newWordActive= true;
    },
    cancellWord(){    
      this.mensajeActivo = false;
      this.newWordActive= false;
    },
    addWord: function(){
      if(this.words_array.includes(this.newWord) && this.indexModificar == -1){
        this.mensaje= this.newWord+" ya existe en la lista";
        this.mensajeActivo = true; 
      }else if(this.newWord.length < this.caracteresMinimosWord || this.newWord.length > this.caracteresMaximosWord){
        this.mensaje="La palabra debe tener entre "+this.caracteresMinimosWord+ " y " +this.caracteresMaximosWord+" caracteres";
        this.mensajeActivo = true;        
      }else{
        if(this.indexModificar != -1 && this.newWord != ''){
          this.words_array[this.indexModificar] = this.newWord;
          this.indexModificar = -1;
        }else{
          this.words_array.push(this.newWord);
        }

        this.setAdicionaPalabraActive();
        this.newWord = '';
        this.mensajeActivo = false;
        this.newWordActive= false;
      }
    },
    adicionarWord: function () {      
      this.newWordActive = true;
      this.mensajeActivo = false;
      if(this.words_array.length >= this.minimoPalabras && this.words_array.length <= this.maximoPalabras){       
        this.$emit('adicionarWord', this.words_array);
      }else{
        this.mensaje="Se debe tener entre "+this.minimoPalabras+ " y " +this.maximoPalabras+" palabras";
        this.mensajeActivo = true;
      } 
    },
    borrarWord: function (index) {
      this.words_array.splice(index, 1);
      this.setAdicionaPalabraActive();
    },
    modifyWord(){
      if(this.words_array.length < this.minimoPalabras || this.words_array.length > this.maximoPalabras){
        this.mensaje="Se debe tener entre "+this.minimoPalabras+ " y " +this.maximoPalabras+" palabras";
        this.mensajeActivo = true;        
      }else this.$emit('modifyWord', this.words_array);  
    },
    clearCurrent(){
      this.words_array = undefined;
      this.time = undefined;
      this.title = undefined;
      this.editionActive = false;
      this.adicionarPalabraActive=true;
    }
  }
};
</script>

 <style scoped lang="scss"> 
.GameBlock {
  padding: 1.5rem;

  .game-block{
    height: 30rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .title{
    color: black;
    font-size: 1.5rem
  }

  .select-block{
    padding: .5rem;
  }

  .question, .response{
    display: flex;
    flex-direction: column;
    margin-bottom: .5rem;
    padding: .5rem;
  }

  #words_array, #time, #title,  #response{
    border: 1px solid #5ae8e8;
    border-radius: 25px;
  }
  .word{
    width: 35%;
    margin: .5rem;
    border: 1px solid #5ae8e8;
    border-radius: 25px;
    padding: .5rem;
  }

  #words_array, #time,  #title{
    height: 2.5rem;
    padding: .5rem;
  }
  #labelIndex{
    width: 5%;
  }
  .labelWord{
    width: 35%;
    margin-right: .5rem;
  }
  .labelMensaje{    
    width: 60%;
    padding: .5rem;
    align-content: center;
    color: red;
  }

  .btnWord{
    display: inline;
    margin-right: .5rem;
    cursor: pointer;
  }

  label {
    text-transform: uppercase;
    font-weight: 500;
    padding: 0 .5rem;
    margin-bottom: .25rem;
    padding: .5rem;
  }

  select{
    background-color: #fff;
    border: 1px solid #ffffff;
    border-radius: 50px;

    width: 35%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
  }

  .options{
    height: 100%;
    flex:2;

    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
}
</style>
