<template>
  <div
    v-if="isLoaded"
    class="loader d-flex align-items-center justify-content-center p-4"
    :style="{height: heightValue }"
  >
    <div class="spinner-border" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
</template>
<script>
    // const moduleTags = ['call'];
    export default {
      name: "loader",
      props: {
        isLoaded: {
          type: Boolean,
          required: true,
        },

        height: {
          type: String,
          default: 'auto',
        },
      },

      computed: {
        heightValue() {
          const pixels = this.height.match(/(?<n>\d+)/)?.groups?.n;
          return pixels ? `${pixels}px` : this.height;
        },
      },
    }
</script>
<style scoped lang="scss">
</style>
