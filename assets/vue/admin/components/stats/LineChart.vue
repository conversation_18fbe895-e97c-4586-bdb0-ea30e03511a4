<template>
    <highcharts class="hc" :options="chartOptions" ref="chart"></highcharts>
</template>

<script>

    export default {
        name: 'LineChart',
        components: {},

        props: {
            title: {
                type: String,
                default: '',
            },
            seriesData: {
                type: Array,
                default: [],
            },
            color: {
                type: String,
                default: 'var(--color-primary)',
            },
            line: {
                type: String,
                default: 'spline'
            },
            tooltipTitle: {
                type: String,
                default: '',
            },
        },

        computed: {
            chartOptions() {
                let ctx = this;
                return {
                    chart: {
                        plotBackgroundColor: null,
                        plotBorderWidth: null,
                        plotShadow: false,
                        type: this.line,
                        height: 300,
                    },
                    credits: {
                        enabled: false
                    },
                    title: {
                        text: this.title
                    },
                    rangeSelector: {
                        enabled: true,
                        selected: 0
                    },
                    xAxis: {
                        type: 'date',
                        labels: {
                            format: '{value: %Y-%m-%d}'
                        },
                        step: 86400,
                    },
                    tooltip: {
                        formatter: function(e) {
                            let date = new Date(this.x);
                            return '<strong>Fecha</strong>: '
                                + date.getFullYear() + '/'
                                + (date.getMonth() < 9 ? '0' : '')
                                + (date.getMonth() + 1)
                                + '/'
                                + (date.getDate() < 10 ? '0' : '')
                                + date.getDate()
                                + '<br />'
                                + (ctx.tooltipTitle ? '<strong>' + ctx.tooltipTitle +  ':</strong> ' : '')
                                + this.y
                                ;
                        }
                    },
                    plotOptions: {
                        area: {
                            fillColor: {
                                linearGradient: {
                                    x1: 0,
                                    y1: 0,
                                    x2: 0,
                                    y2: 1
                                },
                                stops: [
                                    [0, this.color],
                                ]
                            },
                            marker: {
                                radius: 2
                            },
                            lineWidth: 1,
                            states: {
                                hover: {
                                    lineWidth: 1
                                }
                            },
                            threshold: null
                        }
                    },

                    series: [{
                        name: this.title,
                        type: 'area',
                        data: this.seriesData,
                        showInLegend: false,
                        color: this.color,
                    }],
                    lang: {
                        noData: this.$t('NO_INFORMATION'),
                    },
                    noData: {
                        style: {
                            fontWeight: 'bold',
                            fontSize: '15px',
                            color: '#303030'
                        }
                    }
                }
            },
        },

        mounted() {

        },

        watch: {
            seriesData: function (newVal, oldVal) { // watch it
                this.chartOptions.series[0].data = newVal;
            }
        }
    };
</script>

 <style scoped lang="scss"> 
</style>
