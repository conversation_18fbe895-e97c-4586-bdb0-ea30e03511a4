<template>
  <div class="justify-content-end" display="flex">
    <!-- Button trigger modal -->
    <button
        type="button"
        class="btn btn-primary"
        data-bs-toggle="modal"
        data-bs-target="#assignFiltersModal"
    >
      <i class="fa fa-edit"></i>
      {{ useI18n ? $t(prefix + "ASSIGN_FILTER") : translations.assign_filters }}
    </button>

    <!-- Modal -->
    <div
        class="modal fade modal-add-users"
        id="assignFiltersModal"
        tabindex="-1"
        aria-labelledby="assignFiltersModalLabel"
        aria-hidden="true"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header col-12 d-flex justify-content-center">
            <h5 class="modal-title" id="assignFiltersModalLabel">
              {{
              useI18n
              ? $t(prefix + "ASSIGN_FILTER")
              : translations.assign_filters
              }}
            </h5>
            <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <!-- MODAL BODY -->
            <div
                class="w-100 d-flex justify-content-center"
                v-if="loadingCategories"
            >
              <spinner/>
            </div>

            <div class="ItineraryCategoryFilter CategoryFilter" v-else>
              <nav class="CategoryFilter--categories mt-3 mb-2">
                <div class="nav" id="nav-tab">
                  <button
                      type="button"
                      v-for="category in categories"
                      :class="[
                      'btn',
                      { 'text-primary selected': categoryId === category.id },
                    ]"
                      @click="categoryId = category.id"
                  >
                    {{ category.name }}
                    <span v-if="!showWarning && showCategoryWarningStatus"
                    >({{
                        category.selected > 0 ? category.selected : "Todos"
                      }})</span
                    >
                  </button>
                </div>
              </nav>
              <div
                  class="w-100 m-1 text-center"
                  v-if="showCategoryWarningStatus && showWarning"
              >
                <p class="text-danger font-weight-bold">
                  {{ categoryWarningStatusText }}
                </p>
              </div>
              <div class="row col-md-12" :class="allowAll ? 'all' : ''">
                <div class="col-md-5 contentFilters">
                  <div class="title" v-if="showTitles">
                    <span>{{ $t("CATEGORY_FILTER.AVAILABLE") }}</span>
                  </div>
                  <div class="content">
                    <div class="content--filter form-group">
                      <input
                          type="text"
                          class="form-control"
                          v-model="queryAvailable"
                      />
                    </div>
                    <div class="content--loader" v-if="loadingAvailable">
                      <spinner/>
                    </div>
                    <div class="content--content" v-else>
                      <div
                          class="card"
                          v-for="filter in availableFiltered"
                          :key="filter.id"
                      >
                        <div class="card--information">
                          <span class="card--information--title">{{
                            filter.name
                          }}</span>
                        </div>
                        <button
                            type="button"
                            class="ml-auto btn btn-primary btn-sm"
                            @click="add(filter)"
                        >
                          <i class="fa fa-plus"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-2 action max-height-70" v-if="allowAll">
                  <div class="w-100 d-flex mt-auto mb-auto flex-column">
                    <button
                        class="mt-1 btn btn-primary"
                        type="button"
                        @click="addAll()"
                    >
                      {{ useI18n ? $t("ADD_ALL") : translations.add_all }}
                      <i class="fas fa-angle-double-right ml-2"></i>
                    </button>
                    <button
                        class="mt-1 btn btn-danger"
                        type="button"
                        @click="removeAll()"
                    >
                      <i class="fas fa-angle-double-left mr-2"></i>
                      {{ useI18n ? $t("REMOVE_ALL") : translations.remove_all }}
                    </button>
                  </div>
                </div>
                <div class="col-md-5 contentFilters">
                  <div class="title" v-if="showTitles">
                    <span>{{ $t("CATEGORY_FILTER.SELECTED") }}</span>
                  </div>
                  <div class="content">
                    <div class="content--filter form-group">
                      <input
                          type="text"
                          class="form-control"
                          v-model="querySelected"
                          placeholder=""
                      />
                    </div>
                    <div class="content--loader" v-if="loadingSelected">
                      <spinner/>
                    </div>
                    <div class="content--content" v-else>
                      <div
                          class="card"
                          v-for="filter in selectedFiltered"
                          :key="filter.id"
                      >
                        <div class="card--information">
                          <span class="card--information--title">{{
                            filter.name
                          }}</span>
                        </div>
                        <button v-if="!('isManagerFilter' in filter) || filter.isManagerFilter === true"
                                type="button"
                                class="ml-auto text-danger btn btn-sm btn-danger"
                                @click="remove(filter)"
                        >
                          <i class="fa fa-minus"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- MODAL BODY -->

          <div class="modal-footer">
            <button
                type="button"
                class="btn btn-secondary"
                data-bs-dismiss="modal"
                @click="$emit('cancel-edition')"
            >
              <i class="fa fa-edit"></i>
              {{ useI18n ? $t("CANCEL") : translations.cancel }}
            </button>
            <button
                type="button"
                class="btn btn-primary"
                @click="saveSelectedFiltersRaw()"
                data-bs-dismiss="modal"
            >
              {{ useI18n ? $t("SAVE") : translations.save }}
            </button>
          </div>
        </div>
      </div>
    </div>
    <div></div>
  </div>
</template>

<script>
import Spinner from "./base/Spinner.vue";
import axios from "axios";

export function generateCompatibleData(content) {
  const data = {};
  content.forEach((i) => {
    if ("categoryId" in i) {
      if (!data[`category_${i.categoryId}`]) {
        data[`category_${i.categoryId}`] = [];
      }
      data[`category_${i.categoryId}`].push(i);
    }
  });
  return data;
}

export function getFiltersForRequest(content) {
  const keys = Object.keys(content);
  const filters = [];
  keys.forEach((key) => {
    content[key].forEach((filter) => {
      filters.push(filter.id);
    });
  });
  return filters;
}

export default {
  name: "ItineraryCategoryFilter",
  components: {Spinner},
  props: {
    showTitles: {
      type: Boolean,
      default: false,
    },
    titleAvailable: {
      type: String,
      default: "FILTER_CATEGORY.AVAILABLE",
    },
    titleSelected: {
      type: String,
      default: "FILTER_CATEGORY.SELECTED",
    },

    urlCategories: {
      type: String,
      default: "/admin/filter/categories",
    },

    urlAvailableFilters: {
      type: String,
      default: "/admin/filter/list-with-category",
    },

    translations: {},
    useI18n: {
      type: Boolean,
      default: false,
    },
    i18nPrefix: {
      type: String,
      default: "",
    },

    realtime: {
      type: Boolean,
      default: false,
    },

    rest: {
      type: Boolean,
      default: false,
    },

    urlAdd: {
      type: String,
      default: null,
    },
    urlAddAll: {
      type: String,
      default: null,
    },
    urlRemove: {
      type: String,
      default: null,
    },
    urlRemoveAll: {
      type: String,
      default: null,
    },
    saveSelectedFiltersRaw: {
      type: Function,
      default: () => {
      },
    },

    allowAll: {
      type: Boolean,
      default: true,
    },

    /**
     * @param showCategoryStatus Used whether to display or not warning related to the multiple selections
     */
    showCategoryWarningStatus: {
      type: Boolean,
      default: false,
    },

    /**
     * @param categoryWarningStatusText Pass translated warning status text
     */
    categoryWarningStatusText: {
      type: String,
      default: null,
    },

    /**
     * @param value Pass the current status as v-model
     * format: {
     *   category_1: [
     *     {
     *       id: 1,
     *       name: 'Filter Name
     *     }
     *   ]
     * }
     * where the index is the category id
     */
    value: null,
  },
  data() {
    return {
      loadingCategories: false,
      loadingAvailable: false,
      loadingSelected: false,

      categoryId: null,
      queryAvailable: "",
      querySelected: "",

      categories: [], //All available categories
      filters: [], // All available filters

      selectedOffline: [],
      validated: false,
      showWarning: true,
    };
  },

  computed: {
    selectedCategoryId: function () {
      return `category_${this.categoryId}`;
    },

    available: function () {
      const selected = this.selected ?? [];
      const available = this.filters[this.categoryId] ?? [];
      if (selected.length === 0) return available;

      return available.filter((filter) => {
        const index = selected.findIndex((item) => item.id === filter.id);
        return index < 0;
      });
    },

    selected: function () {
      if (!this.innerValue) return [];
      return this.innerValue[`category_${this.categoryId}`] ?? [];
    },

    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit("input", newValue);
      },
    },

    selectedFiltered: function () {
      const selected = this.selected;
      let query = this.querySelected;
      if (query.length === 0) return selected;
      if (selected[`category_${this.categoryId}`] === undefined) return [];
      query = this.normalize(query);
      return selected.filter((filter) =>
          this.normalize(filter.name).includes(query)
      );
    },

    availableFiltered: function () {
      let query = this.queryAvailable;
      if (query.length === 0) return this.available;
      query = this.normalize(query);
      return this.available.filter((filter) =>
          this.normalize(filter.name).includes(query)
      );
    },
  },

  watch: {
    innerValue: {
      handler: function (val, oldVal) {
        this.validateWarning();
      },
      immediate: true,
    },
  },

  async mounted() {
    this.loadingCategories = true;
    this.loadingAvailable = true;
    this.loadingSelected = true;

    try {
      await this.loadCategories();
      await this.loadFilters();
      await this.validateSelected();
    } finally {
      this.loadingCategories = false;
      this.loadingAvailable = false;
      this.loadingSelected = false;
    }
    this.validateWarning();
  },

  methods: {
    validateWarning() {
      let warning = true;
      const categories = structuredClone(this.categories);
      for (let i = 0; i < this.categories.length; i++) {
        const size =
            this.innerValue["category_" + categories[i].id]?.length ?? 0;
        if (size > 0) warning = false;
        categories[i]["selected"] = size;
      }
      this.showWarning = warning;
      this.categories = categories;
    },
    async add(filter) {
      let success = !this.realtime;
      if (this.realtime) {
        const self = this;

        function save() {
          if (self.rest) return axios.post(`${self.urlAdd}/${filter.id}`);
          else return axios.post(self.urlAdd, filter);
        }

        const result = await save();
        const {error} = result.data;
        success = !error;
        if (error)
          this.$toast.error(
              this.$t(`CATEGORY_FILTER.ADD.FAILED`, [filter.name]) + ""
          );
        else
          this.$toast.error(
              this.$t(`CATEGORY_FILTER.ADD.SUCCESS`, [filter.name]) + ""
          );
      }
      if (success) this.addElement(filter);
    },

    async addAll() {
      const elements = structuredClone(this.availableFiltered);
      let success = !this.realtime;
      if (this.realtime) {
        const ids = [];
        elements.forEach((item) => {
          ids.push(item.id);
        });

        const result = await axios.post(this.urlAddAll, {data: ids});
        const {error} = result.data;
        success = !error;
        if (success)
          this.$toast.success(this.$t("CATEGORY_FILTER.ADD_ALL.SUCCESS") + "");
        else this.$toast.error(this.$t("CATEGORY_FILTER.ADD_ALL.ERROR") + "");
      }

      if (success) {
        const selected = structuredClone(this.innerValue);
        elements.forEach((item) => {
          selected[`category_${this.categoryId}`].push(item);
        });
        this.innerValue = selected;
      }
    },

    async remove(filter) {
      let success = !this.realtime;
      if (this.realtime) {
        const self = this;

        function save() {
          if (self.rest) return axios.delete(`${self.urlRemove}/${filter.id}`);
          else return axios.post(self.urlAdd, filter);
        }

        const result = await save();
        const {error} = result.data;
        success = !error;
        if (error)
          this.$toast.error(
              this.$t(`CATEGORY_FILTER.REMOVE.FAILED`, [filter.name]) + ""
          );
        else
          this.$toast.error(
              this.$t(`CATEGORY_FILTER.REMOVE.SUCCESS`, [filter.name]) + ""
          );
      }
      if (success) this.removeElement(filter);
    },

    async removeAll() {
      const elements = structuredClone(this.selectedFiltered);
      if (elements.length === 0) return;
      let success = !this.realtime;
      if (this.realtime) {
        const ids = [];
        elements.forEach((item) => {
          ids.push(item.id);
        });
        const result = await axios.post(this.urlRemoveAll, {data: ids});
        const {error} = result.data;
        success = !error;
        if (success)
          this.$toast.success(
              this.$t("CATEGORY_FILTER.REMOVE_ALL.SUCCESS") + ""
          );
        else
          this.$toast.error(this.$t("CATEGORY_FILTER.REMOVE_ALL.FAILED") + "");
      }

      if (success) {
        const selected = structuredClone(this.innerValue);
        elements.forEach((item) => {
          const index = selected[`category_${this.categoryId}`].findIndex(
              (s) => s.id === item.id
          );
          if (index >= 0) {
            selected[`category_${this.categoryId}`].splice(index, 1);
          }
        });
        this.innerValue = selected;
      }
    },

    addElement(filter) {
      const selected = structuredClone(this.innerValue);
      if (!selected[`category_${this.categoryId}`])
        selected[`category_${this.categoryId}`] = [];
      selected[`category_${this.categoryId}`].push(filter);
      this.innerValue = selected;
    },

    removeElement(filter) {
      const selected = structuredClone(this.innerValue);
      const index = selected[`category_${this.categoryId}`].findIndex(
          (s) => s.id === filter.id
      );
      if (index >= 0) {
        selected[`category_${this.categoryId}`].splice(index, 1);
        this.innerValue = selected;
      }
    },

    async loadCategories() {
      this.loadingCategories = true;
      try {
        const result = await axios.get(this.urlCategories);
        const {data} = result.data;
        this.categories = data;
        if (this.categories.length > 0) this.categoryId = this.categories[0].id;
      } finally {
        this.loadingCategories = false;
      }
    },

    async loadFilters() {
      try {
        const result = await axios.get(this.urlAvailableFilters);
        const {data} = result.data;
        this.filters = data;
      } finally {
      }
    },

    normalize(string) {
      return string
          .toLowerCase()
          .normalize("NFD")
          .replace(/[\u0300-\u036f]/g, "");
    },

    initSelected() {
      const selected = this.innerValue;
      this.categories.forEach((category) => {
        if (!selected[`category_${category.id}`])
          selected[`category_${category.id}`] = [];
      });
      return selected;
    },

    validateSelected() {
      const allFilters = this.innerValue ?? [];
      let selected = this.initSelected();

      // Check if the content needs rework
      if (allFilters.length > 0 && "categoryId" in allFilters[0]) {
        allFilters.forEach((filter) => {
          selected[`category_${filter.categoryId}`].push({
            id: filter.id,
            name: filter.name,
          });
        });
      } else if (allFilters.length > 0) {
        selected = allFilters;
      }
      this.innerValue = selected;
    },
  },
};
</script>

<style scoped lang="scss">
$available-card-border-color: #cbd5e1;
$selected-card-border-color: #13c8ff;
.ItineraryCategoryFilter {
  &--categories {
    .nav {
      justify-content: flex-end;
      gap: 0.15rem;

      button {
        border-radius: 17px 17px 0 0;
        border: 1px solid var(--color-neutral-mid);
        background-color: var(--color-neutral-lighter);

        &.selected {
          border: 1px solid var(--color-neutral-mid);
          background-color: #ffffff;
        }
      }
    }
  }

  .contentFilters {
    display: grid;
    gap: 0.25rem;
    align-content: flex-start;

    .card {
      padding: 0.75rem;
      flex-direction: row;
      align-items: center;
      margin-bottom: 0.5em;

      .card-information {
        display: grid;
        flex: 1;
        gap: 0.125rem;
        font-size: 0.9rem;

        .card-firstName {
          font-weight: 500;
        }

        .card-email {
          font-style: italic;
          color: var(--color-neutral-dark);
        }
      }
    }
  }

  &--content {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    background-color: #ffffff;
    justify-content: center;

    &.all {
      grid-template-columns: 1fr 60px 1fr;
    }

    &--actions {
      align-self: center;
    }

    &--available,
    &--selected {
      display: flex;
      flex-flow: column;
      padding: 0.5rem;

      .title {
        font-weight: bold;
        color: var(--color-neutral-darkest);
        font-size: 18px;
        text-align: center;
        width: 100%;
      }

      .content {
        padding: 0.5rem;
        background: var(--color-neutral-lighter);

        &--content {
          display: grid;
          gap: 0.25rem;
          align-content: flex-start;
          height: 50vh;
          overflow-y: auto;
          overflow-x: hidden;

          .card {
            padding: 0.75rem;
            flex-direction: row;
            align-items: center;
            box-shadow: none;
            border-radius: 5px;

            &--information {
              display: grid;
              flex: 1;
              gap: 0.125rem;
              font-size: 0.9rem;

              &--title {
                font-weight: 500;
              }
            }
          }
        }
      }
    }

    &--available {
      .card {
        border: 1px solid $available-card-border-color;
      }
    }

    &--selected {
      .card {
        border: 1px solid $selected-card-border-color;
      }
    }
  }
}
</style>
