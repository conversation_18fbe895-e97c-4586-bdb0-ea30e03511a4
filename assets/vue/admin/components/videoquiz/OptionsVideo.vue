<template>
  <video-player 
    :videoURL="videoURL" 
    :videoquizId="videoquizId"  
    :videoquizTime="videoquizTime"  
    @addpregunta="addpregunta"       
  />
</template>
  
  <script> 
  import VideoPlayer from "../VideoPlayer";
  export default {
    name: "App",
    props: {
        registroQuiz: {
            type: Object,
            default: {},
        },
    },
    data: () => ({
      videoURL: '',
      videoquizId:0,
      videoquizTime:0,
    }), 
    created() {
      this.videoquizId =   this.registroQuiz.id; 
      if(this.registroQuiz.currentTime && this.registroQuiz.currentTime>0) this.videoquizTime =  this.registroQuiz.currentTime; 
      else this.videoquizTime = 0;
      //console.log('optionsVideo', this.videoquizTime);
      this.videoURL = this.registroQuiz.pathImage+this.registroQuiz.url;       
    },
    
    components: {
      VideoPlayer,
    },
    methods: {
      cargarPreguntas(videoquizId){ 
        const data = {
          videoquiz_id : videoquizId,
          url: this.videoURL,
          pathImage: 'uploads/games/videoquiz/',
          llamado:1,//muestra preguntas
          currentTime: 0,
        };

        this.$emit('detailLine', data);
      },
      addpregunta(currentTime){     
        const data = {
          id:  this.videoquizId,
          url: this.url,
          pathImage: 'uploads/games/videoquiz/',
          llamado:1, //carga optionsPreguntas,
          currentTime: currentTime,
        }
        this.$emit('detailLine', data);
      },
    },
  };
  </script>
  
  <style>
  body {
    font-family: Roboto;
  }
  </style>