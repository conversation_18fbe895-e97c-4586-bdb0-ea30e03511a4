<template>
  <div class="new-video-quiz" v-if="!process">
    <div class="">
      <BaseInputFile
        tag="file-video"
        accept="video/*"
        placeholder="Inserte un video desde la galería"
        @files-selected="fileSelected($event)"
      />
    </div>
   
    <div class="">
      <video ref="videoPlayer" width="100%" height="700px" controls>
        Your browser does not support the video tag.
      </video>

      <div class="row" v-show="video">
        <div class="mt-1">
          <button class="btn btn-primary btn-sm" @click="addNewQuestion">
            <i class="fas fa-plus"></i>
            {{ translationsVue.video_configureFields_add_question }}
          </button>
        </div>
      </div>
    </div>

    <dialog>
      <div class="row">
        <h6>
          <i class="fas fa-hourglass-end"></i>
          {{ translationsVue.games_videoquiz_time_video }} :
          <span class="text-primary">{{ currentQuestion.time }}</span>
        </h6>

        <div class="class-question-image" v-if="currentQuestion !== undefined">
          <div class="title-question">
            <div>
              <textarea
                type="text"
                class="form-control"
                v-model="currentQuestion.question"
                :placeholder="translationsVue.games_help_write_question"
                rows="1"
              />
            </div>
          </div>
          <div class="image">
            <div>
              <label>{{
                translationsVue.games_text_common_ilustre_question
              }}</label>
              <div
                :style="{
                  backgroundImage: 'url(' + previewImage(preview) + ')',
                }"
                :class="
                  image == null
                    ? 'preview-image preview-image-default'
                    : 'preview-image'
                "
                @click="$refs.inputFile.click()"
              ></div>
              <div class="mt-1">
                <input
                  type="file"
                  @change="loadImage($event)"
                  accept="image/png, image/jpeg"
                  ref="inputFile"
                />
                <button
                  class="btn-sm btn btn-primary"
                  @click="$refs.inputFile.click()"
                >
                  <i class="fas fa-upload"></i>
                  {{ translationsVue.games_text_common_select_image }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <p class="mt-5">
        {{ translationsVue.question_configureFields_answers }}
      </p>
      <div
        class="row"
        v-for="(ans, index) in currentQuestion.answers"
        :key="ans.id"
      >
        <div class="col-md-8">
          <div class="mb-3">
            <input
              type="text"
              class="form-control"
              name="question"
              v-model="ans.answer"
              :placeholder="translationsVue.games_help_write_answer"
            />
          </div>
        </div>
        <div class="col-md-3 form-check mt-2">
          <input
            class="form-check-input"
            type="checkbox"
            value="true"
            v-bind:id="ans.id"
            name="correct"
            v-model="ans.correct"
            v-on:input="checkCorrect(index)"
          />
          <label class="form-check-label" for="flexCheckDefault">
            {{ translationsVue.challenges_correct }}
          </label>
        </div>
        <div class="col-md-1 trash-question">
          <p class="trash" @click="deleteAnswer(index)">
            <i class="fas fa-trash-alt text-danger"></i>
          </p>
        </div>
      </div>

      <div>
        <button class="btn-sm btn btn-primary" @click="addAnswer()">
          <i class="fas fa-plus"></i> {{ translationsVue.games_answers }}
        </button>
      </div>

      <div class="col align-self-end text-right mb-2 mt-2">
        <button class="btn btn-secondary btn-sm" @click="closeModal()">
          {{ translationsVue.cancelar }}
        </button>
        <button class="btn btn-primary btn-sm" @click="addQuestion()">
          {{ translationsVue.add }}
        </button>
      </div>
    </dialog>

    <div class="mt-4" v-if="questions.length > 0">
      <table class="table datagrid">
        <thead>
          <tr>
            <th>{{ translationsVue.common_areas_image }}</th>
            <th scope="col">{{ translationsVue.user_configureFields_time }}</th>
            <th scope="col">
              {{ translationsVue.question_label_in_singular }}
            </th>
            <th scope="col">
              {{ translationsVue.question_configureFields_answers }}
            </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(q, index) in questions" :key="q.id">
            <td>
              <div
                :style="{
                  backgroundImage: 'url(' + previewImage(q.preview) + ')',
                }"
                class="preview-image-table"
              ></div>
            </td>

            <td class="text-primary">
              <a @click="goToTimeVideo(q.time)" class="goto-video">
                <i class="fas fa-eye"></i> <span> {{ q.time }}</span></a
              >
            </td>
            <td>{{ q.question }}</td>
            <td>{{ q.answers.length }}</td>
            <td class="text-right">
              <button class="btn btn-primary btn-sm" @click="openModalEdit(q)">
                <i class="fas fa-edit"></i>
              </button>
              <button
                class="btn btn-danger btn-sm"
                @click="deleteQuestion(index)"
              >
                <i class="fas fa-trash-alt"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="mt-5 text-center">
      <button
        v-show="0"
        type="button"
        class="btn btn-secondary"
        data-bs-dismiss="modal"
        ref="closeChapterContentModal"
      ></button>

      <button
        class="btn-sm btn btn-secondary"
        @click="saveVideoQuiz"
        disabled
        id="save-quiz"
      >
        {{ translationsVue.games_videoquiz_savell_all_changes }}
      </button>
    </div>
  </div>
  <div class="new-video-quiz" v-else>
    <h3 class="text-center">
      {{ translationsVue.component_video_preparing_file }}
    </h3>
    <spinner />
  </div>
</template>

<script>
import BaseInputFile from "../base/BaseInputFile";
import Spinner from "../base/Spinner";

export default {
  components: {
    BaseInputFile,
    Spinner,
  },

  props: {
    urlChapter: {
      type: String,
      default: "",
    },

    vimeoUploadSubdomain: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      titleVideo: "",
      process: false,
      typeChapter,
      translationsVue,
      chapterId,
      video: null,
      currentTime: 0,
      totalTimeVideo: 0,
      question: "",
      questions: [],
      answers: [
        {
          id: 1,
          answer: "",
          correct: false,
        },
      ],
      preview: "",
      image: null,
      currentQuestion: {
        id: 1,
        time: 0,
        question: this.question,
        answers: [
          {
            id: 1,
            answer: "",
            correct: false,
          },
        ],
        preview: this.preview,
        image: this.image,
        newPoint: true
      },

    };
  },

  methods: {
    fileSelected(files) {
      if (!files || files.length === 0) return;
      this.video = files[0];
      const blobURL = URL.createObjectURL(this.video);
      this.$refs.videoPlayer.src = blobURL;

      this.$refs.videoPlayer.addEventListener("loadedmetadata", () => {
        this.totalTimeVideo = Math.floor(this.$refs.videoPlayer.duration);
      });

      this.$refs.videoPlayer.addEventListener("timeupdate", () => {
        this.currentTime = Math.floor(this.$refs.videoPlayer.currentTime);
      });

      this.$refs.videoPlayer.addEventListener("canplay", () => {
        if (this.currentTime > 0 && this.newPoint) {
          this.pausedVideo();
        }
        this.newPoint = true;
      });
    },

    pausedVideo() {
      if (!this.$refs.videoPlayer.paused) {
        this.$refs.videoPlayer.pause();
      }
    },

    async seeking() {
      if (this.video) {
        this.currentTime = Math.floor(this.$refs.videoPlayer.currentTime);

        if (this.currentTime > 0 && this.newPoint) {
          this.pausedVideo();
          this.newPoint = true;
        }
      }
    },

    async addNewQuestion() {
      await this.seeking();
      const verify = this.questions.find(
        (q) => q.time === this.getTime(Math.floor(this.currentTime))
      );
      if (verify) {
        this.$toast.open({
          message: this.translationsVue.games_videoquiz_exist_question,
          type: "warning",
          duration: 5000,
          position: "top-right",
        });
        return;
      }

      this.currentQuestion = {
        id: this.questions.length + 1,
        time: this.getTime(this.currentTime),
        seconds: this.currentTime,
        question: this.question,
        answers: [
          {
            id: 1,
            answer: "",
            correct: false,
          },
        ],
        preview: this.preview,
        image: this.image,
      };

      this.openModalEdit(this.currentQuestion);
    },

    openModal() {
      const dialog = this.$el.querySelector("dialog");
      dialog.showModal();
    },

    closeModal() {
      this.question = "";
      this.preview = "/assets/common/add_image_file.svg";
      this.image = null;

      const dialog = this.$el.querySelector("dialog");
      dialog.close();
    },

    addQuestion() {
      const answer = this.currentQuestion.answers.filter(
        (ans) => ans.answer !== ""
      );
      const verify = this.currentQuestion.answers.filter(
        (ans) => ans.correct === true && ans.answer !== ""
      );
      if (
        answer.length < 2 ||
        verify.length === 0 ||
        this.currentQuestion.question === ""
      ) {
        this.$toast.open({
          message: this.translationsVue.games_videoquiz_message_validate_answer,
          type: "warning",
          duration: 5000,
          position: "top-right",
        });
      } else {
        const answers = this.currentQuestion.answers.filter(
          (ans) => ans.answer !== ""
        );

        if (!this.questions.some( question => question.time === this.currentQuestion.time)) {
          this.questions.push({
            id: this.questions.length + 1,
            time: this.getTime(this.currentTime),
            seconds: this.currentTime,
            question: this.question,
            answers: [
              {
                id: 1,
                answer: "",
                correct: true,
              },
            ],
            image: this.image,
            preview: this.preview,
          });
        }

        this.questions = this.questions.map((q) => {
          const blob =
            this.image !== null
              ? new Blob([this.image], { type: "image/png" })
              : null;
          if (q.id === this.currentQuestion.id) {
            q.question = this.currentQuestion.question;
            q.answers = answers;
            q.preview = this.preview;
            q.image = blob;
          }
          return q;
        });

        this.enableButtomSave();
        this.orderQuestionsByTime();

        this.closeModal();
      }
    },

    addAnswer() {
      this.currentQuestion.answers.push({
        id: this.currentQuestion.answers.length + 1,
        answer: "",
        correct: false,
      });
    },

    checkCorrect(index) {
      this.currentQuestion.answers.forEach((ans, i) => {
        if (i === index) {
          ans.correct = true;
        } else {
          ans.correct = false;
        }
      });
    },

    deleteAnswer(index) {
      this.currentQuestion.answers.splice(index, 1);
    },

    deleteQuestion(index) {
      this.questions.splice(index, 1);
      this.enableButtomSave();
    },

    loadImage(event) {
      const input = event.target;

      if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = (e) => {
          this.preview = e.target.result;
        };

        this.image = input.files[0];

        reader.readAsDataURL(input.files[0]);
      }
    },

    openModalEdit(question) {
      this.currentQuestion = question;
      this.preview = question.preview;
      this.image = question.image;
      this.openModal();
    },

    getTime(time) {
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time - hours * 3600) / 60);
      const seconds = Math.floor(time - hours * 3600 - minutes * 60);

      const minutesFormat = minutes < 10 ? "0" + minutes : minutes;
      const secondsFormat = seconds < 10 ? "0" + seconds : seconds;
      const hoursFormat = hours < 10 ? "0" + hours : hours;

      return hoursFormat + ":" + minutesFormat + ":" + secondsFormat;
    },

    async saveVideoQuiz() {
      const questions = this.questions.filter(
        (q) =>
          q.question !== "" &&
          q.answers.filter((ans) => ans.answer !== "").length > 1
      );
      if (questions.length >= 1) {
        const formData = new FormData();
        formData.append("idChapter", this.chapterId);
        formData.append("video", this.video);
        formData.append("title", this.titleVideo);
        formData.append("questions", JSON.stringify(questions));
        formData.append("durationVideo", this.totalTimeVideo);

        this.process = true;

        const data ={
          formData,
          domainVimeo: this.vimeoUploadSubdomain
        }
        await this.$store.dispatch("videoquizModule/createVideoQuiz", data);
        this.process = false;

        await this.fetchVideoQuiz();
        this.$refs["closeChapterContentModal"].click();
      } else {
        this.$toast.open({
          message:
            this.translationsVue.games_videoquiz_validate_to_add_question,
          type: "warning",
          duration: 5000,
          position: "top-right",
        });
      }
    },

    enableButtomSave() {
      const buttonSave = document.getElementById("save-quiz");
      const questions = this.questions.filter(
        (q) =>
          q.question !== "" &&
          q.answers.filter((ans) => ans.answer !== "").length > 1
      );

      if (questions.length > 0 || buttonSave.hasAttribute("disabled")) {
        buttonSave.removeAttribute("disabled");
        buttonSave.classList.remove("btn-secondary");
        buttonSave.classList.add("btn-primary");
      } else {
        buttonSave.setAttribute("disabled", "disabled");
        buttonSave.classList.add("btn-secondary");

        if (buttonSave.classList.contains("btn-primary")) {
          buttonSave.classList.remove("btn-primary");
        }
      }
    },

    orderQuestionsByTime() {
      this.questions.sort((a, b) => {
        const timeA = a.time.split(":");
        const timeB = b.time.split(":");

        const hoursA = parseInt(timeA[0]);
        const minutesA = parseInt(timeA[1]);
        const secondsA = parseInt(timeA[2]);

        const hoursB = parseInt(timeB[0]);
        const minutesB = parseInt(timeB[1]);
        const secondsB = parseInt(timeB[2]);

        const totalSecondsA = hoursA * 3600 + minutesA * 60 + secondsA;
        const totalSecondsB = hoursB * 3600 + minutesB * 60 + secondsB;

        return totalSecondsA - totalSecondsB;
      });
    },

    goToTimeVideo(time) {
      this.newPoint = false;
      const timeSplit = time.split(":");
      const hours = parseInt(timeSplit[0]);
      const minutes = parseInt(timeSplit[1]);
      const seconds = parseInt(timeSplit[2]);

      const totalSeconds = hours * 3600 + minutes * 60 + seconds;
      const video = document.querySelector("video");
      video.currentTime = totalSeconds;
    },

    previewImage(image) {
      return image ? image : "/assets/common/add_image_file.svg";
    },

    async fetchVideoQuiz() {
      await this.$store.dispatch(
        "videoquizModule/fetchVideoQuiz",
        this.chapterId
      );
    },
  },
};
</script>

 <style scoped lang="scss"> 
.new-video-quiz {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  height: 100%;
  .preview-image {
    width: 100%;
    height: 165px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    cursor: pointer;
  }

  .preview-image-default {
    background-size: 30%;
  }
  .preview-image-table {
    width: 30px;
    height: 30px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid #ccc;
  }

  .class-question-image {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 1rem;

    .title-question {
      flex: 1;
    }

    .image {
      width: 300px;
    }
  }
  .goto-video {
    cursor: pointer;
  }

  input[type="file"] {
    display: none;
  }

  .trash-question {
    display: flex;
    justify-content: center;
    align-content: center;
    flex-direction: column;

    .trash {
      cursor: pointer;
    }
  }

  .form-check-input:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
  }

  dialog {
    border: none;
    max-width: 50%;
    border-radius: 0.5rem;
    margin: auto;
    padding: 1.2rem;
  }
}
</style>
