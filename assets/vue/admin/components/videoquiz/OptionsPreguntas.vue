<template>
  <div>   
    <div> 
      Preguntas Video:
    </div> 
    <div>
      <loader :isLoaded="!showCalled"></loader>
      <div v-if="showCalled">
        <table  class="table datagrid">
          <thead class="thead-light">
            <tr>
              <th><span>Tiempo actual</span></th>
              <th><span>Imagen</span></th>      
              <th><span>Pregunta</span></th>  
              <th><span>Respuesta</span></th>   
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(pregunta, index) in registrosPreguntas" :key="index">
              <td>{{pregunta.currenttime}}</td>
              <td>{{pregunta.image}}</td>
              <td>{{pregunta.texto.substring(0, 20)}}</td>
              <td>{{pregunta.respuestas.substring(0,30)}}</td>
              <td>
                <div class="cursor" @click="llamarVideo(pregunta)"> 
                  <i class="fa fa-play"></i> Ver en video
                </div>
                <div class="cursor" @click="modifyPregunta(index)"> 
                  <i class="fa fa-pen"></i> Modificar
                </div>
                <div class="cursor" @click="deletePregunta(pregunta.id)">
                  <i class="fa fa-trash"></i> Eliminar
                </div>                
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div>
      <div class="options" v-if="verPreguntasFormulario">
        <div class="title">
          <label>Pregunta:</label>
        </div>              
        <div>
          <label>Tiempo Pregunta (segundos): {{tiempoPregunta}}</label>
        </div> 
        <div class="question">
          <label for="title">Pregunta:</label>
          <input type="text" id="texto" v-model="texto" placeholder="Texto de la pregunta">
        </div>  
        <div class="row">
          <div class="question" >
            <label for="image">Imagen: {{image}}</label>
            <form>
              <input
                type="file"
                ref="image"
                accept="image/*"
                class="form-control-file"
                @change="fileSelected($event.target.files)"
              />
            </form>     
          </div>        
        </div>  

        <div class="question">
          <label for="respuestas">Respuestas:</label>
          <div class="row">
            <div class="col-sm-2"></div> 
            <div class="col-sm-10">
              <div v-for="(respuesta, index) in respuestas" :key="index">
                <label id="labelIndex">{{index+1}}</label>
                <label id="labelRespuestas">{{respuesta.texto}} --- {{respuesta.valor}}</label>
                <div class="btnWord" @click="editarRespuesta(respuesta, index)"><i class="fa fa-pen"></i> Modificar </div>
                <div class="btnWord" @click="bajarRespuesta(index)" v-if="index<respuestas.length-1"><i class="fa fa-arrow-down"></i> Bajar </div>
                <div class="btnWord" @click="subirRespuesta(index)" v-if="index>0"><i class="fa fa-arrow-up"></i> Subir </div>
                <div class="btnWord" @click="borraRespuesta(index)"><i class="fa fa-trash"></i> Borrar </div>
              </div>             
            </div>     
          </div> 
          <div class="row">
            <div class="col-sm-2"></div> 
            <div class="col-sm-8" v-if="newRespuestaActive"> 
              <div class="question">
                <input type="text" id="newText" v-model="newText" placeholder="Texto de la respuesta"> 
                <div class="select-block"> 
                  <label for="type">Selecciona: </label>

                  <select id="type" v-model="selected">
                    <option v-for="(option, index) in optionsRespuesta" :key="index" v-bind:value="option.value">
                      {{ option.text }}
                    </option>
                  </select>
                </div> 

                <div class="btnWord" @click="addRespuesta()"><i class="fa fa-plus-circle"></i> Guardar Respuesta </div>            
              </div>     
            </div>   
            <div> 
              <button @click="adicionarRespuesta" class="btn btn-primary ml-1" v-if="adicionarRespuestaActive">
                <i class="fa fa-plus"></i> Adicionar Respuesta
              </button>
            </div>                
            <div>       
                <label class="labelMensaje" v-if="mensajeActivo">{{mensaje}}</label>
            </div>
            <div class="col-sm-2"></div>   
          </div>           
          <div class="question"> 
            <button @click="guardarPregunta" class="btn btn-primary ml-1" v-if="true">
              <i class="fa fa-plus"></i> Guardar Pregunta
            </button>
          </div>
        </div>

      </div>    
    </div>
  </div>
</template>
  
<script>
  import { get } from "vuex-pathify";
  import Loader from "../Loader";
  import VideoPlayer from "../VideoPlayer";
  export default {
    name: "App",
    props: {
        registroQuiz: {
            type: Object,
            default: {},
        },
    },
    data: () => ({
      videoquizId:0,
      tiempoPregunta:'',
      texto:'',
      image:'',
      respuestas:[],
      imageRoute: '',
      registrosPreguntas:[],
      newRespuestaActive:false,
      editRespuestaActive:false,
      elementoModificado:0,
      adicionarRespuestaActive:true,     
      optionsRespuesta: [
        { text: 'Falso', value: false },
        { text: 'Verdadero', value: true }
      ],    
      selected: false,
      newText:'',
      verPreguntasFormulario:false,
      called:true,
      mensajeActivo:false,
      mensaje:'',
      intermedio:'',
      editionActive:false,
      idPregunta:0,
    }),
    created() {  
      this.verPreguntasFormulario = this.registroQuiz.adicionarPregunta;
      this.tiempoPregunta = this.registroQuiz.currentTime;
      this.videoquizId =   this.registroQuiz.id; 
      this.cargarPreguntas(this.videoquizId);     
    },
    
    components: {
      VideoPlayer,
      Loader,
    },

    computed: {
      ...get("callModule", ["isLoading"]),

      showCalled() {
        return (!this.isLoading()&& this.called);
      },
    },

    methods: {
      async guardarPregunta(){
        if(this.texto === '' ){
          this.mensaje="Debe digitar la pregunta";
          this.mensajeActivo = true;
        }else if(this.respuestas.length === 0 ){
          this.mensaje="Debe digitar las respuestas";
          this.mensajeActivo = true; 
        }else{
          this.verPreguntasFormulario=false;
          let respuestasString = '';
          this.respuestas.forEach((respuesta,index ) => {           
            respuestasString = respuestasString + respuesta.texto + ";;" + respuesta.valor+"\\;";
          })        
          respuestasString = respuestasString.substring(0, respuestasString.length - 2);//quita caracteres sobrantes

          if(this.editionActive){
              const data = {
              id: this.idPregunta,
              currenttime: this.tiempoPregunta,
              texto: this.texto,
              image: this.image,  
              respuestas: respuestasString,
            };

            await this.$store.dispatch("videoquizModule/editBlockPreguntas", data);
            this.cargarPreguntas(this.videoquizId);
          }else{
            const data = {
              videoquizId: this.videoquizId,
              currenttime: this.tiempoPregunta,
              texto: this.texto,
              image: this.image,  
              respuestas: respuestasString,
            };

            await this.$store.dispatch("videoquizModule/setBlockPreguntas", data);
            this.cargarPreguntas(this.videoquizId);
          }

          this.limpiarFormulario();
        }    
      },
      editarRespuesta(respuesta, index){
        this.newRespuestaActive = true; // abre el fourmulario para edición
        this.newText = respuesta.texto;
        this.selected = respuesta.valor;
        this.editRespuestaActive = true; //dice que está en edición
        this.elementoModificado= index; //nùmero del elemento que se está modificando
        this.mensajeActivo = false; 
      },
      bajarRespuesta(index){
        this.intermedio = this.respuestas[index];
        this.$set(this.respuestas, index, this.respuestas[index+1]);
        this.$set(this.respuestas, index+1, this.intermedio);
        this.mensajeActivo = false; 
      },
      subirRespuesta(index){
        this.intermedio = this.respuestas[index];
        this.$set(this.respuestas, index, this.respuestas[index-1]);
        this.$set(this.respuestas, index-1, this.intermedio);
        this.mensajeActivo = false; 
      },
      borraRespuesta(index){
        this.respuestas.splice(index, 1);
        this.mensajeActivo = false; 
      },
      addRespuesta(){//adiciona la respuesta al array de respuestas
        let objRespuesta={}; 
        objRespuesta.texto = this.newText;
        objRespuesta.valor = this.selected;
        if(this.editRespuestaActive) this.respuestas[this.elementoModificado] = objRespuesta;
        else this.respuestas.push(objRespuesta);
        this.newText = '';
        this.selected = false;
        this.mensajeActivo = false; 
      },
      adicionarRespuesta(){// abre el formulario para nuevas respuestas... lo limita a tres
        if(this.respuestas.length>2){
          this.mensajeActivo = true;
          this.mensaje = 'El máximo de respuestas es 3';
        }else{
          this.mensajeActivo = false;
          this.mensaje = '';
          this.newText = '';
          this.selected = false;
          this.newRespuestaActive=true;
        }
      },
      modifyPregunta(index){ 
        this.editionActive=true;
        let respuestasIntermedio=[];    
        this.idPregunta = this.registrosPreguntas[index].id;
        this.tiempoPregunta=this.registrosPreguntas[index].currenttime;
        this.texto=this.registrosPreguntas[index].texto;
        this.image=this.registrosPreguntas[index].image;
        respuestasIntermedio=this.registrosPreguntas[index].respuestas.split("\\;");
        respuestasIntermedio.forEach((value,index ) => {
          let valor = value.split(";;");
          let objRespuesta={}; 
          objRespuesta.texto = valor[0];
          objRespuesta.valor = valor[1];
          this.respuestas.push(objRespuesta);
        });      
        this.verPreguntasFormulario=true;
        this.mensajeActivo = false; 
      },
      deletePregunta(value){
          const data = {
          id: value,
        };

        this.$store.dispatch("videoquizModule/deleteLinePreguntas", data)
            .then((response) => {
              this.cargarPreguntas(this.videoquizId);
              this.limpiarFormulario();
            });
      },
      limpiarFormulario(){    
        this.idPregunta = 0;
        this.tiempoPregunta= 0;
        this.texto= '';    
        this.videoURL = '';
        this.respuestas=[];
        this.registrosPreguntas = [];
        this.newRespuestaActive = false;
        this.mensajeActivo = false;
        this.verPreguntasFormulario=false;
      },
      fileSelected(files) {
        if (!files || files.length === 0) return;

        this.image = files[0];
      },
      changeVideoPlaying(url) {
        this.videoURL = url;
      },
      cargarPreguntas(videoquizId){ 
        const data = {
          videoquiz_id : videoquizId,
        };
        this.called = false;

        this.$store.dispatch("videoquizModule/reloadBlocksPreguntas", data)
            .then((response) => {
              this.called = true;
              this.registrosPreguntas = response.data.data;
            });
      },
      llamarVideo(pregunta){   
        const data = {
          id:  pregunta.id,
          url: this.registroQuiz.url,
          pathImage: 'uploads/games/videoquiz/',
          llamado:2, //carga optionsVideo,
          currentTime: pregunta.currenttime,
        }
        this.$emit('detailLine', data);
      },
    },
  };
</script>
  
<style>
  body {
    font-family: Roboto;
  }
  
  .options{
    height: 100%;
    flex:2;

    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  .title{
    color: black;
    font-size: 1.5rem
  }
  .word{
    width: 35%;
    margin: .5rem;
    border: 1px solid #5ae8e8;
    border-radius: 25px;
    padding: .5rem;
  }

  .select-block{
    padding: .5rem;
  }

  #texto, #newText{
    border: 1px solid #5ae8e8;
    border-radius: 25px;
    height: 2.5rem;
    padding: .5rem;
  }
  .question{
    display: flex;
    flex-direction: column;
    margin-bottom: .5rem;
    padding: .5rem;
  }

  select{
    background-color: #fff;
    border: 1px solid #5ae8e8;
    border-radius: 25px;

    width: 35%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
  }
  .cursor{
    cursor: pointer;
  }
  #labelIndex{
    width: 5%;
  }
  .labelRespuestas{
    width: 35%;
    margin-right: .5rem;
  }
  .btnWord{
    display: inline;
    margin-left: .5rem;
    cursor: pointer;
  }
  .labelMensaje{    
    width: 60%;
    padding: .5rem;
    align-content: center;
    color: red;
  }
</style>