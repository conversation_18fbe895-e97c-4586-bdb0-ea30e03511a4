<template>
  <div class="ListMaterial p-3">     
    <div
      class="w-100 d-flex align-items-center justify-content-center flex-column"
      v-if="loading"
    >
      <Spinner />
    </div>   

    <div v-else>
      <div v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS)" class="ListMaterial--header mt-3">
        <button class="btn btn-primary" @click="openMaterialCourseModal()">
          <i class="fa fa-plus mr-2"></i>{{ $t("ANNOUNCEMENT.ADD_MATERIAL") }}
        </button>
      </div>
      <div class="ListMaterial--content my-3">
        <DataNotFound
          :hide-on="!files.length"
          :text="$t('ANNOUNCEMENT.MATERIALSTAB.NOT_FOUND') || ''"
          icon="fa-book"
          :banner="true"
        />
        <table class="table table-condensed" v-if="files.length">
          <thead>
            <tr>
              <th style="width: 50px"></th>
              <th>{{ $t("NAME") }}</th>
              <th class="text-center text-nowrap">
                {{ $t("FILE_UPLOAD.FILE_TYPE") }}
              </th>
              <th class="text-center text-nowrap">{{ $t("DATE") }}</th>
              <th class="text-center">{{ $t("ACTIVE") }}</th>
              <th class="text-center">{{ $t("LIBRARY.COMMENTS.PREVIEW") }}</th>
              <th class="text-center">{{ $t("DOWNLOADABLE") }}</th>
              <th v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS)" class="text-center" style="width: 90px">
                {{ $t("ACTIONS") }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(file, index) in files" :key="'fileMaterial-' + file.id">
              <td>
                <button
                  type="button"
                  class="btn btn-info"
                  @click="openIndex = index"
                >
                  <i class="fa" :class="fileIcons[file.typeMaterial]"></i>
                </button>
                <Viewer
                  :custom="false"
                  :base-path="basePath"
                  :file="file"
                  :modal="true"
                  :open="openIndex === index"
                  @close="openIndex = -1"
                />
              </td>
              <td>{{ file.name }}</td>
              <td class="text-center text-nowrap">
                {{ fileTypes[file.type]}}<span class="text-uppercase">{{ getExtension(index) }}</span>
              </td>
              <td class="text-center text-nowrap">
                <DateTimeTag :datetime="file.createdAt" />
              </td>
              <td class="text-center">
                <div class="custom-control custom-switch position-relative">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    :id="'switch_active_' + file.id"
                    v-model="file.isActive"
                    :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS)"
                    @change="changeActiveStatus(file)"
                  />
                  <label
                    class="custom-control-label"
                    :for="'switch_active_' + file.id"
                  ></label>
                  <span v-if="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS)" class="tooltipMessage">{{ $t('NOT_ALLOWED') }}</span>
                </div>
              </td>
              <td class="text-center">
                <div
                  v-if="!file?.disableVisible"
                  class="custom-control custom-switch position-relative">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    :id="'switch_visibility_' + file.id"
                    v-model="file.isVisible"
                    :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS) || !file.isActive"
                    @change="changeVisibilityStatus(file)"
                  />
                  <label
                    class="custom-control-label"
                    :for="'switch_visibility_' + file.id"
                  ></label>
                  <span v-if="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS) || !file.isActive" class="tooltipMessage">{{ $t('NOT_ALLOWED') }}</span>
                </div>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                <div
                  v-if="!file?.disableDownload"
                  class="custom-control custom-switch position-relative">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    :id="'switch_' + file.id"
                    v-model="file.isDownload"
                    :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS) || !file.isActive"
                    @change="allowDownload(file.id, file.isDownload, file.disableDownload, file.isVisible)"
                  />
                  <label
                    class="custom-control-label"
                    :for="'switch_' + file.id"
                  ></label>
                  <span v-if="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS) || !file.isActive" class="tooltipMessage">{{ $t('NOT_ALLOWED') }}</span>
                </div>
                <span v-else>-</span>
              </td>
              <td v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS)" class="d-flex align-items-center justify-content-evenly">
                <button
                  type="button"
                  class="btn btn-danger ml-1"
                  @click="deleteMaterial(file.id)"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div
      v-if="showModal"
      class="modal fade"
      id="materialCourseModal"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="materialCourseModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="materialCourseModalLabel">
              {{ $t("ANNOUNCEMENT.ADD_MATERIAL") }}
            </h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              @click="closeMaterialCourseModal()"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div id="materialCourse">
              <AddMaterialCourse
                :id="this.$route?.params?.id || courseId"
                @cancel="closeMaterialCourseModal()"
                @success="onUploadSuccess()"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from "jquery";
import "bootstrap";
import { get } from "vuex-pathify";
import Spinner from "../base/Spinner.vue";
import DataNotFound from "../../../announcement/components/details/DataNotFound.vue";
import AddMaterialCourse from "./AddMaterialCourse.vue";
import Viewer from '../../../common/components/viewer/Viewer.vue'
import DateTimeTag from '../../../announcement/components/details/dateTimeTag.vue'
import { COURSE_PERMISSIONS } from '../../../common/utils/auth/permissions/course.permissions'

export default {
  name: "list-material-course",

  components: {
    Spinner,
    DataNotFound,
    AddMaterialCourse,
    Viewer,
    DateTimeTag
  },

  props: {
    idAnnouncement: {
      type: Number,
      default: 0,
    },

    courseId: {
      type: Number,
      default: 0
    },
  
    origin: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      translationsVue,
      currentSort: "createdAt",
      currentSortDir: "asc",
      filterStatus: "all",
      filterType: "all",
      visor: "",
      autoplay: false,
      files: [],
      openIndex: -1,
      showModal: false,
      fileTypes: [
        "",
        "lectura ",
        "video ",
        "comprimido ",
        "imagen ",
        "documento ",
        "texto ",
      ],
      fileIcons: [
        "",
        "fa-file-pdf",
        "fa-file-video",
        "fa-file-archive",
        "fa-file-image",
        "fa-file-word",
        "fa-file-alt",
      ],
    };
  },

  computed: {
    COURSE_PERMISSIONS() {
      return COURSE_PERMISSIONS
    },
    ...get("materialCourse", [
      "loading",
      "getMaterialsCourse",
      "getRouteCourse",
    ]),

    basePath: get("materialCourse/fileBasePath"),
  
    materialsCourse() {
      return this.getMaterialsCourse();
    },

    routeCourse() {
      return this.getRouteCourse();
    },

    sortedMaterials() {
      if (this.currentSort) {
        return this.materialsCourse?.sort((a, b) => {
          let modifier = 1;
          if (this.currentSortDir === "desc") modifier = -1;
          if (a[this.currentSort] < b[this.currentSort]) return -1 * modifier;
          if (a[this.currentSort] > b[this.currentSort]) return 1 * modifier;
          return 0;
        });
      }

      return this.currentSort;
    },

    idCourse() {
      return typeof idCourse !== 'undefined' && idCourse !== null 
        ? idCourse 
        : this.$route?.params?.id ?? null;    
    }
  },

  watch: {
    materialsCourse(newMaterials) {
      this.files = newMaterials;
    },
  },

  async created() {
    await this.loadFiles()
    if( this.materialsCourse && this.materialsCourse.length === 0) {
      this.files = [];
    } else {
      this.files = this.materialsCourse
    }
  },

  methods: {
    sort: function (s) {
      if (s === this.currentSort) {
        this.currentSortDir = this.currentSortDir === "asc" ? "desc" : "asc";
      }
      this.currentSort = s;
    },


    async deleteMaterial(idMaterial) {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS)) return;

      this.$alertify.confirmWithTitle(
        this.$t("MATERIAL_COURSE.DELETE.CONFIRM.TITLE"),
        this.$t("MATERIAL_COURSE.DELETE.CONFIRM.DESCRIPTION"),
        async () => {

          let data = {};
          if (this.idAnnouncement !== 0) {
            data = {
              course: this.idCourse,
              idMaterialCourse: idMaterial,
              announcement: this.idAnnouncement,
            };
          } else {
            data = {
              course: this.idCourse,
              idMaterialCourse: idMaterial,
            };
          }

          try {
            await this.$store.dispatch("materialCourse/deleteMaterialCourse", data);
            this.$toast.success(
              this.$t("MATERIAL_COURSE.DELETE.SUCCESS") + ""
            );
            this.loadFiles();
          } catch (error) {
            this.$t("MATERIAL_COURSE.DELETE.FAILED") + ""
          }
        },
        () => {}
      );
    },

    async allowDownload(idMaterial, state, disableDownload = false, isVisible) {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS)) return;
      if (disableDownload) return null;
      try {
        const data = {
          idMaterialCourse: idMaterial,
          isDonwload: state,
        };
        await this.$store.dispatch("materialCourse/allowDonwload", data);

        this.$toast.success(this.$t("MATERIAL_COURSE.DOWNLOADABLE.SUCCESS"));
      } catch (error) {
        this.$toast.error(this.$t("MATERIAL_COURSE.DOWNLOADABLE.FAILED"));
      }
    },

    async changeActiveStatus(file) {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS)) return;
      try {
        await this.$store.dispatch("materialCourse/setMaterialCourseActive", {
          id: file.id,
          isActive: file.isActive,
        });
        await this.activePreviewAndDownload(file);
        this.$toast.success(this.$t("MATERIAL_COURSE.DOWNLOADABLE.SUCCESS"));
      } catch (error) {
        this.$toast.error(this.$t("MATERIAL_COURSE.DOWNLOADABLE.FAILED"));
      }
    },

    async changeVisibilityStatus(file) {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.MATERIALS)) return;
      if (file.disableVisible) return null;
      try {
        await this.$store.dispatch("materialCourse/setMaterialCourseVisibitily", {
            id: file.id,
            visibitily: file.isVisible,
        })
        this.$toast.success( this.$t("MATERIAL_COURSE.DOWNLOADABLE.SUCCESS"));
      } catch (error) {
        this.$toast.error( this.$t("MATERIAL_COURSE.DOWNLOADABLE.FAILED"));
      }
    },

    async loadFiles() {
      let data = {};
      if (this.idAnnouncement !== 0) {
        data = {
          idCourse: this.idCourse,
          idAnnouncement: this.idAnnouncement,
        };
      } else {
        data = {
          idCourse: this.idCourse,
        };
      }
      await this.$store.dispatch("materialCourse/fetchMaterialCourse", data);
    },

    openMaterialCourseModal() {
      this.showModal = true;
      this.$nextTick(() => {
        $("#materialCourseModal").modal({
            show: true,
            static: true,
            backdrop: false,
            keyboard: false,
        });
      });
    },

    closeMaterialCourseModal() {
      this.showModal = false;
      $("#materialCourseModal").modal("hide");
    },

    onUploadSuccess() {
      this.closeMaterialCourseModal();
      this.loadFiles();
    },

    getExtension(index) {
      const splitted = (this.files[index].name || "").split(".");
      return splitted[splitted.length - 1] || "";
    },
    async activePreviewAndDownload(file){
      try {
        await this.$store.dispatch("materialCourse/setMaterialCourseVisibitily", {
          id: file.id,
          visibitily: file.isVisible,
        })

        const data = {
          idMaterialCourse: file.id,
          isDonwload: file.isActive,
        };

        await this.$store.dispatch("materialCourse/allowDonwload", data);

        $("#switch_visibility_" + file.id).prop('checked', file.isActive);
        $("#switch_" + file.id).prop('checked', file.isActive);
      } catch (error) {
        this.$toast.error( this.$t("MATERIAL_COURSE.DOWNLOADABLE.FAILED"));
      }
    }
  },
};
</script>

 <style scoped lang="scss"> 
@import "/assets/css/app.scss";

.ListMaterial {
  width: 100%;

  &--header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  &--content {
    overflow-x: auto;
  }

  ::v-deep {
    .modal-header {
      background-color: var(--color-neutral-darker);
      align-items: center;

      .modal-title {
        color: white;
      }
      .btn-close {
        filter: invert(1) grayscale(100%) brightness(200%);
      }
    }

    .text-gray {
      color: var(--color-neutral-mid-dark);
    }
  }
}

.modal-body {
  text-align: left;

  p:nth-child(1) {
    font-size: 1.3rem;
    font-weight: bold;
  }
}

.body-visor {
  padding: 0;
}

input[type="checkbox"] {
  background: $color-app;
  border: none;
  border-radius: 0.2rem;
  color: white;
  width: 1.5rem;
  height: 1.5rem;
}

label {
  font-size: 1.4rem;
  /* color: var(--color-secondary-darkest); */
}

.custom-switch.position-relative {
  width: fit-content;
  margin: auto;
}

.custom-switch.position-relative:hover > .tooltipMessage {
  display: initial;
}

.tooltipMessage {
  display: none;
  font-size: 0.8rem;
  background-color: $color-primary-lighter;
  position: absolute;
  padding: 0.25rem 0.75rem;
  border-radius: 0.3rem;
  bottom: 2rem;
  left: -70px;
  width: 150px;
  z-index: 1;
}

.description:hover + .tooltipMessage {
  display: initial;
}
</style>