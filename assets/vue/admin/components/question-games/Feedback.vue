<template>
  <div class="feedback">
    <div class="title-feedback">
      <div class="form-check form-switch mb-4">
        <input
          type="checkbox"
          id="is_feedback"
          name="random"
          data-ea-align="left"
          class="form-check-input"
          v-model="feedback.isFeedback"
        />
        <label class="checkbox-switch form-check-label" for="is_feedback">
          {{ translationsVue.game_feedback_title }}</label
        >
      </div>
    </div>
    <div class="feedbacks" v-if="feedback.isFeedback">
      <div class="feedback-positive">
        <BaseTextTarea
          :label="translationsVue.game_feedback_title_positive"
          :max="255"
          :value.sync="feedback.correct"
          placeholder="Ejemplo muy bien"
          :required="true"
          :rows="3"
          :submitted="submitted"
          :customBorderColor="'green'"
        ></BaseTextTarea>
      </div>

      <div class="feedback-negative">
        <BaseTextTarea
          :label="translationsVue.game_feedback_title_negative"
          :max="255"
          :value.sync="feedback.incorrect"
          placeholder="Ejemplo: ¡Vaya!"
          :required="true"
          :rows="3"
          :submitted="submitted"
          :customBorderColor="'red'"
        ></BaseTextTarea>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    feedback: {
      type: Object,
      default: () => ({}),
    },

    submitted: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      translationsVue,
      isFeedback: false,
    };
  },
};
</script>

 <style scoped lang="scss"> 
.feedback {
  margin-top: 2rem;
  background-color: var(--color-primary-lightest);
  padding: 1rem;

  label.form-check-label {
    margin-left: 1rem !important;
    font-weight: bold;
  }

  .form-check-input:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
  }

  .form-switch {
    padding: 0;
  }

  .feedbacks {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    grid-template-areas: "feedback-positive feedback-negative";

    .feedback-positive {
      grid-area: feedback-positive;
    }

    .feedback-negative {
      grid-area: feedback-negative;
    }

    @media (max-width: 768px) {
      grid-template-areas:
        "feedback-positive feedback-positive"
        "feedback-negative feedback-negative";
    }
  }
}
</style>