<template>
  <div class="list-task">
    <table
      class="table table-responsive-sm datagrid"
      v-if="sortedFileTask && sortedFileTask.length > 0"
    >
      <thead>
        <tr>
          <th scope="col"></th>
          <th scope="col">
            <a href="javascript:void(0)" @click="sort('fileOriginalName')">
              {{ translationsVue.material_course_configureFields_file }}
              <i class="fa fa-fw fa-sort"></i>
            </a>
          </th>
          <th scope="col" colspan="2" width="12%" v-if="allowDelete()">
            <a href="javascript:void(0)" @click="sort('isDownload')">
              {{ translationsVue.material_course_download }}
              <!-- <i class="fa fa-fw fa-sort"></i> -->
            </a>
          </th>
          <th scope="col">
            <a href="javascript:void(0)" @click="sort('fileMimeType')">
              {{ translationsVue.material_course_configureFields_type }}
              <i class="fa fa-fw fa-sort"></i>
            </a>
          </th>

          <th scope="col">
            <a href="javascript:void(0)" @click="sort('createdAt')">
              {{ translationsVue.Created_At }}
              <i class="fa fa-fw fa-sort"></i>
            </a>
          </th>
          <th scope="col" class="text-right">{{ translationsVue.Actions }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(taskFile, index) in sortedFileTask" :key="taskFile.id">
          <th scope="row">{{ index + 1 }}</th>
          <td>{{ taskFile.fileOriginalName }}</td>
          <td colspan="2" v-if="allowDelete()">
            <input
              type="checkbox"
              :id="`switch${taskFile.id}`"
              v-model="taskFile.isDownload"
              v-on:input="allowDownload(taskFile.id, taskFile.isDownload)"
            />
          </td>
          <td>{{ taskFile.fileMimeType }}</td>
          <td>{{ taskFile.createdAt }}</td>
          <td class="text-right">
            <button
              v-if="allowDelete(taskFile.announcement) == true"
              type="button"
              class="btn btn-danger btn-sm"
              data-bs-toggle="modal"
              :data-bs-target="`#modalDelete${taskFile.id}`"
            >
              <i class="fas fa-trash-alt"></i>
            </button>
            <span v-else>--</span>

            <div
              class="modal fade"
              :id="`modalDelete${taskFile.id}`"
              tabindex="-1"
              aria-labelledby="modalDeletelLabel"
              aria-hidden="true"
            >
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-body">
                    <p>
                      {{
                        translationsVue.taskCourse_configureFields_question_delete
                      }}
                    </p>
                    <p>
                      {{
                        translationsVue.material_course_configureFields_question_decition
                      }}
                    </p>
                  </div>
                  <div class="modal-footer">
                    <button
                      type="button"
                      class="btn btn-secondary"
                      data-bs-dismiss="modal"
                    >
                      {{ translationsVue.cancelar }}
                    </button>
                    <button
                      type="button"
                      class="btn btn-danger"
                      @click="deleteMaterial(taskFile.id)"
                    >
                      {{
                        translationsVue.material_course_configureFields_delete
                      }}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Button trigger modal -->
            <button
              type="button"
              class="btn btn-secondary btn-sm"
              data-bs-toggle="modal"
              :data-bs-target="`#visorMaterial${taskFile.id}`"
              @click="typeComponent(taskFile.typeMaterial), playVideo(true)"
              v-if="taskFile.typeMaterial != '3'"
            >
              <i class="fas fa-eye"></i>
            </button>

            <!-- Modal -->
            <div
              class="modal fade"
              :id="`visorMaterial${taskFile.id}`"
              tabindex="-1"
              aria-labelledby="exampleModalLabel"
              aria-hidden="true"
            >
              <div class="modal-dialog modal-xl">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">
                      {{ taskFile.name }}
                    </h5>
                    <button
                      type="button"
                      @click="playVideo(false)"
                      class="btn-close btn-close-white"
                      data-bs-dismiss="modal"
                      aria-label="Close"
                    ></button>
                  </div>
                  <div class="modal-body body-visor">
                    <!--    {{taskFile.id}} {{visor}} -->
                    <component
                      :is="visor"
                      :name="taskFile.filename"
                      route-base="/uploads/task_course/"
                      :key="taskFile.id"
                      :identifierVideo="taskFile.identifierVideo"
                      :urlMaterial="taskFile.urlMaterial"
                      :codeVideo="taskFile.codeVideo"
                      :autoplay="autoplay"
                    />
                  </div>
                  <!--  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Save changes</button>
                  </div> -->
                </div>
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <div v-else class="no-content">
      <p>{{ translationsVue.taskCourse_configureFields_noFile }}</p>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import visorImagen from "./visors/visorImagen";
import visorPdf from "./visors/visorPdf";
import visorVideo from "./visors/visorVideo";
import visorOffice from "./visors/visorOffice";
import visorTxt from "./visors/visorTxt";

export default {
  name: "list-task-course",

  components: {
    visorImagen,
    visorPdf,
    visorVideo,
    visorOffice,
    visorTxt,
  },

  props: {
    idAnnouncement: {
      type: Number,
      default: 0,
    },

    origin: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      translationsVue,
      idCourse,
      idTask,
      currentSort: "createdAt",
      currentSortDir: "asc",
      filterStatus: "all",
      filterType: "all",
      visor: "",
      autoplay: false
    };
  },

  computed: {
    ...get("materialCourse", ["isLoading", "getFilesTask", "getRouteCourse"]),

    filesTask() {
      return this.getFilesTask();
    },

    routeCourse() {
      return this.getRouteCourse();
    },

    sortedFileTask() {
      if (this.currentSort) {
        return this.filesTask?.sort((a, b) => {
          let modifier = 1;
          if (this.currentSortDir === "desc") modifier = -1;
          if (a[this.currentSort] < b[this.currentSort]) return -1 * modifier;
          if (a[this.currentSort] > b[this.currentSort]) return 1 * modifier;
          return 0;
        });
      }

      return this.currentSort;
    },
  },

  async created() {
    const data = {
      idTask: this.idTask,
    };

    this.$store.dispatch("materialCourse/fetchFilesTask", data);
  },

  methods: {
    sort: function (s) {
      if (s === this.currentSort) {
        this.currentSortDir = this.currentSortDir === "asc" ? "desc" : "asc";
      }
      this.currentSort = s;
    },

    allowDelete(announcement) {
      if (this.idAnnouncement !== 0 && announcement !== null && this.origin === '') {
        return true;
      } else if (this.idAnnouncement === 0 && this.origin === '') {
        return true;
      }
      return false;
    },

    typeComponent(typeMaterial) {
      if (typeMaterial === "1") {
        this.visor = "visorPdf";
      } else if (typeMaterial === "2") {
        this.visor = "visorVideo";
      } else if (typeMaterial === "3") {
        this.visor = "visorVideo";
      } else if (typeMaterial === "4") {
        this.visor = "visorImagen";
      } else if (typeMaterial === "5") {
        this.visor = "visorOffice";
      } else if (typeMaterial === "6") {
        this.visor = "visorTxt";
      }
    },

    async deleteMaterial(idFile) {
      const data = {
        idFile: idFile,
        idTask: this.idTask,
      };

      await this.$store.dispatch("materialCourse/deleteFileTask", data);
      location.reload();      
    },

    async allowDownload(idFile, state) {
      const data = {
        idFile,
        isDownload: state ? false : true,
      };
      await this.$store.dispatch("materialCourse/allowDonwloadFileTask", data);
    },

    playVideo(payload){
      this.autoplay = payload;
    }
  },
};
</script>

 <style scoped lang="scss"> 
@import "/assets/css/app.scss";

.list-task {
  margin-top: 2rem;
  .no-content {
    p {
      font-size: 19px;
      text-align: center;
      color: #32383EFF;
      background-color: #fbfbfb;
      height: 15rem;
      display: flex;
      justify-content: center;
      justify-items: center;
      flex-direction: column;
      margin-top: 2rem;
    }
  }
}

.modal-body {
  text-align: left;

  p:nth-child(1) {
    font-size: 1.3rem;
    font-weight: bold;
  }
}

.body-visor {
  padding: 0;
}

input[type="checkbox"] {
  background: $color-app;
  border: none;
  border-radius: 0.2rem;
  color: white;
  width: 1.5rem;
  height: 1.5rem;
}

label {
  font-size: 1.4rem;
  /* color: var(--color-secondary-darkest); */
}
</style>