<template>
  <div class="comment-task-user">
    <div class="header">
      <div class="user">
        <img
          class="avatar"
          :src="avatar"
          :onerror="`this.onerror=null; this.src='${avatarDefault}'`"
        />
        <p>{{ name }}</p>
      </div>
      <div class="date-delivery">{{ comment.sentAt }}</div>
    </div>

    <div class="comment">
      <p>{{ comment.body }}</p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    comment: {
      type: Object,
      default: () => {},
    },

    nameAvatar: {
      type: String,
      default: () => {},
    },
  },
  computed: {
    name() {
      return (
        this.comment?.sender?.firstName + " " /*this.comment?.sender?.lastName*/
      );
    },

    avatar() {
      return `/uploads/users/avatars/${this.comment?.sender?.avatar}`;
    },

    avatarDefault() {
      return `/uploads/users/avatars/default.svg`;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.comment-task-user {
  display: flex;
  gap: 0.4rem;
  flex-direction: column;
  flex-wrap: wrap;
  padding: 0.6rem;
  font-size: 14px;
  background: var(--color-neutral-light);
  border-radius: 5px;
  margin-bottom: 1rem;
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    .user {
      display: flex;
      height: 3rem;
      align-items: center;

      .avatar {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        text-align: center;
        padding: auto;
        display: flex;
        justify-content: center;
        flex-direction: column;
      }
      p {
        font-weight: bold;
        font-size: 16px;
        margin: auto;
        margin-left: 0.5rem;
      }
    }

    .date-delivery {
      font-weight: 600;
    }
  }

  .comment {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-left: 3.5rem;
    margin-top: -1rem;
  }

  p:nth-child(1) {
    font-size: 14px;
  }
}
</style>
