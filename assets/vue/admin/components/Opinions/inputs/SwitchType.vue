<template>
  <div class="SwitchType">
    <p class="my-0">{{ question }}</p>
    <div class="d-flex">
      <label class="form-label m-0 mr-2 text-nowrap">{{ $t("NO") }}</label>
      <div class="custom-control custom-switch">
        <input
          type="checkbox"
          class="custom-control-input"
          v-model="answers"
          disabled="disabled"
        />
        <label
          class="custom-control-label"
        ></label>
      </div>
      <label class="form-label m-0 text-nowrap">{{ $t("YES") }}</label>
    </div>
  </div>
</template>

<script>
import typesMixin from './typesMixin'

export default {
  name: "SwitchType",
  mixins: [typesMixin],
}
</script>