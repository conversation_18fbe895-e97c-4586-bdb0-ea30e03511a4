<template>
  <div class="CheckboxType">
    <p class="my-0">{{ question }}</p>
    <div class="item mt-1" v-for="(answer, index) in answers" :key="'rb_' + index + answer.id">
      <i class="icon" :class="{'fa fa-check-square': answer.checked, 'far fa-square': !answer.checked}"/>
      <span>{{ answer.value }}</span>
    </div>
  </div>
</template>

<script>
import typesMixin from './typesMixin'

export default {
  name: "CheckboxType",
  mixins: [typesMixin],
}
</script>

 <style scoped lang="scss"> 
.CheckboxType {
  .item {
    display: flex;
    gap: 1rem;
    align-items: center;
  }
  .icon {
    font-size: 1.5rem;
    color: var(--color-neutral-mid);
  }
  
  .icon:not(.fa-square) {
    color: var(--color-primary);
  }
}
</style>