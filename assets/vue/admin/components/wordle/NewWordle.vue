<template>
  <div class="new-title">
    <form>
    <div class="row">

      <div class="mb-3 col-md-12">
        <label for="title" class="form-label">{{ translationsVue.content_configureFields_title }}</label>
        <input type="text" class="form-control" name="title" required  v-model="title"/>
      </div>

      <div class="mb-3 col-md-7">
        <label for="title" class="form-label">{{ translationsVue.content_configureFields_title }}</label>
        <input type="text" class="form-control" name="title" required  v-model="title"/>
      </div>

      <div class="mb-3 col-md-5">
        <label for="title" class="form-label">{{ translationsVue.games_text_common_time }} </label>
        <input type="time" id="appt" name="appt"
               class="form-control"
               v-model="time"
               min="00:00:00" max="23:00:00">
      </div>
    </div>
    <div class="mt-xxl-2 text-center">
      <button  type="submit" class="btn-sm btn btn-primary" @click="saveQuestion"> {{ translationsVue.Save }}</button>
    </div>
    </form>
  </div>
</template>

<script>

import { get } from "vuex-pathify";

export default {
  data(){
    return {
      image: null,
      preview: null,
      typeChapter,
      chapterId,
      time: '00:00:30',
      translationsVue,
      title: '',

    }
  },
  computed: {
    ...get("parejasModule", ["getRouteChapter"]),

    routeChapter(){
      return this.getRouteChapter()
    }
  },

  methods:{
    loadImage(event) {
      const input = event.target;

      if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = (e) => {
          this.preview = e.target.result;
        };

        this.image = input.files[0];

        reader.readAsDataURL(input.files[0]);
      }
    },

    async saveQuestion(){
      if(this.title == '' && this.image == null){
        this.$toast.open({
          message:  this.translationsVue.games_validate_memory_match,
          type: "info",
          duration: 5000,
          position: "top-right",
        });
      }
      else{
        const time = this.time;
        const seconds = time.split(':');
        const secondsTime = (+seconds[0]) * 60 * 60 + (+seconds[1]) * 60 + (+seconds[2]);

        const formData = new FormData();
        formData.append('title', this.title);
        formData.append('time', secondsTime);
        formData.append('idChapter', this.chapterId);
        formData.append('image', this.image);

        await this.$store.dispatch('parejasModule/createParejas', formData);
        window.location.href = this.routeChapter;
      }


    },
  }
};
</script>

 <style scoped lang="scss"> 
.new-title{
  .preview-image{
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid #ccc;
  }

  input[type="file"]{
    display: none;
  }
}
</style>
