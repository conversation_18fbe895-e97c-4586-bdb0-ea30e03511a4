<template>
  <div class="setting-multiselect-with-new">
    <multiselect
      v-model="selectedValues"
      :options="formattedOptions"
      :multiple="true"
      :taggable="true"
      track-by="value"
      label="label"
      placeholder="Select options"
      class="form-multiselect"
    />
    <div class="button-group mt-2">
      <button type="button" @click="toggleAddInput" class="btn btn-success btn-sm">
        {{ $t("ADD_NEW") }}
      </button>
      <button
        type="button"
        @click="toggleOptionsList"
        class="btn btn-danger btn-sm"
      >
        {{ showOptionsList ? $t("HIDE_OPTIONS") : $t("SHOW_OPTIONS") }}
      </button>
    </div>
    <div v-if="isAdding" class="add-new mt-2">
      <input
        type="text"
        v-model="newOption"
        class="form-control"
        :placeholder="$t('ADD_NEW_PLACEHOLDER')"
      />
      <button type="button" @click="addNewOption" class="btn btn-primary btn-sm mt-2">
        {{ $t("CONFIRM") }}
      </button>
    </div>
    <div v-if="showOptionsList" class="options-list mt-3">
      <h6>{{ $t("CURRENT_OPTIONS") }}</h6>
      <ul>
        <li
          v-for="(option, index) in formattedOptions"
          :key="option.value"
          class="option-item"
        >
          {{ option.label }}
          <button
            type="button"
            class="btn btn-danger btn-sm ml-2"
            @click="removeOption(index)"
          >
            {{ $t("REMOVE") }}
          </button>
        </li>
      </ul>
    </div>
    <p class="description" v-if="setting.description">{{ setting.description }}</p>
  </div>
</template>

<script>
import Multiselect from 'vue-multiselect';

export default {
  name: 'SettingMultiSelectWithNew',
  components: {
    Multiselect,
  },
  props: {
    setting: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isAdding: false,
      newOption: '',
      showOptionsList: false,
      selectedValues: this.getSelectedOptions(),
    };
  },
  computed: {
    formattedOptions() {
      return this.setting.options.map(option => ({
        label: option,
        value: option,
      }));
    },
  },
  watch: {
    selectedValues(newValue) {
      const valuesOnly = newValue.map(option => option.value);
      this.setting.value = JSON.stringify(valuesOnly);
    },
  },
  methods: {
    getSelectedOptions() {
      const storedValues = this.setting.value ? JSON.parse(this.setting.value) : [];
      return storedValues.map(value => ({
        label: value,
        value,
      }));
    },
    toggleAddInput() {
      this.isAdding = !this.isAdding;
      this.newOption = '';
    },
    toggleOptionsList() {
      this.showOptionsList = !this.showOptionsList;
    },
    addNewOption() {
      if (!this.newOption.trim()) return;

      const newOption = {
        label: this.newOption,
        value: this.newOption,
      };

      this.setting.options.push(newOption.value);

      this.newOption = '';
    },
    removeOption(index) {
      const removedOption = this.formattedOptions.splice(index, 1)[0];

      const originalIndex = this.setting.options.indexOf(removedOption.value);
      if (originalIndex !== -1) {
        this.setting.options.splice(originalIndex, 1);
      }

      this.selectedValues = this.selectedValues.filter(
        selected => selected.value !== removedOption.value
      );

      this.updateSettingValue();
    },
    updateSettingValue() {
      const valuesOnly = this.selectedValues.map(option => option.value);
      this.setting.value = JSON.stringify(valuesOnly);
    },
  },
};
</script>

<style scoped>
  @import 'vue-multiselect/dist/vue-multiselect.min.css';

  .setting-multiselect-with-new {
    margin-bottom: 15px;
  }

  .button-group {
    margin-bottom: 10px;
  }

  .btn {
    margin-right: 5px;
  }

  .add-new {
    margin-top: 10px;
  }

  .options-list ul {
    list-style-type: none;
    padding: 0;
  }

  .options-list li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  .option-item button {
    margin-left: 10px;
  }

  .form-multiselect {
    width: 100%;
  }

  .description {
    font-size: 0.9em;
    color: #666;
  }
</style>