<template>
  <div class="RatingList" :class="{loading: isLoading}">
    <p class="title bgGray">
      <i :class="isLoading ? 'fa fa-circle-o-notch fa-spin fa-fw': icon"></i>
      <span class="title-text">{{ $t(title) }}</span>
    </p>
    <div v-if="isLoading" class="ratingContent">
      <p><i class="fa fa-list fa-5x"></i></p>
      <p><i class="fa fa-list fa-5x"></i></p>
    </div>
    <div v-else class="ratingContent">
      <div v-for="(list, index) in elementList"
          :key="tag + index"
          class="tableContainer">
        <p class="title"><i :class="list.icon || icon"></i> <span>{{ list.title }}</span></p>
        <table>
          <tr>
            <td class="headers" v-for="header in (list.headers || [])">{{ header }}</td>
          </tr>
          <tr class="dataContainer" v-for="item in list.values">
            <td class="iconContainer" :style="{backgroundColor: list.color}">
              <i :class="listIcon"></i> {{ item.value }}
            </td>
            <td class="valueContainer"><span>{{ item.name }}</span></td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name      : "RatingList",
  components: {},
  props     : {
    isLoading: {
      type: Boolean,
      default: true
    },
    icon : {
      type   : String,
      default: "fa fa-user",
    },
    listIcon: {
      type   : String,
      default: "fa fa-star",
    },
    title: {
      type   : String,
      default: "Title",
    },
    tag: {
      type   : String,
      default: "RatingList",
    },
    headers: {
      type   : Array,
      default: () => ([]),
    },
    elementList: {
      type   : Array,
      default: () => ([]),
    },
  },
};
</script>

 <style scoped lang="scss"> 
.RatingList {
  width: 100%;
  background-color: white;
  border: 1px solid #E7EBF0;
  box-shadow: 0 0 2px 2px #ECEFF1;
  overflow: hidden;
  border-radius: 7px;

  p { margin: 0 auto; }

  .ratingContent {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(390px, 1fr))
  }

  .tableContainer {
    padding: 1rem;
    table {
      margin: 0;
      border-collapse: separate;
      border-spacing: 0.5rem;
      text-align: center;
      vertical-align: middle;
    }
  }

  .headers {
    background-color: #90A4AE;
  }

  .headers, .iconContainer, .headers span {
    border-radius: 7px;
    padding: 0.2rem 1rem;
    font-weight: bold;
    color: white;
  }

  .iconContainer{
    white-space: nowrap;
  }

  .valueContainer {
    text-align: left;
    text-transform: capitalize;
  }

  .title {
    color: #37474F;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: bold;
    box-shadow: 0 3px 4px -2px #CFD8DC;

    &.bgGray {
      background-color: #E7EBF0;
      box-shadow: initial;
    }
  }

  &.loading {
    .ratingContent p {
      animation: opacityAnimation 1.1s linear infinite alternate;
      padding: 1rem;
      text-align: center;
      color: #CFD8DC;
    }
  }
}
</style>
