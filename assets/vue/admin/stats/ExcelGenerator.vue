<template>
  <button type="button" class="btn btn-primary" @click="generateExcel">
    <i class="fa fa-file-excel"></i> {{ text.length ? text : $t('EXCEL_DOWNLOAD_BUTTON') }}
  </button>
</template>

<script>
import { utils, writeFileXLSX } from "xlsx";

export default {
  name: "ExcelGenerator",
  props: {
    options: {
      type: Object,
      default: () => ({})
    },
    text: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      column: 0,
      row: 2,
      sheetdata: [[]],
    }
  },
  methods: {
    generateExcel() {
      const { sheets, sourceData, fileName } = { sheets: [], sourceData: {}, fileName: 'file', ...this.options };

      const workbook = utils.book_new();

      sheets.forEach((sheet) => {
        this.initSheet();
        sheet.tables.forEach((table) => {
          this.writeCell(table.name);
          table.cells.forEach((cell) => this.writeTableColumn(sourceData[table.key], cell.key, cell.name));
          this.addColumn(-1);
        });
        utils.book_append_sheet(workbook, utils.aoa_to_sheet(this.sheetdata, {}), sheet.name);
      });

      writeFileXLSX(workbook, `${fileName}.xlsx`);
    },
    initSheet() {
      this.column = 0;
      this.row = -1;
      this.sheetdata = [[]];
    },
    addColumn(rowInit = 0) {
      this.column += 1;
      this.row = rowInit;
    },
    addRow() {
      this.row += 1;
      this.sheetdata[this.row] = [...(this.sheetdata[this.row] || [])];
    },
    writeCell(content) {
      this.addRow();
      this.sheetdata[this.row][this.column] = content;
    },
    writeTableColumn(sourceData, cellKey, header = '') {
      let [contents, newKey] = [sourceData, cellKey];
      if (newKey.includes('.')) {
        const keySplitted = newKey.split('.');
        contents = sourceData[`${keySplitted[0]}`];
        newKey = keySplitted[1];
      }

      if (contents) {
        this.writeCell(header);

        if (!Array.isArray(contents)) { contents = [contents] }

        contents.forEach((content) => {
          if (Array.isArray(content[`${newKey}`])) {
            content[`${newKey}`].forEach(item => this.writeCell(item))
          } else this.writeCell(content[`${newKey}`] || content);
        });
        this.addColumn();
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 

</style>
