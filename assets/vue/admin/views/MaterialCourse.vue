<template>
  <div class="material-course" v-if="process == 0">
    <BaseSelect
     class="base-select"
      :title="translationsVue.material_course_configureFields_type"
      :options="options"
      v-model="typeFile"
    >
    </BaseSelect>

    <BaseInputFile
      tag="file"
      v-bind="validationInputFile"
      :placeholder="translationsVue.material_course_placeholder_file"
      @files-selected="fileSelected($event)"
      id="input-file"
    />

    <div class="actions">
      <button class="btn btn-secondary" data-bs-dismiss="modal">
        {{ translationsVue.cancelar }}
      </button>
      <button class="btn btn-primary" @click="saveMaterial()" id="save-file">
        {{ translationsVue.Save }}
      </button>
    </div>
  </div>
  <div v-else class="spinner">
    <BaseSpinner />
    <p class="wait">{{ translationsVue.component_video_preparing_file }}</p>
  </div>
</template>
<script>
import { get } from "vuex-pathify";

import BaseSelect from "../components/base/BaseSelect";
import BaseInputFile from "../components/base/BaseInputFile";
import BaseSpinner from "../components/base/Spinner";

export default {
  name: "materialCourse",
  components: {
    BaseSelect,
    BaseInputFile,
    BaseSpinner,
  },

  props:{
    idAnnouncement:{
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      idCourse,
      translationsMessage,
      translationsVue,
      typeFile: 5,
      files: null,
      process: 0,
    };
  },

  computed: {
    ...get("materialCourse", ["isLoading", "getRouteCourse"]),

    routeCourse() {
      return this.getRouteCourse();
    },

    options() {
      let typeMaterial = [];
      for (let i = 1; i <= 6; i++) {
        typeMaterial.push({
          id: i,
          name: this.translationsVue[
            `material_course_configureFields_type_${i}`
          ],
        });
      }
      return typeMaterial;
    },

    validationInputFile() {
      if (this.typeFile === 1) {
        return {
          accept: "application/pdf",
          multiple: "multiple",
        };
      } else if (this.typeFile === 2) {
        return { accept: "video/*" };
      } else if (this.typeFile === 3) {
        return {
          accept: ".zip, .rar",
          multiple: "multiple",
        };
      } else if (this.typeFile === 4) {
        return {
          accept: "image/*",
          multiple: "multiple",
        };
      } else if (this.typeFile === 5) {
        return {
          accept:
            ".doc, .docx, .docx, .docm, .dot, .dotx, .odt, dotm, .cvs, .dbf, .xla, .xls, .xlsb, .xlsm, .xlsx, .pptx, .pptm, .ppt, .mpt, .mpx, .mpp, .vsdx, .vsdm, .vssx, .vssm, .vstx, .vstm, .accdb",
          multiple: "multiple",
        };
      } else if (this.typeFile === 6) {
        return {
          accept: ".txt",
          multiple: "multiple",
        };
      }
    },

    /*
    validationInputFile() {
      return {
        accept: this.acceptFile,
      };
    }, */
  },

  watch: {
    files() {
      const bottom = document.getElementById("save-file");
      bottom.removeAttribute("disabled");

      bottom.classList.remove("btn-secondary");
    },
  },

  mounted() {
    this.disabledButtomSave();

    console.log("Creando el componente");
  },

  methods: {
    fileSelected(event) {
      this.files = event;
    },

    disabledButtomSave() {
      const bottom = document.getElementById("save-file");
      bottom.setAttribute("disabled", "disabled");

      bottom.classList.add("btn-secondary");
    },

    activateButtonSave() {
      const bottom = document.getElementById("save-file");
      bottom.removeAttribute("disabled");

      bottom.classList.remove("btn-secondary");
    },

    /*   resetInputFile(){
      console.log("resetear");
     const inputFile = document.getElementById('input-file');
     inputFile.value = '';
     return inputFile;
    },

     changeTypeFile(event){
      console.log("ha sido cambiado el select", event);
      this.resetInputFile();
    }, */

    async saveMaterial() {
      let data = {};
      if (this.idAnnouncement !== 0) {
        data = {
          course: this.idCourse,
          files: this.files,
          typeMaterial: this.typeFile,
          announcement: this.idAnnouncement,
        };
      } else {
        data = {
          course: this.idCourse,
          files: this.files,
          typeMaterial: this.typeFile,
          announcement: '',
        };
      }

      this.disabledButtomSave();
      this.process = 1;
      if (this.typeFile >= 1 && this.typeFile <= 6 && this.files !== null) {
        await this.$store.dispatch("materialCourse/newMaterialCourse", data);
      }
      this.process = 0;

      window.location.href = this.routeCourse;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.material-course {
  .base-select{
    margin-bottom: 2rem;
  }
  .actions {
    display: flex;
    flex-direction: row;
    gap: 2rem;
    justify-content: flex-end;
    margin-top: 3rem;
  }
}

.spinner {
  .wait {
    text-align: center;
    font-size: 2rem;
  }
}
</style>
