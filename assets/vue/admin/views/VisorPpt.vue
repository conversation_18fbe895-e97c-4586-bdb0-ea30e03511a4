<template>
  <div class="VisorPpt">
    <div class="ppt">
      <iframe
        :src="iframeSrcPrev"
        id="pptIframePrev"
        class="iframePpt"
        :style="iframeStyle('prev')"
        @load="onIframeLoad"
      ></iframe>
      <iframe
        :src="iframeSrcPivot"
        id="pptIframePivot"
        class="iframePpt"
        :style="iframeStyle('pivot')"
        @load="onIframeLoad"
      ></iframe>
      <iframe
        :src="iframeSrcNext"
        id="pptIframeNext"
        class="iframePpt"
        :style="iframeStyle('next')"
        @load="onIframeLoad"
      ></iframe>
      <div id="spinner-container" v-if="loading">
        <Spinner class="spinner" />
      </div>
    </div>
    <div class="actions" v-if="infoChapter">
      <span>{{ currentSlide }} / {{ slideCount }}</span>
    </div>
    <div
      class="arrow-icon left"
      @click="lastSlide"
      :class="{ disabled: disableNavigation }"
      v-if="currentSlide > 1"
      title="Diapositiva anterior"
    >
      <i class="fas fa-chevron-left"></i>
    </div>
    <div
      class="arrow-icon right"
      @click="nextSlide"
      :class="{ disabled: disableNavigation }"
      v-if="currentSlide < slideCount"
      title="Siguiente diapositiva"
    >
      <i class="fas fa-chevron-right"></i>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Spinner from "../components/base/Spinner";

const KEYBOARD_CODES = { LEFT: "ArrowLeft", RIGHT: "ArrowRight" };

export default {
  name: "VisorPpt",
  components: {
    Spinner,
  },
  props: ["id", "nameppt", "is_downloadable", "pptid", "pptslides", "slideid"],
  data() {
    return {
      infoChapter: undefined,
      loading: true,
      disableNavigation: false,
      currentSlide: 1,
      slideCount: this.pptslides,
      iframeSrcPrev: "",
      iframeSrcPivot: "",
      iframeSrcNext: "",
      prevSlideExists: false,
      nextSlideExists: false,
      activeIframe: "pivot",
      keyboardPaginationEnable: true,
    };
  },
  async created() {
    this.infoChapter = await this.$store.dispatch("chapterModule/getState", this.id);
    if (this.infoChapter && this.infoChapter.pagePpt && !this.infoChapter.finished) {
      this.currentSlide = this.infoChapter.pagePpt;
    }
    await this.$store.dispatch("timeModule/initStart", this.userChapter());
    this.updateIframeSources();
    this.loading = false;
    this.loadKeyboardNavigation();
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.handleKeyDown);
    window.removeEventListener("keyup", this.handleKeyUp);
  },
  computed: {
    ...get("chapterModule", ["userChapter"]),
  },
  methods: {
    updateIframeSources() {
      this.prevSlideExists = this.currentSlide > 1;
      this.nextSlideExists = this.currentSlide < this.slideCount;
      this.iframeSrcPrev = this.prevSlideExists ? this.getSlideUrl(this.currentSlide - 1) : "";
      this.iframeSrcPivot = this.getSlideUrl(this.currentSlide);
      this.iframeSrcNext = this.nextSlideExists ? this.getSlideUrl(this.currentSlide + 1) : "";
      this.activeIframe = "pivot";
    },
    getSlideUrl(slide) {
      if (slide < 1 || slide > this.slideCount) {
        return "";
      }
      return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
        `${window.location.origin}/uploads/ppt/packages/${this.pptid}/slide_${slide}.pptx`
      )}`;
    },
    iframeStyle(type) {
      return {
        display: type === this.activeIframe ? "block" : "none",
        width: "100%",
        height: "100%",
        border: "none",
      };
    },
    onIframeLoad() {
      this.loading = false;
    },
    async nextSlide() {
      if (this.currentSlide < this.slideCount && !this.disableNavigation) {
        this.disableNavigation = true;
        this.loading = true;
        const nextSlideIndex = this.currentSlide + 2;
        if (this.activeIframe === "pivot" || this.activeIframe === "prev") {
          this.activeIframe = "next";
          this.iframeSrcPrev = this.getSlideUrl(this.currentSlide);
          if (nextSlideIndex <= this.slideCount) {
            this.iframeSrcPivot = this.getSlideUrl(nextSlideIndex);
          }
        } else if (this.activeIframe === "next") {
          this.activeIframe = "pivot";
          this.iframeSrcPrev = this.iframeSrcNext;
          if (nextSlideIndex <= this.slideCount) {
            this.iframeSrcNext = this.getSlideUrl(nextSlideIndex);
          }
        }
        this.currentSlide++;
        await this.updateChapterState();
        this.loading = false;
        setTimeout(() => {
          this.disableNavigation = false;
        }, 1250);
      }
    },
    async lastSlide() {
      if (this.currentSlide > 1 && !this.disableNavigation) {
        this.disableNavigation = true;
        this.loading = true;
        const prevSlideIndex = this.currentSlide - 2;
        if (this.activeIframe === "pivot" || this.activeIframe === "next") {
          this.activeIframe = "prev";
          this.iframeSrcNext = this.getSlideUrl(this.currentSlide);
          if (prevSlideIndex >= 1) {
            this.iframeSrcPivot = this.getSlideUrl(prevSlideIndex);
          }
        } else if (this.activeIframe === "prev") {
          this.activeIframe = "pivot";
          this.iframeSrcNext = this.iframeSrcPrev;
          if (prevSlideIndex >= 1) {
            this.iframeSrcPrev = this.getSlideUrl(prevSlideIndex);
          }
        }
        this.currentSlide--;
        await this.updateChapterState();
        this.loading = false;
        setTimeout(() => {
          this.disableNavigation = false;
        }, 1250);
      }
    },
    async updateChapterState() {
      const chapterStatus = {
        chapter: this.id,
        pagePpt: this.currentSlide,
        finished: this.currentSlide >= this.slideCount,
      };
      await this.$store.dispatch("chapterModule/updateState", chapterStatus);
    },
    loadKeyboardNavigation() {
      window.addEventListener("keydown", this.handleKeyDown);
      window.addEventListener("keyup", this.handleKeyUp);
    },
    handleKeyDown(event) {
      const { key } = event;
      if (this.keyboardPaginationEnable && Object.values(KEYBOARD_CODES).includes(key)) {
        this.changeBlockWithKeyboard(key);
        this.keyboardPaginationEnable = false;
      }
    },
    handleKeyUp() {
      this.keyboardPaginationEnable = true;
    },
    changeBlockWithKeyboard(key) {
      if (key === KEYBOARD_CODES.LEFT) {
        this.lastSlide();
      }
      if (key === KEYBOARD_CODES.RIGHT) {
        this.nextSlide();
      }
    },
    async downloadFile() {
      await this.$store.dispatch("chapterModule/downloadFile", this.nameppt);
    },
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins}m ${secs}s`;
    },
  },
};
</script>

<style scoped lang="scss">
#spinner-container {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}
.arrow-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 3rem;
  height: 3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: #016b98;
  color: white;
  cursor: pointer;
  transition: opacity 0.3s;
  z-index: 10;
  &.disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }
  &.left {
    left: 1rem;
  }
  &.right {
    right: 1rem;
  }
}
.VisorPpt {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: black;
  position: relative;
  .ppt {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
  }
  .actions {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    font-size: 1rem;
    z-index: 5;
  }
  .iframePpt {
    width: 100%;
    height: 100%;
    border: none;
    transition: opacity 0.3s ease-in-out;
  }
  .spinner {
    margin: auto;
  }
}
</style>
