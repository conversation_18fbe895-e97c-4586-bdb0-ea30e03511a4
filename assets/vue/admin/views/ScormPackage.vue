<template>
  <div class="ScormPackage">
    <div class="card mb-3" style="width: 18rem; margin: auto">
      <div class="card-body text-center">
        <img src="assets/chapters/scorm.svg" />
      </div>
    </div>

    <div v-if="!proccessSave">
      <BaseInputFile
        tag="file"
        accept=".zip"
        :placeholder="placeholderinput"
        @files-selected="fileSelected($event)"
      />

      <button class="btn btn-primary btn-block" @click="savePackageScorm()">
        {{ textsave }}
      </button>
    </div>
    <div v-else>
      <h3 class="text-center">
        {{ translationsVue.component_video_preparing_file }}
      </h3>
      <BaseSpinner />
    </div>
  </div>
</template>

<script>
import BaseInputFile from "../components/base/BaseInputFile";
import BaseSpinner from "../components/base/Spinner";

export default {
  name: "scorm-package",
  components: {
    BaseInputFile,
    BaseSpinner,
  },
  props: {
    chapter: {
      type: Number,
      default: undefined,
    },

    scormuploadsubdomain: {
      type: String,
      default: undefined,
    },

    textsave: {
      type: String,
      default: undefined,
    },

    placeholderinput: {
      type: String,
      default: undefined,
    },

    infoscorm: {
      type: String,
      default: undefined,
    },
  },

  data() {
    return {
      file: undefined,
      proccessSave: false,
      translationsVue,
    };
  },

  methods: {
    async fileSelected(event) {
      this.file = event[0];
    },

    async savePackageScorm() {
      const data = {
        file: this.file,
        chapter: this.chapter,
        domain: this.scormuploadsubdomain,
      };
      if (this.file != undefined) {
        this.proccessSave = true;
        await this.$store.dispatch("scormPackageModule/newPackageScorm", data);
        this.process = false;

        window.location.href = `/admin/chapter/${this.chapter}/redirect-to-chapter`;
      } else {
        this.$toast.open({
          message: this.infoscorm,
          type: "info",
          duration: 5000,
          position: "top-right",
        });
      }
    },
  },
};
</script>

 <style scoped lang="scss"> 
.ScormPackage {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
</style>
