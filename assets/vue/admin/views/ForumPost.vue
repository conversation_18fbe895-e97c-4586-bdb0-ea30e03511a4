<template>
  <div
      v-if="comments"
      className="Forum"
  >
    <div className="discordblock">
      <ThreadComments
          :key="key"
          :currentcomments="newComments"
          @saveComments="sendNewComments"
      />
    </div>
  </div>
</template>

<script>

import ThreadComments from '../components/forum/ThreadComments';

export default {
  components: {
    ThreadComments,
  },

  data() {
    return {
      currentSection: 0,
      courses: undefined,
      comments: undefined,
      threads: undefined,
      newComments: undefined,
      treadId: undefined,
      key: 1,
    };
  },

  props: ["threadid","courseid"],

  async created() {
    this.comments = await this.$store.dispatch('forumModule/fetchThread', this.threadid);
    console.log(this.comments);
    this.mountComments(this.comments);
  },

  methods: {
    clean() {
      this.newComments = [];
    },

    changeCurrentThread(value) {
      this.$store.dispatch('forumModule/fetchThreadsComments', value)
          .then((data) => {
            this.clean();
            this.mountComments(data);
            const {courseId} = this.$route.params;
            this.treadId = value;

            this.$router.push({name: 'Thread', params: {threadId: value, courseId}});
          })
          .catch((error) => {
            if (error.response.status === 404) {
              this.$router.push({name: 'help'});
            }
          })
          .finally(() => {
            this.$emit('close');
          });
    },

    mountComments(comentarios) {
      this.clean();
      this.newComments = [];
      let allComents = [];
      let firstComment = {
        user: comentarios.data.user,
        createdAt: comentarios.data.createdAt,
        message: comentarios.data.message,
        id: comentarios.data.id,
        forumLikes: comentarios.data.forumLikes,
      };

      allComents.push(firstComment);
      comentarios.data.children.forEach((element) => allComents.push(element));
      this.newComments = allComents;
      firstComment = {};
      allComents = [];
    },

    scrollToEnd() {
      const container = this.$el.querySelector('#container');
      container.scrollTop = container.scrollHeight;
    },

    sendNewComments(value) {
      let responseId = null;
      let mensajeText = value;

      if(value.hasOwnProperty('response_id') ){
        responseId = value.response_id;
      }

      if(typeof value.text !== 'undefined'){
        mensajeText = value.text;
      }

      const addText = {
        course_id: parseInt(this.courseid, 10),
        title: null,
        message: mensajeText,
        forum_post_id: parseInt(this.threadid, 10),
        response_id: responseId,
      };

      this.$store.dispatch('forumModule/sendComment', addText)
          .then((response) => {
            const comment = {
              user: {
                firstName: response.data.payload.user.firstName,
                lastName: response.data.payload.user.lastName,
                image: response.data.payload.user.image,
              },
              createdAt: response.data.payload.createdAt,
              message: response.data.payload.message,
              id: response.data.payload.id,
              created: response.data.payload.created,
            };
            this.newComments.push(comment);
          })
          .catch((error) => {
            if (error.status === 404) {
              this.$router.push({name: 'help'});
            }
          })
          .finally(() => {
            this.$emit('close');
          });
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Forum {
 // background-image: url("~@/assets/images/bk_home.jpg");
  background-size: cover;
  background-position: top left;
  margin-top: 75px; // todo remove
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: baseline;

  padding-top: 2%;
  padding-left: 7%;
  padding-right: 7%;

  .lasttems {
    flex: 0 0 300px;
    color: hsl(198, 98%, 25%);
    align-self: stretch;
    display: none;
    //@media #{$breakpoint-md-min} {
    //  display: block;
    //}
  }

  .profile-menu {
    color:  hsl(0, 0%, 100%);
  }

  a {
    color:  hsl(0, 0%, 100%);
  }

  .discordblock {
    flex: 4;
    align-self: stretch;
    width: 100%;
    //@media #{$breakpoint-md-min} {
    //  display: block;
    //  width: 75%;
    //}
  }

  .profile-menu {
    color: black;
  }
}
</style>
