import Vue from 'vue';
import jQuery from 'jquery';
import axios from "axios";

new Vue({
    delimiters: ['${', '}'],

    data() {
        return {
            destinationCategory: null,
            challengeId: challengeId,
            questions: undefined,
        }
    },

    async created() {

    },

    methods: {
        countAnswers(challengeAnswers) {
            return challengeAnswers.length;
        },

        importQuestions() {
            let formData = new FormData();
            formData.append('challenge_id', this.challengeId);

            let quizFile = document.querySelector('#excel-file');
            console.log(quizFile.files[0]);
            formData.append('challenge_questions', quizFile.files[0]);

            axios
                .post(importQuestionsUrl, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                })
                .then((response) => {
                    if (response.data.error) {
                        console.log('return', response.data.data)

                    }else{
                        location.reload();
                    }
                });

            //jQuery('#importFromExcelModal').modal('hide');
        },

        downloadTemplate(){
            axios
                .post(downloadTemplateUrl, {

                },
                {
                responseType: 'blob'
                }).then((response) => {
                    const url = URL.createObjectURL(new Blob([response.data], {
                        type: 'application/vnd.ms-excel'
                    }))
                    const link = document.createElement('a')
                    link.href = url
                    link.setAttribute('download', 'fileName')
                    document.body.appendChild(link)
                    link.click()
                });
        }

    },

    computed: {},


}).$mount('#challenge-detail')
