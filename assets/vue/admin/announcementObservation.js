import Vue from 'vue';
import $ from 'jquery';
import Vuex from "vuex";
import axios from "axios";
import 'bootstrap';
import VueToast from "vue-toast-notification";
import 'vue-toast-notification/dist/theme-sugar.css';
import VueAlertify from "vue-alertify";

Vue.use(Vuex);
Vue.use(VueToast);
Vue.use(VueAlertify, {
    closable: false,
    movable: false,
});

const store = new Vuex.Store({
    state: {
        loadingFiles: true,
        allowedFileTypes: {},
    },
    getters: {
        isLoadingFiles(state) { return state.loadingFiles; },
        allowedFileTypes(state) { return state.allowedFileTypes; }
    },
    mutations: {
        SET_LOADING_FILES(state, isLoading) {
            state.loadingFiles = isLoading;
        },
        SET_ALLOWED_FILE_TYPES(state, types) {
            state.allowedFileTypes = types;
        }
    },
    actions: {
        async loadObservationFiles({ commit }, id) {
            commit('SET_LOADING_FILES', true);
            try {
                const url = `/admin/announcement-observation/${id}/files`;
                const result = await axios.get(url);
                return result.data;
            } finally {
                commit('SET_LOADING_FILES', false);

            }
        },

        async loadAllowedFileTypes({ commit }) {
            const result = await axios.get('/admin/announcement-observation/allowed-file-types');
            const { data } = result.data;
            commit('SET_ALLOWED_FILE_TYPES', data);
        },

        async uploadObservationFiles({commit}, { id, formData }) {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const url = `/admin/announcement-observation/${id}/files`;
            const result = await axios.post(url, formData, { headers });
            return result.data;
        },

        async deleteAnnouncementObservationDocument({ commit }, id) {
            const url = `/admin/announcement-observation-document/${id}`;
            const result = await axios.delete(url);
            return result.data;
        }
    }
});

import visorImagen from "./components/task-course/visors/visorImagen.vue";
import visorPdf from "./components/task-course/visors/visorPdf.vue";
import visorPpt from "./components/task-course/visors/visorPpt.vue";
import visorVideo from "./components/task-course/visors/visorVideo.vue";
import visorOffice from "./components/task-course/visors/visorOffice.vue";
import visorTxt from "./components/task-course/visors/visorTxt.vue";
import loader from "./components/Loader.vue";
import spinner from "./components/base/Spinner.vue";

new Vue({
    delimiters: ['${', '}'],
    components: {
        visorImagen, visorPdf, visorPpt, visorVideo, visorOffice, visorTxt, loader, spinner
    },
    $,
    store,
    data() {
        return {
            files: [],
            selectedFileType: '',
            uploadingFiles: false,
        };
    },
    computed: {
        allowedFileTypes() {
            return this.$store.getters['allowedFileTypes'];
        },
        isLoadingFiles() {
            return this.$store.getters['isLoadingFiles'];
        }
    },
    created() {
        this.loadFiles();
        this.loadAllowedFileTypes();
    },
    methods: {
        showSelectionFileModal() {
            $('#observation-files').modal({
                'show'  : true,
                static  : true,
                backdrop: false,
                keyboard: false
            })
        },
        loadAllowedFileTypes() {
            this.$store.dispatch('loadAllowedFileTypes')
        },
        loadFiles() {
            this.$store.dispatch('loadObservationFiles', observationId).then(res => {
                const { data, error } = res;
                this.files = data;
            })
        },
        submit() {
            const files = document.getElementById('file').files;
            if (files.length === 0) {
                this.$toast.error('No file has been selected');
                return;
            }

            const form = document.forms['form-upload-observation-file'];
            const formData = new FormData(form);
            if (files.length === 0) {
                this.$toast.error('Is required to select at least one file');
                return;
            } else if (files.length > 1) {
                formData.delete('file');
                for (let i = 0; i < files.length; i++) {
                    formData.append(`file_${i}`, files[i]);
                }
                formData.append('filesLength', files.length);
            }
            formData.append('file-type', this.selectedFileType.type);

            this.$alertify.confirmWithTitle(
                confirmFileUploadText,
                '...',
                () => {
                    this.uploadingFiles = true;
                    this.$store.dispatch('uploadObservationFiles', { id: observationId, formData }).then(res => {
                        const { data, error } = res;
                        if (error) this.$toast.error(data);
                        else {
                            $('#observation-files').modal('hide');
                            this.$toast.success(data);
                            this.loadFiles();
                        }
                    }).finally(() => {
                        this.uploadingFiles = false;
                    });
                },
                () => {},
            )
        },

        fileVisorComponent(file) {
            switch (file.type) {
                case 'pdf': return 'visorPdf';
                case 'ppt': return 'visorPpt';
                case 'video': return 'visorVideo';
                case 'compressed': return 'visorVideo';
                case 'image': return 'visorImagen';
                case 'office': return 'visorOffice';
                case 'txt': return 'visorTxt';
            }
            return null;
        },

        deleteAnnouncementObservationDocument(file) {
            this.$alertify.confirmWithTitle(
                confirmFileDelete,
                '...',
                () => {
                    this.$store.dispatch('deleteAnnouncementObservationDocument', file.id).then(res => {
                        const { data, error } = res;
                        if (error) this.$toast.error(data);
                        else {
                            this.$toast.success(data);
                            this.loadFiles();
                        }
                    })
                },
                () => {},
            );
        }
    }
}).$mount('#app');
