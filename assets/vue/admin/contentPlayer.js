// Require Froala Editor js file.
require('froala-editor/js/froala_editor.pkgd.min.js');

// Require Froala Editor css files.
require('froala-editor/css/froala_editor.pkgd.min.css');
require('froala-editor/css/froala_style.min.css');

import Vue from 'vue';
import ContentPlayer from './views/ContentPlayer';
import store from './store';

// Import and use Vue Froala lib.
import VueFroala from 'vue-froala-wysiwyg';

import checkView from 'vue-check-view';
import VueScrollTo from 'vue-scrollto';
// import VueObserveVisibility from 'vue-observe-visibility'
// import inViewportDirective from 'vue-in-viewport-directive'
Vue.use(checkView);
Vue.use(VueScrollTo);

Vue.use(VueFroala);
// Vue.use(VueObserveVisibility)
// Vue.directive('in-viewport', inViewportDirective)

new Vue({
  components: { ContentPlayer },
  // template: '<Call v-bind:id="this.announcement"></Call>',
  store,
}).$mount('#content-player');
