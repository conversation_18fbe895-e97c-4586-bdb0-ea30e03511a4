import Vue from 'vue';
import CallUserFilter from './views/CallUserFilter';
import store from './store';
import Multiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.min.css';
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';

Vue.use(VueToast);

Vue.component('multiselect', Multiselect)

new Vue({
    components: { CallUserFilter, Multiselect },
    store,
}).$mount('#call_user_filter')
