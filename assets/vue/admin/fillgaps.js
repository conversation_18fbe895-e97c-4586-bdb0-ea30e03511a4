// Require Froala Editor js file.
require('froala-editor/js/froala_editor.pkgd.min.js')

// Require Froala Editor css files.
require('froala-editor/css/froala_editor.pkgd.min.css')
require('froala-editor/css/froala_style.min.css')

import Vue from 'vue';
import fillgapsgame from './views/Fillgaps';
import store from './store';

import '../../vue/registerBaseComponents';
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';


Vue.use(VueToast);


// Import and use Vue Froala lib.
import VueFroala from 'vue-froala-wysiwyg'

import checkView  from 'vue-check-view'
import VueScrollTo from 'vue-scrollto'

Vue.use(checkView);
Vue.use(VueScrollTo);

Vue.use(VueFroala)

new Vue({
    components: { fillgapsgame },
    store,
}).$mount('#fillgaps')
