import {make} from "vuex-pathify";
import axios from "axios";

const state = {
    loading: true,
    help_categories: [],
    category: {
        category: null,
        translations: []
    },
    form: {
        id: -1,
        name: '',
        translations: [],
        help_categories: []
    }
};

export const mutations = {
    ...make.mutations(state),
};
export const getters = {
    ...make.getters(state),
};
export const actions = {
    getCategories({ commit }) {
        commit('SET_LOADING', true);
        axios.get("/admin/help-categories/all").then(r => {
            const { data } = r.data;
            commit('SET_HELP_CATEGORIES', data.data);
        }).catch(e => {
            console.log(e);
        }).finally(() => {
            commit('SET_LOADING', false);
        });
    },

    async initForm({ commit, dispatch }, { id = -1, locales = {}}) {
        commit('SET_LOADING', true);
        let translations = [];

        const keys = Object.keys(locales);
        if (id > 0) {
            const result = await axios.get(`/admin/help-categories/${id}`);
            const { data, error } = result.data;

            if (data.translations && data.translations.length > 0) {
                translations = data.translations;
            }
            keys.forEach(k => {
                if (!translations.find(t => t.locale === k)) {
                    // not found, add
                    translations.push({ locale: k, name: ''});
                }
            });
        } else {
            keys.forEach(k => {
                translations.push({ locale: k, name: ''});
            });

        }

        commit('SET_FORM', {
            id,
            name: '',
            translations,
            help_categories: [],
        })
        commit('SET_LOADING', false);
    },

    /**
     *
     * @param getters
     * @param rootGetters
     * @return {*|Promise<axios.AxiosResponse<any> | {data: string, e: *, error: boolean}>}
     */
    save({ getters, rootGetters }) {
        const { form } = getters;
        const locales = rootGetters['localeModule/locales'];
        const userLocale = rootGetters['localeModule/userLocale'];

        // Find default translation
        let errorDefault = true;
        form.translations.forEach(t => {
            if (t.locale === userLocale) {//debo validar el locale actual del administrador
                errorDefault = !t.name || t.name.length < 1;
                form.name = t.name;
            }
        })
        if (errorDefault) return Promise.reject("Name in locale [" + locales[userLocale] + "] is required");

        const data = {...form};
        const headers = {'Content-Type': 'application/json'};

        if(form.id < 1) {//**** está fallando la creación */
            return axios.post(`/admin/help-categories/create`, data, { headers }).then(r => (r.data))
                .catch(e => ({error: true, data: 'Failed to make request', e}));
        } else {
            return axios.put(`/admin/help-categories/update`, data, { headers }).then(r => (r.data))
                .catch(e => ({error: true, data: 'Failed to make request', e}));
        }
    },

    async deleteCategory({}, id) {
        try {
            const result = await axios.delete(`/admin/help-categories/${id}`);
            const { error, data } = result.data;
            if (error) return Promise.reject(data);
            return Promise.resolve();
        } catch (e) {
            if(e.response && e.response.data && e.response.data.status === 422) {
                return Promise.reject({
                    status: 422,
                    message: e.response.data.data
                });
            } else {
                return Promise.reject("Failed to make request");
            }
        }
    },

    updateCategoriesOrder({ getters }) {
        ///update-order
        const { help_categories } = getters;
        let order = {};
        help_categories.forEach((cat, index) => {
            order[cat.id] = index + 1;
        });

        return axios.post('/admin/help-categories/update-order', order).then(r => (r.data))
            .catch(e => ({ error: true, data: 'Fail to make request'}));
    },

};

export default {
    namespaced: true,
    state,
    mutations,
    getters,
    actions
};
