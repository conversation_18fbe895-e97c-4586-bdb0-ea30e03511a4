<script>
import StepForm from "../../common/views/StepForm.vue";
import {get, sync} from "vuex-pathify";
import Spinner from "../../base/BaseSpinner.vue";
import FormInformation from "../components/Form/Information.vue";

export default {
  name: "FormView",
  components: {Spinner, StepForm, FormInformation},
  data() {
    return {
      steps: {
        current: 1,
        total: 3
      }
    };
  },
  computed: {
    locales: get('localeModule/locales'),
    loading: sync('helpCategoryModule/loading'),
    form: sync('helpCategoryModule/form'),
    routeName() {
      return this.$route.name;
    },
    id() {
      return this.$route.params.id ?? -1;
    }
  },
  created() {
    this.$store.dispatch('helpCategoryModule/initForm', {id: this.id, locales: this.locales});
  },
  mounted() {
    this.$eventBus.$on("onSave", (e) => {
      this.save();
    });
    this.$eventBus.$on("onDelete", (e) => {
      this.remove();
    });
  },
  beforeDestroy() {
    this.$eventBus.$off("onSave");
    this.$eventBus.$off("onDelete");
  },
  methods: {
    save() {
      this.$store.dispatch('helpCategoryModule/save').then(r => {
        this.$toast.success(this.$t('CATALOG.SAVED') + '');
        this.$router.push({ name: 'Home'});
      }).catch(e => {
        this.$toast.error(e);
      });
    },
    remove() {
      this.$alertify.confirmWithTitle(
          this.$t('DELETE'),
          this.$t('COMMON_AREAS.QUESTION_DELETE'),
          () => {
            this.$store.dispatch('helpCategoryModule/deleteCategory', this.id).then(r => {
              this.$toast.success(this.$t('DELETE_SUCCESS') + '');
              this.$router.push({ name: 'Home'});
            }).catch(e => {
              if(e && e.status === 422 && e.message) {
                this.$toast.error(e.message);
              } else {
                this.$toast.error(e);
              }
            })
          },
          () => {},
      )
    }
  }
}
</script>

<template>
  <div class="d-flex w-100 align-items-center justify-content-center" v-if="loading">
    <spinner/>
  </div>
  <div class="FormView" v-else>
    <div class="w-100 d-flex flex-row Header">
      <h4>{{ $t('COURSE_CATEGORY.LABEL.DETAIL') }}</h4>
    </div>
    <div class="bg-white p-0 pt-1 pb-1">
      <FormInformation></FormInformation>
    </div>
  </div>
</template>

<style scoped lang="scss">
.FormView {
  .Header {
    padding: 1rem;
    & > h4 {
      font-size: 22px;
      color: var(--color-neutral-darkest);
    }
  }

  .tab-pane {
    padding: 1rem;
  }
}
</style>
