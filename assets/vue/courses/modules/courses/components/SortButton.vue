<template>
  <i
    class="SortButton fa cursor-pointer"
    :class="[icon, disabledClass]"
    @click="toggleSort"
  />
</template>

<script>
export default {
  name: "SortButton",
  props: {
    sortBy: {
      type: String,
      default: '',
    },
    current: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      currentIndex: 0,
    }
  },
  computed: {
    direction() {
      return ['', 'ASC', 'DESC'][this.currentIndex] || ''
    },
    icon() {
      return `${(['fa-sort', 'fa-caret-up', 'fa-caret-down'][this.currentIndex] || '')}`
    },
    disabledClass() {
      return this.disabled ? 'disabled' : ''
    },
  },
  watch: {
    current() {
      if (this.current !== this.sortBy) this.currentIndex = 0;
    }
  },
  methods: {
    toggleSort() {
      if (this.disabled) return null
      this.currentIndex = (this.currentIndex + 1) % 3;
      this.$emit("toggle", { sortBy: this.direction ? this.sortBy : '', direction: this.direction });
    }
  }
}
</script>

<style scoped>
.SortButton {
  &.disabled {
    color: var(--color-neutral-mid-dark);
  }
}
</style>