export class BaseOptionModel {
  constructor({ id, name }) {
    this.id = id ?? null;
    this.name = name ?? null;
  }
}

export class TypeCourse extends BaseOptionModel {}

export class CourseCategory extends BaseOptionModel {}

export class CreatorCourse extends BaseOptionModel {}

export class Course {
  constructor({
    id,
    code,
    name,
    typeCourse,
    typeCourseId,
    category,
    open,
    active,
    totalChapters,
    thumbnailUrl,
    locale,
    languages,
    completed,
    icon,
  }) {
    this.id = id ?? null;
    this.code = code || '';
    this.name = name ?? null;
    this.typeCourse = typeCourse ?? null;
    this.typeCourseId = typeCourseId ?? null;
    this.category = category ?? null;
    this.open = open ?? null;
    this.active = active ?? null;
    this.totalChapters = totalChapters ?? null;
    this.thumbnailUrl = thumbnailUrl ?? null;
    this.locale = locale ?? null;
    this.languages = languages ?? null;
    this.completed = completed ?? null;
    this.icon = icon ?? null;
  }
}

export class Courses  {
  constructor({
    courses,
    totalCourses,
    typeCourses,
    courseCategories,
    creatorsCourses,
  }) {
    this.courses = courses
      ? Array.isArray(courses)
        ? courses.map((course) => new Course(course))
        : []
      : null;
    this.totalCourses = totalCourses ?? null;
    this.typeCourses = typeCourses
      ? Array.isArray(typeCourses)
        ? typeCourses.map((type) => new TypeCourse(type))
        : []
      : null;
    this.courseCategories = courseCategories
      ? Array.isArray(courseCategories)
        ? courseCategories.map((cat) => new CourseCategory(cat))
        : []
      : null;
    this.creatorsCourses = creatorsCourses
      ? Array.isArray(creatorsCourses)
        ? creatorsCourses.map((creator) => new CreatorCourse(creator))
        : []
      : null;
  }
}
