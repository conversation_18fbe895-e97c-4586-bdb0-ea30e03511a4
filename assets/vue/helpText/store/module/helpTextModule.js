import {make} from "vuex-pathify";
import axios from "axios";

const state = {
    loading: true,
    items: [],
    pagination: {
        page: 1,
        totalItems: 0
    },
    categories: [],
    translations: [],
    warnings: [],
};

const getters = {
    ...make.getters(state)
};

const mutations = {
    ...make.mutations(state)
};

export const actions = {
    ...make.actions(state),
    async getCategories({ commit }) {
        const result = await axios.get('/admin/help-text/categories');
        const { data } = result.data;
        commit('SET_CATEGORIES', data);
    },

    async getTranslations({ commit }) {
        const result = await axios.get('/admin/help-text/translations');
        const { data } = result.data;
        commit('SET_TRANSLATIONS', data.translations);
        commit('SET_WARNINGS', data.warningLocales);

    },

    async saveNewHelpText({ commit }, formData) {
        const headers = {
            'Content-Type': 'multipart/form-data'
        };
        const result = await axios.post('/admin/help-text', formData, {
            headers
        });
        return result.data;
    },

    async updateHelpText({ commit }, {id, formData}) {
        const headers = {
            'Content-Type': 'multipart/form-data'
        };
        const result = await axios.post(`/admin/help-text/${id}/update`, formData, {
            headers
        });
        return result.data;
    },

    async getHelpText({ commit }, id) {
        const result = await axios.get(`/admin/help-text/${id}`);
        return result.data;
    },

    async viewHelpText({ commit }, id) {
        const result = await axios.get(`/admin/help-text/${id}/view`);
        return result.data;
    },

    async getItems({ commit, getters }) {
        commit('SET_LOADING', true);
        try {
            const pagination = getters['pagination'];
            const result = await axios.get(`/admin/help-text/items/${pagination.page}`);
            const { data } = result.data;
            commit('SET_ITEMS', data.items);
            commit('SET_PAGINATION', {
                page: pagination.page,
                totalItems: data['total-items']
            });
        } finally {
            commit('SET_LOADING', false);
        }
    },

    async deleteHelpText({ commit, dispatch }, id) {
        const result = await axios.delete(`/admin/help-text/${id}`);
        const { error } = result.data;
        if (!error) {
            dispatch('getItems');
        }
        return result.data;
    }
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
