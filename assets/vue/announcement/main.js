import  initVueApp, { getI18n } from "../common/mainApp";
import './announcement.scss';

import store from './store';
import router from "./router";
import {get} from "vuex-pathify";

const i18n = getI18n(store);

const excludedRouteNamesFromCounter = ['Home', 'CreateAnnouncement', 'UpdateAnnouncement'];

const app = initVueApp(
    {
        store,
        router,
        i18n,
        paramMounted: () => {
            document.title = i18n.t('ANNOUNCEMENTS') + '';
            // console.log(i18n.t('ALERTIFY.OK'))
        },
        paramComputed: {
            currentRoute() {
                return this.$route.name;
            },
            globalCounterPayload: get('timeCounterModule/globalPayload'),
        },
        paramWatch: {
            currentRoute: {
                immediate: true,
                handler: function (val, oldVal) {
                    if (this.$isTutor()) {
                        if (excludedRouteNamesFromCounter.includes(this.$route.name)) {
                            this.$store.dispatch('timeCounterModule/reset');
                            return;
                        }
                        if (this.$route.name === 'ViewAnnouncement') {
                            const id = this.$route.params.id;
                            this.$store.dispatch(`timeCounterModule/setRoutes`, {
                                global: `/admin/announcement/tutor-time/${id}`,
                                local: `/admin/announcement/tutor-time/${id}`
                            });

                            /** A Global counter is required **/
                            this.$store.dispatch('timeCounterModule/setGlobalPayload', {
                                name: 'ViewAnnouncement',
                                params: this.$route.params,
                                userId: this.$getUser().id,
                                send: false
                            });

                            this.$store.dispatch('timeCounterModule/setLocalPayload', {
                                name: this.$route.name,
                                params: {...this.$route.params},
                                userId: this.$getUser().id,
                            });
                        } else {
                            /**
                             * Local route counter
                             */
                            this.$store.dispatch('timeCounterModule/setLocalPayload', {
                                name: this.$route.name,
                                params: this.$route.params,
                                userId: this.$getUser().id
                            });
                        }
                    }
                }
            }
        },

        paramBeforeDestroy: () => {
            if (this.$isTutor()) {
                this.$store.dispatch('timeCounterModule/reset');
            }
        }
    }
);
app.$mount('#app');
