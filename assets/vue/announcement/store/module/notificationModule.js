import axios from "axios";
export default {
    namespaced: true,
    actions: {
        loadNotifications({}, announcementID = 0) {
            return axios.get(`/admin/announcement/notifications/${announcementID}`)
              .then((result) => result.data);
        },
        saveNotifications({}, payload = {}) {
            return axios.post('/admin/announcement/notification/save', payload)
              .then((result) => result.data);
        },
        deleteNotification({}, id) {
            return axios.post('/admin/announcement/notification/delete', { id })
                .then(r => r.data);
        }
    }
}
