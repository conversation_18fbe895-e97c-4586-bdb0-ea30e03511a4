"use strict";
import { make } from "vuex-pathify";
import { mapState } from 'vuex';

import {
  ANNOUNCEMENT_DEFAULT_VALUES,
  ANNOUNCEMENT_SOURCE,
  ANNOUNCEMENT_VALIDATIONS,
  initAnnouncement,
  loadAnnouncement,
  loadPreData,
  loadTutors,
  configureSteps,
  initAnnouncementApprovedCriteria,
  getCourseChapters,
  findCourses,
} from "./common";

import {
  saveAnnouncementAlerts,
  saveAnnouncementBonus,
  saveAnnouncementCertificate,
  saveAnnouncementCommunication,
  saveAnnouncementGeneralInfo,
  saveAnnouncementGeneralInfoExtern,
  saveAnnouncementGroups,
  saveAnnouncementStudents,
  saveAnnouncementSurvey,
  saveGroupTutorInfo,
  saveNewGroupTutor,
} from "./componentSaveMethods";

import { STATUS_ACTIVE, STATUS_CONFIGURATION } from "../../../mixins/constants";
import axios from "axios";

const state = {
  /** MAIN INFO **/
  announcement: ANNOUNCEMENT_DEFAULT_VALUES,
  codes: {
    code: undefined,
    actionCode: undefined,
  },
  update: false,
  /** Validations by default empty, fill based on announcement source **/
  validation: ANNOUNCEMENT_VALIDATIONS,

  /** LOADERS **/
  loading: true,
  saving: false,
  loadingChapters: false,

  enableCourseFinder: true,
  confirmation: {
    name: undefined,
    confirmed: false,
  },

  /** FORM STEPPER CONTROLLER **/
  steps: {
    current: 1,
    total: 1,
  },
  stepsInformation: {
    titles: new Map(),
    steps: [],
  },
  stepsConfigurations: {}, // Configuration send by the back

  errors: {},

  chapters: [],
  types: [],
  approvedCriteria: [],
  clientConfigurationAnnouncement: [],
  tutors: [],
  alertsTypeTutor: [],
  typeDiplomas: [],
  typeSurveys: [],
  virtualClassroomTypes: [],
  timezones: [],
  courseCategories: [],

  fundaeConfiguration: {
    actionTypes: [],
    maxStudentsPerGroup: 0,
    minPassingScore: 0,
    default_entry_margin: 0,
    default_exit_margin: 0,
  },
  typeMoneys: [],
  typeSessions: [],
  enrollmentTemplateXls: null,

  typeIdentifications: [],
  mainIdentification: null,
  modalities: [],
  companies: []
};

const mutations = {
  ...make.mutations(state),
  /** Steps mutations **/
  increaseStep(state) {
    if (state.steps.current === state.steps.total) return;
    state.steps.current += 1;
  },
  decreaseStep(state) {
    if (state.steps.current === 1) return;
    state.steps.current -= 1;
  },

  setChapterTiming(state, timing) {
    state.announcement.chapterTiming = timing;
  },

  SET_ANNOUNCEMENT_TYPE(state, type) {
    state.announcement.type = type;
  },

  SET_ANNOUNCEMENT_DATA(state, data) {
    const keys = Object.keys(state.announcement);
    state.extra = data.extra;

    keys.forEach((key) => {
      state.announcement[`${key}`] = data[`${key}`];
    });
  },

  SET_ID(state, id) {
    state.announcement.id = id;
  },

  SET_APPLY_EXTERNAL_RESULT(state, data) {
    state.announcement.students[0].id = data.firstGroupId;
    state.announcement.usersPerGroup = data.usersPerGroup;
  },

  SET_INSPECTOR_ACCESS(state, access) {
    state.announcement.inspectorAccess = access;
  },

  SET_DIDACTIC_GUIDE(state, guide) {
    state.announcement.didacticGuide = guide;
  },

  INIT_ANNOUNCEMENT_APPROVED_CRITERIA(state, approvedCriteria) {
    const data = {};
    approvedCriteria.forEach((criteria) => {
      if (criteria.extra.alwaysEnabled) {
        data[criteria.id] = {
          value: 0,
          enabled: false,
          alwaysEnabled: true,
        };
      } else {
        data[criteria.id] = {
          value: 0,
          enabled: false,
        };
      }
    });
    state.announcement.approvedCriteriaValues = data;
  },
  SET_CONFIG_ANNOUNCEMENT(state, configAnnouncement) {
    state.announcement.configAnnouncement = configAnnouncement;
  },

  SET_ALERT_TYPE_TUTOR_VALUES(state, alertsTypeTutor) {
    state.announcement.alertTypeTutorValues = alertsTypeTutor;
  },

  SET_CURRENT_STEP(state, step) {
    state.steps.current = step;
  },

  RESET_ERROR(state) {
    state.errors = {};
  },

  RESET(state) {
    state.announcement = ANNOUNCEMENT_DEFAULT_VALUES;
    state.steps = {
      current: 1,
      total: 1,
    };
  },

  SET_UPDATE_STUDENTS(state, students) {
    state.announcement.students = students.map(g => {
      g.sessions = g.sessions.map(s => {
        const sTemp = s;
        sTemp.startAt = s.startAt.slice(0, 19);
        sTemp.finishAt = s.finishAt.slice(0, 19);
        return sTemp;
      })
      return g;
    });
  },

  setPreSelectedCourse(state, course) {
    state.announcement.type = course.type;
    state.announcement.course = course;
  },
  RUN_FUNDAE_CONFIGURATION(state, configuration) {
    const actionTypes = [];
    configuration.action_types.forEach((value) => {
      actionTypes.push({
        name: `ANNOUNCEMENT.FORM.ENTITY.FUNDAE_CONFIGURATION.${value.toUpperCase()}`,
        value,
      });
    });
    state.fundaeConfiguration.actionTypes = actionTypes;
    state.fundaeConfiguration.maxStudentsPerGroup =
      configuration.max_students_per_group;
    state.fundaeConfiguration.minPassingScore = configuration.min_passing_score;
    state.fundaeConfiguration.default_entry_margin =
      configuration.default_entry_margin;
    state.fundaeConfiguration.default_exit_margin =
      configuration.default_exit_margin;
  },
  SET_ANNOUNCEMENT_CODE(state, code) {
    state.announcement.code = code;
  },

  SET_ENROLLMENT_TEMPLATE_XLS(state, xls) {
    state.enrollmentTemplateXls = xls;
  },

  SET_EXTRA(state, extra) {
    state.announcement.extra = extra;
    state.extra = extra;
  },
};

export const getters = {
  ...make.getters(state),
  modalityList(state) { return state.modalities },
  isNotified(state) {
    return state.announcement.notifiedAt !== null;
  },
  isConfigurable(state) {
    return state.announcement.status === STATUS_CONFIGURATION;
  },
};

export const actions = {
  ...make.actions(state),
  /**
   * Loaders
   */
  loadPreData(
    { commit, dispatch },
    { id = null, source = ANNOUNCEMENT_SOURCE.INTERN }
  ) {
    commit("SET_LOADING", true);
    commit("RESET");
    loadPreData(async (r) => {
      const {
        typeCourses,
        approvedCriteria,
        clientConfigurationAnnouncement,
        alertTypeTutor,
        typeDiplomas,
        typeSurveys,
        fundaeConfiguration,
        classroomvirtualTypes,
        codes,
        timezones,
        typeMoneys,
        typeSessions,
        stepsConfigurations,
        enrollmentTemplate,
        typeIdentifications,
        mainIdentification,
        modalities,
        companies,
        extra: rawExtra
      } = r.data;

      const extra = rawExtra.map(item => ({
        id: item.id,
        name: item.name,
        value: item.value || null
      }));

      /** Default announcement initialization **/
      const announcement = initAnnouncement(source);
      announcement.approvedCriteriaValues =
        initAnnouncementApprovedCriteria(approvedCriteria);
      if (id == null || id <= 0) {
        announcement.code = codes.code;
        if (typeCourses.length > 0) {
          announcement.type = typeCourses[0].type;
          announcement.course.typeCourseId = typeCourses[0].id;
        }
      }
      commit("SET_ANNOUNCEMENT", announcement);

      /** Execute form initial configuration **/
      commit("SET_STEPS_CONFIGURATIONS", stepsConfigurations);
      dispatch("configureSteps", { stepsConfigurations, source });

      commit("SET_TYPES", typeCourses);
      commit(
        "SET_CLIENT_CONFIGURATION_ANNOUNCEMENT",
        clientConfigurationAnnouncement
      );
      commit("SET_ALERTS_TYPE_TUTOR", alertTypeTutor);
      commit("SET_TYPE_DIPLOMAS", typeDiplomas);
      commit("SET_TYPE_SURVEYS", typeSurveys);
      commit("SET_VIRTUAL_CLASSROOM_TYPES", classroomvirtualTypes);
      commit("RUN_FUNDAE_CONFIGURATION", fundaeConfiguration);
      commit("SET_TIMEZONES", timezones);
      commit("SET_TYPE_MONEYS", typeMoneys);
      commit("SET_TYPE_SESSIONS", typeSessions);
      commit("SET_CODES", codes);
      commit("SET_APPROVED_CRITERIA", approvedCriteria);
      commit("SET_TYPE_IDENTIFICATIONS", typeIdentifications);
      commit("SET_MODALITIES", modalities);
      commit("SET_COMPANIES", companies);
      if ("courseCategories" in r.data) {
        commit("SET_COURSE_CATEGORIES", r.data.courseCategories);
      }

      dispatch("loadTutors");
      commit("SET_ENROLLMENT_TEMPLATE_XLS", enrollmentTemplate);
      commit("SET_MAIN_IDENTIFICATION", mainIdentification);
      commit("SET_EXTRA", extra);

      if (id && id > 0) {
        // Load the announcement
        const { error, data } = await loadAnnouncement(id);
        commit("SET_ANNOUNCEMENT_DATA", data);
      }

      commit("SET_LOADING", false);
    }, source).catch((e) => {});
  },

  configureSteps({ commit, getters }, { stepsConfigurations, source }) {
    const steps = configureSteps(stepsConfigurations, source);
    Object.freeze(steps);
    commit("SET_STEPS_CONFIGURATIONS", steps);
  },

  loadTutors({ commit }) {
    loadTutors()
      .then((tutors) => {
        commit("SET_TUTORS", tutors);
      })
      .catch((e) => {});
  },

  /**
   * Save methods
   */
  async saveAnnouncementGeneralInfo({ commit, getters }) {
    const result = await saveAnnouncementGeneralInfo(getters["announcement"]);
    const { error, data } = result;
    if (!error) {
      const { id, access, didacticGuideResult } = data;
      commit("SET_ID", id);
      commit("SET_INSPECTOR_ACCESS", access);
      commit("SET_DIDACTIC_GUIDE", didacticGuideResult);
    }
    return result;
  },

  async saveAnnouncementBonus({ commit, getters }) {
    return saveAnnouncementBonus(getters["announcement"]);
  },

  async saveAnnouncementStudents({ commit, getters }) {
    const { error, data } = await saveAnnouncementStudents(
      getters["announcement"]
    );
    if (!error) {
      commit("SET_UPDATE_STUDENTS", data);
    }
    return { error, data };
  },

  async saveAnnouncementGroups({ commit, getters }) {
    return saveAnnouncementGroups(getters["announcement"]);
  },

  async saveAnnouncementCommunication({ commit, getters }) {
    return saveAnnouncementCommunication(getters["announcement"]);
  },

  async saveAnnouncementSurvey({ commit, getters }) {
    return saveAnnouncementSurvey(getters["announcement"]);
  },

  async saveAnnouncementCertificate({ commit, getters }) {
    return saveAnnouncementCertificate(getters["announcement"]);
  },

  async saveAnnouncementAlerts({ commit, getters }) {
    return saveAnnouncementAlerts(getters["announcement"]);
  },

  /**
   *
   * @param commit
   * @param getters
   * @param formData
   * @return {Promise<axios.AxiosResponse<*>|{trace: *, data: string, error: boolean}>}
   */
  async saveGroupTutorInfo({ commit, getters }, formData) {
    const result = ANNOUNCEMENT_VALIDATIONS.AnnouncementTutor.validate(
      getters["announcement"],
      formData
    );
    if (result.error) {
      return Promise.reject(result);
    }
    return saveGroupTutorInfo(formData);
  },

  async saveNewGroupTutor({ commit, getters }, formData) {
    const result = ANNOUNCEMENT_VALIDATIONS.AnnouncementTutor.validate(
      getters["announcement"],
      formData,
      true
    );
    if (result.error) {
      return Promise.reject(result);
    }
    return saveNewGroupTutor(formData);
  },

  async saveAnnouncementGeneralInfoExtern({ commit, getters }) {
    const result = await saveAnnouncementGeneralInfoExtern(
      getters["announcement"]
    );

    const { error, data } = result;
    if (!error) {
      const { id } = data;
      commit("SET_ID", id);
      commit("SET_APPLY_EXTERNAL_RESULT", data);
    } else {
    }

    return result;
  },

  /**
   * Step handlers
   */
  resetError({ commit }) {
    commit("RESET_ERROR");
  },

  async nextStep({ commit, dispatch, getters }) {
    commit("RESET_ERROR");

    const { status } = getters["announcement"];
    if (status === STATUS_ACTIVE) {
      // Do nothing, avoid saving
      commit("increaseStep");
      return;
    }

    let { confirmation } = getters;
    const { steps, stepsInformation } = getters;

    if (confirmation.name !== undefined && !confirmation.confirmed) {
      confirmation = {
        name: undefined,
        confirmed: false,
      };

      commit("SET_CONFIRMATION", confirmation);
    }

    const errors = {};
    const errorResult = await dispatch("validate", steps.current);
    if ("type" in errorResult) errors[errorResult.type] = errorResult;
    commit("SET_ERRORS", errors);
    if (errorResult.error) return;

    const currentStepInfo = stepsInformation.steps.find(
      (step) => step.step === steps.current
    );

    if (
      currentStepInfo.confirmationRequired &&
      currentStepInfo.type !== confirmation.name
    ) {
      commit("SET_CONFIRMATION", {
        name: currentStepInfo.type,
        confirmed: false,
      });
      return false;
    } else {
      commit("SET_CONFIRMATION", {
        name: undefined,
        confirmed: false,
      });
    }

    if (currentStepInfo.save && currentStepInfo.saveMethodName) {
      try {
        commit("SET_SAVING", true);
        const { error, data } = await dispatch(
          `${currentStepInfo.saveMethodName}`
        );
        if (error) {
          let isString = typeof data === "string";
          errors[currentStepInfo.type] = {
            error: true,
            i18n: isString,
            message: isString ? data : null,
            data,
            request: true,
          };
          commit("SET_ERRORS", errors);
          return;
        }
      } finally {
        commit("SET_SAVING", false);
      }
    }

    commit("increaseStep");
  },

  prevStep({ commit }) {
    commit("RESET_ERROR");
    commit("SET_CONFIRMATION", {
      name: undefined,
      confirmed: false,
    });
    commit("decreaseStep");
  },

  async onStepChange({ commit, getters, dispatch }, step) {
    commit("SET_ERRORS", {});
    commit("SET_CONFIRMATION", {
      name: undefined,
      confirmed: false,
    });

    const steps = getters["steps"];
    const direction = step > steps.current ? 1 : -1;
    if (direction === 1) {
      let isValid = true;
      const errors = {};
      for (let i = steps.current; i < step; i++) {
        const result = await dispatch("validate", i);
        errors[result.type] = result;
        if (result.error) isValid = false;
      }

      commit("SET_ERRORS", errors);
      if (isValid) {
        const stepsInformation = getters["stepsInformation"];
        const currentStepInfo = stepsInformation.steps.find(
          (step) => step.step === steps.current
        );
        if (currentStepInfo === undefined) {
          // commit('SET_CURRENT_STEP', 1);
          return;
        }
        if (currentStepInfo.save && currentStepInfo.saveMethodName) {
          try {
            commit("SET_SAVING", true);
            if (!(await dispatch(`${currentStepInfo.saveMethodName}`))) return;
          } finally {
            commit("SET_SAVING", false);
          }
        }

        commit("SET_CURRENT_STEP", step);
      }
    } else {
      commit("SET_CURRENT_STEP", step);
    }
  },

  /** Main validator **/
  validate({ commit, getters }, currentStep) {
    commit("RESET_ERROR");

    const validations = getters["validation"];
    const announcement = getters["announcement"];
    const stepsInformation = getters["stepsInformation"];
    const currentStepInfo = stepsInformation.steps.find(
      (step) => step.step === currentStep
    );
    if (!currentStepInfo) return { error: false };

    const activeValidation = validations[currentStepInfo.type];
    if (!activeValidation || !activeValidation.enabled)
      return {
        error: false,
        type: currentStepInfo.type,
      };

    return activeValidation.validate(announcement, getters['modalityList']);
  },

  /**
   * Other methods
   */
  async getCourseChaptersInfo({ commit, getters }) {
    const loadingChapters = getters["loadingChapters"];
    if (loadingChapters) return;
    commit("SET_LOADING_CHAPTERS", true);

    const { course, chapterTiming } = getters["announcement"];
    getCourseChapters(course.id, chapterTiming)
      .then((data) => {
        const { timing, chapters } = data;
        commit("setChapterTiming", timing);
        commit("SET_CHAPTERS", chapters);
      })
      .finally(() => {
        commit("SET_LOADING_CHAPTERS", false);
      });
  },

  setConfigAnnouncement({ commit }, config) {
    commit("SET_CONFIG_ANNOUNCEMENT", config);
  },

  setAlertTypeTutorValues({ commit }, typeTutorValues) {
    commit("SET_ALERT_TYPE_TUTOR_VALUES", typeTutorValues);
  },

  async findCourses(
    { commit },
    { query, typeCourse, page = 1, pageSize = 10, coursesStatus }
  ) {
    return findCourses({ query, typeCourse, page, pageSize, coursesStatus });
    // if (!error) {
    //     return data;
    // }
    // return [];
  },

  async loadPreSelectedCourse({ commit }, courseId) {
    const url = new URL(
      window.location.origin + "/admin/announcement/form/pre-selected-course"
    );
    url.searchParams.set("courseId", courseId);
    const result = await axios.get(url.toString());
    const { error, data } = result.data;
    if (!error) {
      commit("setPreSelectedCourse", data);
    }
  },

  async isStepValid({ commit, dispatch }, step) {
    const errors = {};
    const errorResult = await dispatch("validate", step.step);
    if ("type" in errorResult) errors[errorResult.type] = errorResult;
    commit("SET_ERRORS", errors);
    return !errorResult.error;
  },

  /**
   *
   * @param dispatch
   * @param commit
   * @param getters
   * @returns {Promise<boolean>}
   */
  async submitForm({ dispatch, commit, getters }) {
    try {
      const stepsInformation = getters["stepsInformation"];
      const lastStep = stepsInformation.steps[stepsInformation.steps.length - 1];
      if (!(await dispatch("isStepValid", lastStep))) return false;

      const errors = {};

      if (lastStep.save && lastStep.saveMethodName) {
        try {
          commit("SET_SAVING", true);
          const { error, data } = await dispatch(`${lastStep.saveMethodName}`);
          if (error) {
            let isString = typeof data === "string";
            errors[lastStep.type] = {
              error: true,
              i18n: isString,
              message: isString ? data : null,
              data,
              request: true,
            };
            commit("SET_ERRORS", errors);
            // return;
          }
          return !error;
        } finally {
          commit("SET_SAVING", false);
        }
      }
    } finally {
      commit("SET_SAVING", false);
    }
  },

  async uploadUsersExcelFile({ commit }, formData) {
    const headers = {
      "Content-Type": "multipart/form-data",
    };
    const result = await axios.post(
      "/admin/announcement/form/user-from-file",
      formData,
      {
        headers,
      }
    );
    const { error, data } = result.data;
    if (error && 'type' in data) {
      if (data.type === 'DUPLICATE_USERS') {
        let error = {
          type: 'AnnouncementStudents',
          data: {
            i18n: ['ANNOUNCEMENT.FORM.UPLOAD_USERS.EMAIL_DUPLICATED'],
            errorExtra: data.data
          },
        };
        let errors = {
          'AnnouncementStudents': error
        };
        commit("SET_ERRORS", errors);
      }
    }
    return result.data;
  },

  async uploadParticipantsExcelFile({ commit }, formData) {//migrated
    const headers = {
      'Content-Type': 'multipart/form-data'
    };
    const result = await axios.post('/admin/announcement/form/participants-from-file', formData, {
      headers
    });
    return result.data;
  },

  setErrors({ commit }, errors) {
    commit("SET_ERRORS", errors);
  },

  async deleteSessionFromGroup({}, sessionId) {
    const result = await axios.post(
      "/admin/announcement/form/delete-group-info-session",
      {
        id: sessionId,
      }
    );
    return result.data;
  },

  async saveGroupSession({}, { groupId, session }) {
    const result = await axios.post(
      "/admin/announcement/form/group-info-session",
      {
        ...session,
        groupId,
      }
    );

    return result.data;
  },
  reset({ commit}) {
    commit('RESET');
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
