<script>
import AnnouncementFormBase from "./AnnouncementFormBase.vue";
import {ANNOUNCEMENT_SOURCE} from "../store/module/announcementForm/common";
import ROUTE_NAMES from "../../courses/modules/courses/router/routeNames";

export default {
  name: "UpdateAnnouncement",
  components: {AnnouncementFormBase},
  async created() {
    const isUpdate = this.$route.name === 'UpdateAnnouncement';
    let enableCourseFinder = !isUpdate;
    const params = this.$route.params;
    if (params.origin) {
      await this.$store.dispatch('contentTitleModule/addRoute', {
        routeName: 'Home',
        params: {
          linkName: params?.isFromCourseDetails ? this.$t("COURSES.HOME.TITLE") : this.$t('ANNOUNCEMENTS'),
          params: {}
        }
      });

      if (params?.isFromCourseDetails) {
        this.$store.dispatch("contentTitleModule/addRoute", {
          routeName: ROUTE_NAMES.VIEW_COURSE,
          params: { linkName: params.courseName, params: {
            id: params.id,
            name: params.courseName
          }},
        });
      }

      if (params.origin === 'course') {
        enableCourseFinder = false;
        await this.$store.dispatch('announcementFormModule/loadPreSelectedCourse', params.id);
      }
    }

    await this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: isUpdate ? this.$t('ANNOUNCEMENT.UPDATE') : this.$t('ANNOUNCEMENT.CREATE'),
        params: this.$route.params
      }
    });
    await this.$store.dispatch('announcementFormModule/setEnableCourseFinder', enableCourseFinder);

    this.$store.dispatch('announcementFormModule/loadPreData', { id: isUpdate ? this.$route.params.id : null, source: ANNOUNCEMENT_SOURCE.INTERN});
  },
  methods: {
    goToDetail(id) {
      this.$router.replace({ name: 'ViewAnnouncement', params: { id, isFromCourseDetails: this.$route.params?.isFromCourseDetails } });
    }
  }
}
</script>

<template>
  <announcement-form-base @saved="goToDetail"/>
</template>

<style scoped lang="scss">

</style>
