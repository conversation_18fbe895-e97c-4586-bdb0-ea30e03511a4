<script>
import {get} from "vuex-pathify";
import AssistanceTable from "../../components/details/assistanceTable.vue";
import FragmentContent from "../../components/details/fragmentContent.vue";
import Spinner from "../../../admin/components/base/Spinner.vue";
import DataNotFound from "../../components/details/DataNotFound.vue";
import StudentsTable from "../../components/details/studentsTable.vue";
import AccordionContent from "../../components/details/accordionContent.vue";
import {UtilsMixin} from "../../mixins/utilsMixin";
import dateTimeFormatterMixin from "../../../common/mixins/dateTimeFormatterMixin";
import ShortAnswerTag from "../../components/details/shortAnswerTag.vue";
import Loader from "../../../admin/components/Loader.vue";

export default {
  name: "AnnouncementInfoExtern",
  mixins: [UtilsMixin, dateTimeFormatterMixin],
  components: {
    Loader,
    ShortAnswerTag, AccordionContent, StudentsTable, DataNotFound, Spinner, FragmentContent, AssistanceTable},
  computed: {
    loading: get('announcementModule/loading'),
    announcement: get('announcementModule/announcement'),
    loadingUsers: get('announcementModule/loadingCalledUsers'),
    groupStudents: get('announcementModule/calledUsers'),
    firstGroup() {
      if (this.groupStudents.length === 0) return {};
      return this.groupStudents[0];
    },

    basicInfoDetails() {
      const details = [
        { question: this.$t('COURSES.COURSE_NAME'), answer: this.announcement.course?.name || '--' },
        { question: this.$t('ANNOUNCEMENT.FORM.ENTITY.START_AT'),
          answer: this.getDateTimeFormatted(this.announcement.startAt, 'es-ES', {
            timezone: this.announcement.timezone,
            hour12: false
          })
        },
        { question: this.$t('CODE'), answer: this.announcement.code || '--' },
        { question: this.$t('ANNOUNCEMENT.FORM.ENTITY.FINISH_AT'),
          answer: this.getDateTimeFormatted(this.announcement.finishAt, 'es-ES', {
            timezone: this.announcement.timezone,
            hour12: false
          })
        },
        { question: this.$t('CATEGORY'), answer: this.announcement.course?.category?.name || '--' },
        { question: this.$t('ANNOUNCEMENT.FORM.ENTITY.FORMATION_TIME'), answer: this.formatHoursToTime(this.announcement.totalHours || 0)},
        { question: this.$t('LOCALE'), answer: this.announcement.course.locale },
        { question: this.$t('ANNOUNCEMENT.FORM.GROUP.PLACE'), answer: this.firstGroup.groupInfo?.place },
        { question: this.$t('TIMEZONE'), answer: this.announcement.timezone },
      ];
      if (this.announcement.extra && typeof this.announcement.extra === 'object') {
        for (const [key, value] of Object.entries(this.announcement.extra)) {
          details.push({
            question: value.name || '--',
            answer: value.value !== null ? value.value : '--'
          });
        }
      }
      return details;
    },


    costsInfo() {
      return [
        {question: this.$t('ANNOUNCEMENT_OBSERVATION.TOTAL_COST'), answer: this.firstGroup?.groupInfo?.cost || '--'},
        {question: this.$t('ANNOUNCEMENT.GROUP_SESSION_INFO.TYPE_MONEY'), answer: this.firstGroup?.groupInfo?.typeCost?.name || '--'},
      ];
    }
  },
  watch: {
    announcement: {
      handler: function () {
        this.initView();
      },
      deep: true,
      immediate: true
    },
  },
  methods: {
    initView() {
      this.viewData();
    },
    printData() {
      window.print()
    },
    viewData() {
      this.$store.dispatch(
          "announcementModule/loadAnnouncementCalledUsers",
          this.$route.params.id
      );
    }
  }
}
</script>

<template>
  <div class="AnnouncementInfoExtern bg-white p-1 p-sm-4">
    <div class="w-100 d-flex align-items-center mt-3">
      <div class="badges d-flex flex-wrap">
        <span class="badge bg-info text-white"><i :class="announcement.course.icon" class="mr-2"></i>{{ announcement.course.type }}</span>
      </div>
      <button class="btn btn-sm btn-primary ml-auto" type="button" @click="printData">
        <i class="fa fa-download"></i> {{ $t('ANNOUNCEMENT.INFOTAB.DOWNLOAD') }}
      </button>
    </div>

    <div class="w-100 BasicInfo mt-3">
      <div class="row">
        <short-answer-tag
            v-for="(detail, index) in basicInfoDetails"
            :key="`basic-info-details-${index}`"
            class="col-md-4 col-xs-12 mt-2 fragmentDetail"
            :question="detail.question + (detail.question.length ? ':' : '')"
            :answer="detail.answer"
            :href="detail.href"/>
      </div>
      <div class="tutor-info p-2">
        <h4 class="text-center">{{ $t('ANNOUNCEMENT.TUTOR.TITLE') }}</h4>
        <loader :is-loaded="loadingUsers" />
        <div v-if="!loadingUsers"
            class="avatar"
            :style="{
              'background-image': `url(/uploads/users/avatars/default.svg)`,
            }"
        ></div>
        <span v-if="!loadingUsers" class="w-100 text-center"><i class="fa fa-user"></i> {{ firstGroup?.groupInfo?.tutor?.name ?? '' }}</span>
        <span v-if="!loadingUsers" class="w-100 text-center"><i class="fa fa-envelope"></i> {{ firstGroup?.groupInfo?.tutor?.email ?? '' }}</span>
      </div>
    </div>

    <fragment-content
        tag="students"
        :title="$t('ANNOUNCEMENT.FORM.STEPS.STUDENTS') || ''"
        class="oversize mt-3"
    >
      <div class="w-100 d-flex align-items-center justify-content-center flex-column" v-if="loadingUsers">
        <spinner />
      </div>
      <div class="row">
        <accordion-content
            v-for="(group, index) in groupStudents"
            icon="fa-users"
            :title="group.name || ''"
            :badge="`${$t('ANNOUNCEMENT.PEOPLE')}: ${group.total}`"
            :opened="!index">
          <students-table
              :student-list="group.users || []"
              class="col-12 pb-3"
              :show-details="false"
              :tag="'group' + (index + 1) + '-students'"/>
        </accordion-content>
      </div>
      <div>
        <DataNotFound :hide-on="!loadingUsers && !groupStudents.length"
                      :text="$t('ANNOUNCEMENT.STUDENTTAB.NOT_FOUND') || ''"
                      icon="fa-users"
                      :banner="true" />
      </div>
    </fragment-content>

    <fragment-content
        tag="costs"
        icon="fa-coins"
        :title="$t('ANNOUNCEMENT.GROUP_SESSION_INFO.COST') || ''"
        class="mt-3"
        :details="costsInfo"
    ></fragment-content>
  </div>
</template>

<style scoped lang="scss">
.AnnouncementInfoExtern {
  .badge {
    font-size: 1rem;
    font-weight: normal;
    padding: 0.5rem 1rem;
    white-space: nowrap;
    user-select: none;

    i {
      margin-right: 0.5rem;
    }
  }

  .BasicInfo {
    display: grid;
    grid-template-columns: 1fr 25%;
    align-items: flex-start;

    .tutor-info {
      background-color: var(--color-primary-light);
      color: #212121;
      display: flex;
      justify-content: center;
      flex-flow: column;
      gap: .5rem;
      border-radius: 5px;

      .avatar {
        @include avatar;
        width: 100px !important;
        height: 100px !important;
        background-color: unset;
      }
    }
  }
}
</style>
