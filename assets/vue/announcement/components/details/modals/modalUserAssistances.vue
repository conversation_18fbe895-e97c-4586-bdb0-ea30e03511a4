<template>
  <div class="modalUserAssistances">
    <BaseModal
      :identifier="tag + 'userAssistance'"
      :title="`${$t('ANNOUNCEMENT.ASSISTANCE')}: ${session.title}`"
      size="modal-xl"
      padding="0px"
    >
      <div class="modalUserAssistances--info d-flex flex-row flex-nowrap">
        <div class="d-flex flex-column flex-grow-1">
          <p class="pb-2 font-weight-bold">
            <i class="fa fa-calendar mr-2"></i>{{ getDateText(session.startAt) }}
          </p>
          <p class="pb-2"><i class="fa fa-arrow-right"></i> {{ $t('ANNOUNCEMENT.GROUP_SESSION_INFO.ENTRY_MARGIN') }}: {{ session.entryMargin }}% ({{ session.hourInit }} - {{ session.entryMarginMaxTime }})</p>
          <p class="pb-2"><i class="fa fa-arrow-left"></i> {{ $t('ANNOUNCEMENT.GROUP_SESSION_INFO.EXIT_MARGIN') }}: {{ session.exitMargin }}% ({{ session.exitMarginMinTime }} - {{ session.hourEnd }})</p>
        </div>
        <div class="d-flex flex-column p-3 flex-grow-1 UploadAssistance--container">
          <file-as-text
              id="AnnouncementModalAssistance--btn-upload-form"
              class="col-12"
              :label="$t('ANNOUNCEMENT.MODALS.ASSISTANCE_UPLOAD_FILE') + ''"
              :label-info="$t('ANNOUNCEMENT.MODALS.ASSISTANCE_UPLOAD_FILE_DESCRIPTION')"
              :label-with-info="true"
              :with-input="false"
              @click="showUploadForm = !showUploadForm"
              label-description-position="top"
              v-model="firstSelectedFile"
          />
        </div>

      </div>
      <div class="modalUserAssistances--table container" v-show="!showUploadForm">
        <table class="table table-condensed">
          <thead>
            <tr>
              <th class="pb-0" colspan="3"></th>
              <th class="text-center pb-0 px-0 fs-5">
                {{ $t("ANNOUNCEMENT.ASSISTANCE_STUDENTS") }}
              </th>
              <th class="text-center pb-0 px-0 fs-5" colspan="2">
                {{ $t("ANNOUNCEMENT.ASSISTANCE_TUTOR") }}
              </th>
            </tr>
            <tr>
              <th style="width: 60px"></th>
              <th class="pt-0" style="width: 3rem"></th>
              <th class="pt-0 color-neutral-darkest"><strong>{{ $t("NAME") }}</strong></th>
              <th class="text-center pt-0 color-neutral-darkest">Check In/Out</th>
              <th class="text-center pt-0">
                <div class="d-flex flex-nowrap flex-row align-items-center gap-1 pl-2">
                  <i
                      :class="allChecked ? 'text-primary' : 'fa-stop text-gray'
                    "
                      class="fa fa-check-square cursor-pointer"
                      @click="toggleAll"
                  ></i>
                  <span class="cursor-pointer color-neutral-darkest" @click="toggleAll">
                    {{ $t('INPUT.CHECK_ALL') }}
                  </span>
                </div>
              </th>
              <th class="actions--chevron"></th>
            </tr>
          </thead>
          <tbody v-for="(user, index) in session.assistance"
                 :key="tag + 'user_assistance' + index"
          >
            <tr class="UserAssistance--basic-info">
              <td style="width: 60px">
                <span class="badge bg-warning text-white" v-if="user.external">EXT</span>
              </td>
              <td style="width: 3rem">
                <img class="userAvatar" :src="user.avatar || ''" alt=" " />
              </td>
              <td class="color-neutral-darkest">{{ user.name }}</td>
              <td class="text-center">
                <div class="d-flex flex-nowrap flex-row align-items-center justify-content-center">
                  <span class="badge text-white" :class="user.student_assistance ? 'bg-primary' : 'bg-danger'">
                    {{ $t(user.student_assistance ? 'YES' : 'NO') }}
                  </span>
                </div>
              </td>
              <td class="text-center">
                <div class="d-flex flex-nowrap flex-row align-items-center gap-1">
                  <i
                      :class="
                    user.assistance ? 'text-primary' : 'fa-stop text-gray'
                  "
                      class="fa fa-check-square cursor-pointer"
                      @click="toggleUserCheck(index)"
                  ></i>
                  <Multiselect
                      v-if="user.assistance"
                      v-model="user.percent"
                      :options="assistancePercent"
                      :searchable="false"
                      :placeholder="$t('MULTISELECT.PLACEHOLDER')"
                      :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
                      :show-labels="false"
                  >
                    <template v-slot:option="option">
                      {{ $t('ANNOUNCEMENT.GROUP_SESSION_INFO.ASSISTANCE_PERCENT', [option.option]) }}
                    </template>
                    <template v-slot:singleLabel="singleLabel">
                      {{ $t('ANNOUNCEMENT.GROUP_SESSION_INFO.ASSISTANCE_PERCENT', [singleLabel.option]) }}
                    </template>
                  </Multiselect>
                </div>
              </td>
              <td class="text-center actions--chevron">
                <i class="fa fa-chevron-up" v-if="activeUserIdInfo === user.id" @click="activeUserIdInfo = null;"></i>
                <i class="fa fa-chevron-down" v-if="activeUserIdInfo !== user.id" @click="activeUserIdInfo = user.id"></i>
              </td>
            </tr>
          <tr class="UserAssistance--assistance-info" v-if="activeUserIdInfo === user.id && user.student_assistance_info.length > 0">
            <td colspan="6">
              <table class="table table-condensed AssistanceInfo">
                <thead>
                <tr>
                  <th class="text-center" colspan="2">{{ $t('ANNOUNCEMENT.GROUP_SESSION_INFO.ENTRY_EXIT_REGISTER') }}</th>
                  <th colspan="2">({{ session.timezoneOffset }}) {{ session.timezone }}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(assistance_info, assistance_info_index) in user.student_assistance_info" :key="tag + 'user_assistance' + index + 'ass_info' + assistance_info_index">
                  <td class="default">
                    {{ assistance_info.ip }}
                  </td>
                  <td colspan="2">
                    <span v-if="assistance_info.entry" class="AssistanceInfo--entry"
                          :class="entryExitClass(assistance_info.inTime)">
                      <i class="fa fa-arrow-right"></i> {{ getDateTime(assistance_info.entry) }}
                    </span>
                    <span v-if="assistance_info.exit" class="AssistanceInfo--exit"
                          :class="entryExitClass(assistance_info.inTime)">
                      <i class="fa fa-arrow-left"></i> {{ getDateTime(assistance_info.exit) }}
                    </span>
                  </td>
                  <td>
                    <span v-if="assistance_info.inTime === false"
                          class="out-of-time--info"
                          :class="assistance_info.entry ? 'EntryOutOfTime' : 'ExitOutOfTime'"
                    >
                      <i class="fa fa-exclamation-circle"></i> {{ $t('ANNOUNCEMENT.GROUP_SESSION_INFO.OUT_OF_TIME') }}
                    </span>
                  </td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <div v-if="showUploadForm">
        <form-file-uploader
            :file-types="fileTypeAssistance"
            id="form-upload-assistance-files"
            @input="selectedFiles = $event"
            @upload="uploadFiles"
            @cancel="showUploadForm = false"
            :uploading="uploadingFiles"
            :show-submit="true"
            :show-cancel="true"
        />
      </div>
      <div v-if="tutorView && !showUploadForm" class="row p-3">
        <div
            class="col-12 text-right d-flex flex-row flex-nowrap justify-content-end"
        >
          <button
              type="button"
              class="btn btn-primary mr-2 pl-2 pr-2"
              @click="downloadAssistanceFile()"
          >
            <i class="fa fa-download"></i>
            {{ $t("ANNOUNCEMENT.MODALS.ASSISTANCE_DOWNLOAD_TEMPLATE") }}
          </button>
          <button
              class="btn btn-primary pl-2 pr-2"
              type="button"
              :disabled="saving"
              @click="saveassistance"
          >
            {{ $t("SAVE") }}
          </button>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script>
import { UtilsMixin } from "../../../mixins/utilsMixin";
import { get } from "vuex-pathify";
import FormFileUploader from "../../../../common/components/FormFileUploader.vue";
import Multiselect from "vue-multiselect";
import FileAsText from "../../../../common/components/file/FileAsText.vue";
import BaseModal from "../../../../base/BaseModal.vue";


export default {
  name: "modalUserAssistances",
  mixins: [UtilsMixin],
  components: {FileAsText, FormFileUploader,  Multiselect, BaseModal },
  props: {
    tag: { type: String, default: "" },
    tutorView: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      saving: false,
      showUploadForm: false,
      uploadingFiles: false,
      fileTypeAssistance: [
        {
          name: 'PDF/Images',
          accept: 'application/pdf, image/png, image/jpg, image/jpeg',
          type: 'default',
          multiple: true
        }
      ],
      activeUserIdInfo: null,
      assistancePercent: [
        100, 75, 50, 25
      ],
      selectedFiles: []
    };
  },
  computed: {
    announcement: get("announcementModule/announcement"),
    session: get("announcementModule/sessionSelected"),
    allChecked() {
      return (this.session?.assistance || []).every((user) => user.assistance);
    },
    selectedFilesText() {
      let txt = '<strong>' + this.$t('ANNOUNCEMENT.ASSISTANCE_FILES') + '</strong><br><hr><div class="AssistanceFilesInfo">';
      for (let i = 0; i < this.selectedFiles.length; i++)
      {
        txt += `<strong>${ i + 1}</strong>: ` + this.selectedFiles[i].name + '<br>'
      }
      txt += '</div>'
      return txt;
    },
    firstSelectedFile: {
      get() {
        if (this.selectedFiles.length < 1) return {name: this.$t("ANNOUNCEMENT.MODALS.ASSISTANCE_UPLOAD_FILE")};
        return this.selectedFiles[0];
      },
      set(newValue) {
        // Ignore
      }
    }
  },
  watch: {
    session: {
      deep: true,
      handler: function (val, oldVal) {
        this.selectedFiles = [];
      }
    }
  },
  methods: {
    getDateTime(data) {
      const timestamp = Date.parse(data);
      return new Date(timestamp).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: this.session.timezone
      })
    },
    entryExitClass(inTime) {
      if (inTime == null) return 'default';
      return inTime === true ? 'in-time' : 'out-of-time';
    },
    toggleAll() {
      if (this.saving) return;
      const newValue = !this.allChecked;
      this.session.assistance.forEach((user) => (user.assistance = newValue));
    },
    toggleUserCheck(index) {
      if (this.saving) return;
      this.session.assistance[index].assistance =
        !this.session.assistance[index].assistance;
    },
    saveassistance() {
      if (this.saving) return;

      this.saving = true;
      this.$toast.info(`${this.$t("ANNOUNCEMENT.MODALS.ASSISTANCE_INFO")}`);
      this.$store
        .dispatch("announcementModule/updateGroupAssistance", {
          idGroup: this.session.groupId,
          idSession: this.session.id,
          assistance: this.session.assistance.map((user) => ({
            id: user.id,
            assistance: user.assistance,
            percent: user.percent,
          })),
        })
        .then(() => {
          this.$toast.clear();
          this.$toast.success(
            `${this.$t("ANNOUNCEMENT.MODALS.ASSISTANCE_SUCCESS")}`
          );
          document.getElementById(this.tag + "userAssistance_close").click();
        })
        .catch(() => {
          this.$toast.clear();
          this.$toast.error(`${this.$t("ANNOUNCEMENT.STUDENTTAB.ERROR")}`);
        })
        .finally(() => {
          this.saving = false;
        });
    },

    uploadFiles() {
      const formData = new FormData();
      const files = document.getElementById(`form-upload-assistance-files-file`).files;
      if (files.length === 1) {
        formData.append('file', files[0]);
      } else {
        for (let i = 0; i < files.length; i++) {
          formData.append(`file_${i}`, files[i]);
        }
        formData.append('filesLength', files.length);
      }

      formData.append('sessionId', this.session.id);
      this.$alertify.confirmWithTitle(
          this.$t('FILE_UPLOAD.CONFIRM_UPLOAD.TITLE'),
          this.$t('FILE_UPLOAD.CONFIRM_UPLOAD.DESCRIPTION'),
          () => {
            this.uploadingFiles = true;
            this.$store.dispatch('announcementModule/uploadAssistanceFiles', {sessionId: this.session.id, formData}).then(res => {
              const { error } = res;
              this.$toast.clear();
              if (error) {
                this.$toast.error(this.$t('FILE_UPLOAD.UPLOAD.FAILED') + '');
              } else {
                this.$toast.success(this.$t('FILE_UPLOAD.UPLOAD.SUCCESS') + '');
                this.showUploadForm = false;
              }
            }).finally(() => {
              this.uploadingFiles = false;
            })
          },
          () => {}
      );
    },

    downloadAssistanceFile() {
      this.$store.dispatch(
        "announcementModule/downloadAnnouncementGroupSessionAssistance",
        { sessionId: this.session.id }
      );
    }
  },
};
</script>

 <style scoped lang="scss"> 
.modalUserAssistances {
  .userAvatar {
    width: 2rem;
    height: 2rem;
    display: block;
  }

  .modalTitle {
    gap: 1rem;
  }

  &--info {
    padding: 2rem;
  }

  .container {
    padding: 2rem;
    border-radius: 7px;
    background-color: white;

    & > table {
      height: 400px;
      overflow: auto;
    }
  }

  table {
    font-size: 0.9rem;

    &.AssistanceInfo {
      td {
        border: 0 !important;
      }
    }
  }

  ::v-deep {
    .modal-body {
      background-color: var(--color-neutral-lighter);
    }
  }

  tbody {
    display: block !important;
  }

  thead,
  tbody,
  tr {
    display: table;
    width: 100%;
    table-layout: fixed;
  }

  thead {
    width: calc(100% - 1rem);
  }

  .fa-check-square {
    font-size: 1.5rem;
  }

  .AssistanceFileUpload {
    input {
      display: none;
    }
  }

  .AssistanceInfo {
    .default {
      color: var(--color-neutral-mid-darker) !important;
    }

    &--entry {
      &.in-time {
        color: #53DF52;
      }
      &.out-of-time {
        color: #FC6F03;
      }
    }
    &--exit {
      &.in-time {
        color: #53DF52;
      }
      &.out-of-time {
        color: #E5554F;
      }
    }

    .out-of-time--info {
      &.EntryOutOfTime {
        color: var(--color-warning);
      }
      &.ExitOutOfTime {
        color: var(--color-tertiary);
      }
    }
  }
  :deep(.multiselect__tags) {
    border: 1px solid var(--color-primary) !important;
    color: var(--color-primary) !important;
  }
  :deep(.multiselect__content) {
    border: 1px solid var(--color-primary) !important;
  }
  :deep(.multiselect__element) {
    color: var(--color-primary) !important;
  }

  .actions--chevron {
    width: 40px;
    color: var(--color-primary);
    font-size: 20px;
  }

  .UploadAssistance--container {
    background-color: #D9F0FA;
    border-color: #D9F0FA !important;
    border-radius: 5px;
  }

  .UserAssistance {
    &--basic-info {
      background-color: #D9F0FA;
      border-color: #D9F0FA !important;
      .text-user-assistance {
        color: var(--color-neutral-darker);
      }
    }
    &--assistance-info {
      background-color: #eef7fb;
      border: 1px solid #D9F0FA !important;
    }
  }

  :deep(.AssistanceFilesInfo) {
    max-height: 60px;
    width: 100%;
    overflow: auto;
  }

  .bg-primary {
    color: #fff !important;
    background-color: var(--color-primary) !important;
  }

  :deep(#form-upload-assistance-files) {
    .FormFileUploader--type {
      .selector-title, .selector-container {
        padding: 0rem 1rem;
      }
    }
    .FileSelector {
      padding: 1rem;
    }
  }
}
</style>
