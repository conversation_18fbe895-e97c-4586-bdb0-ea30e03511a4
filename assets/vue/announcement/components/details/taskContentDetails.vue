<template>
<div class="taskContentDetails">
  <div class="row mb-3">
    <div class="col-12 pl-5">
      <table class="table-condensed">
        <tbody>
        <tr>
          <td class="font-weight-bold">{{ $t('ANNOUNCEMENT.MODALS.PROGRESS_TASK1') }}:</td>
          <td class="font-weight-bold text-primary">{{ completedAvg }}%</td>
        </tr>
        <tr>
          <td>{{ $t('ANNOUNCEMENT.MODALS.PROGRESS_TASK4') }}:</td>
          <td class="font-weight-bold">{{ chaptersCompleted || 0 }} / <span class="text-primary">{{ chapters.length || 0 }}</span></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="row">
    <table class="table table-condensed table-sm col-12">
      <tbody>
      <tr v-for="(chapter, index) in chapters"
          :key="'chapter-'+index">
        <td style="width: 2.2rem;">
          <img :src="chapter.image || ''" class="chapterImage" alt=" ">
        </td>
        <td>{{chapter.title}}</td>
        <td class="subtitle text-right">
          <span v-if="chapter.state === 'COMPLETED'">
            {{ getDateTime(chapter.finishedAt) }}
            <i class="fa fa-check text-primary ml-2"></i></span>
          <span v-else class="badge font-weight-bold"
                :class="{'badge-warning': chapter.state === 'IN_PROCESS'}">
            {{ $t(chapter.state === 'IN_PROCESS' ? 'IN_PROCESS': 'NO_STARTING') }}
          </span>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</div>
</template>

<script>
import {UtilsMixin} from "../../mixins/utilsMixin";

export default {
  name: "taskContentDetails",
  mixins: [UtilsMixin],
  props: {
    chapters: {
      type: Array,
      default: () => ([])
    }
  },
  computed: {
    completedAvg() {
      return Math.round(this.chaptersCompleted * 100 / this.chapters.length);
    },
    chaptersCompleted() {
      return this.chapters.filter((chapter) => chapter.state === 'COMPLETED').length
    }
  }
}
</script>

 <style scoped lang="scss"> 
.taskContentDetails {
  .chapterImage {
    width: 2rem;
    display: block;
    margin: auto;
    aspect-ratio: 1;
    object-fit: cover;
    object-position: center;
    position: relative;

    &:before {
      content: ' ';
      display: block;
      position: absolute;
      height: 100%;
      width: 100%;
      background-color: var(--color-neutral-mid-dark);
    }
  }
  table td {
    width: 200px;
  }
  .progress-title {
    font-size: 1.2rem;
  }
  .subtitle {
    font-size: 0.8rem;
    color: var(--color-neutral-mid-dark);
  }
  .badge {
    background-color: var(--color-neutral-mid-light);
    color: var(--color-neutral-mid-dark);
  }
  .badge-warning {
    background-color: var(--color-secondary-lightest) !important;
    color: var(--color-secondary-warning) !important;
  }
}
</style>
