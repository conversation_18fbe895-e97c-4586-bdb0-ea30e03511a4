<script>
import { get } from "vuex-pathify";
import Spinner from "../../../base/BaseSpinner.vue";
import FileAsText from "../../../common/components/file/FileAsText.vue";
import Multiselect from "vue-multiselect";

export default {
  name: "NewUserTutor",
  components: { FileAsText, Spinner, Multiselect },
  props: {
    groupId: {
      type: Number | String,
      required: true,
    },
    isSubsidized: {
      type: Boolean,
      default: false,
    },

    typeIdentifications: {
      type: Object | Array,
      default: () => [],
    },

    mainIdentification: {
      type: Object,
      default: () => {},
    },

    companies: {
      type: Object | Array,
      default: () => [],
    },
  },
  data() {
    return {
      savingTutor: false,
      tutorId: -1,
      name: null,
      dni: null,
      email: null,
      telephone: null,
      cv: null,
      tutoringTime: null,
      identificationValue: null,
      identification: {
        identification: null,
        value: null,
      },
      company: null,
    };
  },
  watch: {
    telephone: {
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        const isNum = /^\d+$/.test(val);
        let result = val;
        if (!isNum && val.length > 0) result = val.replace(/\D/g, "");
        this.telephone = result;
      },
    },

    mainIdentification: {
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.identification = {
          identification: val?.id || null,
          value: null,
        };
      },
    },
  },
  computed: {
    nameIdentificationMain() {
      this.identification = this.mainIdentification;
      return this.mainIdentification?.name || "DNI";
    },

    froalaConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 250,
        pluginsEnabled: ["align", "lists", "paragraphStyle", "paragraphFormat"],
        pastePlain: true
      };
    },
  },
  methods: {
    identificationKeyDown(e) {
      const isValid = /^[a-zA-Z0-9\-_]*$/g.test(e.key);
      if (!isValid)e.preventDefault();
    },
    invalidInput(event) {
      if (event.target.validity.valueMissing) {
        event.target.setCustomValidity(this.$t('REQUIRED_FIELD') + '');
      }
    },
    saveTutorInfo() {
      this.savingTutor = true;
      const form =
        document.forms[`user-group-${this.groupId}-tutor-modal-new-form`];
      const formData = new FormData(form);
      formData.append("id", this.groupId);
      formData.append("tutoringTime", this.tutoringTime ?? "");
      formData.append("identification", JSON.stringify(this.identification));
      formData.append("company", JSON.stringify(this.company));

      if (this.cv == null && this.isSubsidized) {
        this.$toast.error(
          this.$t("ANNOUNCEMENT.FORM.ENTITY.GROUP.TUTOR_CV_REQUIRED") + ""
        );
        return;
      }

      if (this.cv instanceof File) formData.append("cv", this.cv);
      else formData.append("cv", JSON.stringify(this.cv));

      this.$store
        .dispatch("announcementFormModule/saveNewGroupTutor", formData)
        .then((r) => {
          const { data, error } = r;
          if (error) {
            this.$toast.error(this.$t(data, ['PDF']) + '');
          }
          else {
            this.$emit("input", data);
            document
              .getElementById(`user-group-${this.groupId}-tutor-modal_close`)
              .click();
          }
        })
        .finally(() => {
          this.savingTutor = false;
        });
    },
  },
};
</script>

<template>
  <div class="NewUserTutor">
    <div
      v-if="savingTutor"
      class="d-flex align-items-center justify-content-center"
    >
      <spinner />
    </div>
    <form
      action=""
      :id="`user-group-${groupId}-tutor-modal-new-form`"
      class="TutorSelection"
      @submit.prevent="saveTutorInfo()"
      v-show="!savingTutor"
    >
      <div class="w-100 d-flex">
        <div class="fa fa-tutor ml-auto mr-auto"></div>
      </div>
      <div class="w-100 d-flex flex-row flex-wrap">
        <div class="form-group required col-xs-12 col-md-6">
          <label>{{ $t("NAME") }}</label>
          <input type="text" class="form-control" required name="firstName" @invalid="invalidInput" :maxlength="125"/>
        </div>

        <div class="form-group required col-xs-12 col-md-6">
          <label>{{ $t("SUBSCRIPTION.LAST_NAME") }}</label>
          <input type="text" class="form-control" required name="lastName" @invalid="invalidInput" :maxlength="125"/>
        </div>

        <div class="form-group required col-6">
          <label
            ><i class="fa fa-envelope mr-3"></i>{{ $t("USER.EMAIL") }}</label
          >
          <input
            required
            type="email"
            class="form-control"
            v-model="email"
            name="email"
            @invalid="invalidInput"
          />
        </div>

        <div
          class="form-group  col-6"
          v-if="companies && companies.length > 0"
        >
          <label
            ><i class="fa fa-building card mr-3"></i
            >{{ $t("USER.USER_FIELDS_FUNDAE.USER_COMPANY") }}</label
          >
          <Multiselect
            v-model="company"
            :options="companies"
            :allow-empty="false"
            :searchable="true"
            track-by="id"
            label="name"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
          ></Multiselect>
        </div>
      </div>
      <hr />
      <div class="row form-group">
        <!--   <label class="w-100 pl-2"
          >{{ $t("IDENTIFICATION") }}
          {{ nameIdentificationMain }}</label
        >
        <div class="col-6">
          <Multiselect
            v-model="identification"
            :options="typeIdentifications"
            :allow-empty="false"
            :searchable="true"
            track-by="id"
            label="name"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
          ></Multiselect>
        </div> -->
        <div class="col-6">
          <label
            ><i class="fa fa-address-card mr-3"></i>
            {{ nameIdentificationMain }}</label
          >
          <input
            type="text"
            class="form-control"
            name="identificationValue"
            v-model="identificationValue"
            @keydown="identificationKeyDown"
            :maxlength="20"
          />
        </div>

        <div class="col-6" :class="isSubsidized ? 'required' : ''">
          <label><i class="fa fa-mobile mr-3"></i> {{ $t("TELEPHONE") }}</label>
          <input
            type="text"
            class="form-control"
            v-model="telephone"
            name="telephone"
            :required="isSubsidized"
            @invalid="invalidInput"
          />
        </div>
      </div>

      <file-as-text
        :id="`modal-tutor-file-${groupId}`"
        :name="`modal-tutor-file-${groupId}`"
        label="Curriculum"
        v-model="cv"
      >
        <template v-slot:label-left>
          <i class="fa fa-file mr-3"></i>
        </template>
      </file-as-text>

      <div class="form-group col-12">
        <label class="w-100">
          <i class="fa fa-clock mr-3"></i
          >{{ $t("ANNOUNCEMENT.FORM.GROUP.ENTITY.TUTORING_TIME") }}</label
        >
        <froala
          tag="textarea"
          v-model="tutoringTime"
          :config="froalaConfig"
        ></froala>
      </div>

      <div class="d-flex w-100 mt-3 justify-content-end">
        <button type="submit" class="btn btn-primary">
          {{ $t("ANNOUNCEMENT.TUTOR.SAVE") }}
        </button>
      </div>
    </form>
  </div>
</template>

<style scoped lang="scss"></style>
