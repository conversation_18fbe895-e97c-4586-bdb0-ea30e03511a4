<script>
import FileAsText from "../../../common/components/file/FileAsText.vue";
import Spinner from "../../../base/BaseSpinner.vue";
import NewUserTutor from "./NewUserTutor.vue";
import Multiselect from "vue-multiselect";

export default {
  name: "TutorFormModal",
  components: { NewUserTutor, Spinner, FileAsText, Multiselect },
  props: {
    showForm: {
      type: Boolean,
      default: true
    },
    groupId: {
      type: Number | String,
      required: true,
    },

    tutors: {
      type: Object | Array,
      default: () => [],
    },

    typeIdentifications: {
      type: Object | Array,
      default: () => [],
    },

    mainIdentification: {
      type: Object,
      default: () => {},
    },

    companies: {
      type: Object | Array,
      default: () => [],
    },

    isSubsidized: {
      type: Boolean,
      default: false,
    },

    value: {
      type: Object | Array,
      default: () => ({
        tutorId: -1,
        name: "",
        email: "",
        telephone: "",
        cv: null,
        tutoringTime: "",
        identificationValue: "",
        identification: {
          identification: null,
          value: null,
        },
        companyValue: "",
        company: null,
      }),
    },
  },
  data() {
    return {
      savingTutor: false,
      newTutor: false,
      tutorBck: null,
      tutorSelected: {},
      errors: {
        cv: null
      },
      tutor: {
        tutorId: -1,
        name: null,
        email: null,
        telephone: null,
        cv: null,
        tutoringTime: null,
        identificationValue: null,
        identification: {
          identification: null,
          value: null,
        },
        companyValue: "",
        company: null,
      },
    };
  },
  computed: {
    tutorId: {
      get() {
        return parseInt(this.tutor.tutorId || this.value.tutorId);
      },
      set(newValue, oldValue) {
        const id = +(newValue || 0);
        if (id < 0 || isNaN(id) || id === oldValue) return;
        this.tutor.tutorId = id;
        const foundTutor = this.tutors.find((t) => t.id === id);
        if (foundTutor == null) this.tutor.tutorId = -1;

        this.name = foundTutor?.name;
        this.identificationValue = foundTutor?.dni ?? ""; // fix identificationValue
        this.email = foundTutor?.email ?? "";
        this.telephone = foundTutor?.telephone ?? "";
        this.cv = foundTutor?.cv ?? null;
        this.company = foundTutor?.company ?? null;
      },
    },
    identification: {
      get() {
        return (
          this.tutor?.identification?.identification ??
          this.value.identification?.identification
        );
      },
      set(newValue) {
        if (this.tutor.identification.identification === newValue) return;
        this.tutor.identification.identification = newValue;
      },
    },
    identificationValue: {
      get() {
        return (
          this.tutor?.identificationValue ?? this.value.identificationValue
        );
      },
      set(newValue) {
        if (this.tutor.identificationValue === newValue) return;
        this.tutor.identificationValue = newValue;
      },
    },

    company: {
      get() {
        return this.tutor?.company ?? this.value.company;
      },
      set(newValue) {
        if (this.tutor.company === newValue) return;
        this.tutor.company = newValue;
      },
    },

    companyValue: {
      get() {
        return this.tutor?.companyValue ?? this.value.companyValue;
      },
      set(newValue) {
        if (this.tutor.companyValue === newValue) return;
        this.tutor.companyValue = newValue;
      },
    },

    name: {
      get() {
        return this.tutor?.name ?? this.value.name;
      },
      set(newValue) {
        if (this.tutor.name === newValue) return;
        this.tutor.name = newValue;
      },
    },
    email: {
      get() {
        return this.tutor?.email ?? this.value.email;
      },
      set(newValue) {
        this.tutor.email = newValue;
      },
    },
    telephone: {
      get() {
        return this.tutor?.telephone ?? this.value.telephone;
      },
      set(newValue) {
        let isNum = /^\d+$/.test(newValue);
        if (!isNum && newValue.length > 0)
          this.tutor.telephone = newValue.replace(/\D/g, "");
        else this.tutor.telephone = newValue;
      },
    },
    cv: {
      get() {
        return this.tutor?.cv ?? this.value.cv;
      },
      set(newValue) {
        this.tutor.cv = newValue;
      },
    },
    tutoringTime: {
      get() {
        return this.tutor?.tutoringTime ?? this.value.tutoringTime;
      },
      set(newValue) {
        this.tutor.tutoringTime = newValue;
      },
    },

    nameIdentificationMain() {
      return this.mainIdentification?.name || "DNI";
    },

    froalaConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 200,
        pluginsEnabled: ["align", "lists", "paragraphStyle", "paragraphFormat"],
        pastePlain: true
      };
    },
  },
  
  watch: {
    tutorSelected(newVal) {
      this.tutorId = newVal.id
    },
    showForm() {
      if (this.tutorSelected.id === this.value?.tutorId) return;
      const foundTutor = this.tutors.find((t) => t.id === this.value?.tutorId);
      this.tutorSelected = { ...(foundTutor || {}) }
    }
  },

  mounted() {
    let closeBtn = document.getElementById(
        `user-group-${this.groupId}-tutor-modal_close`
    );
    const self = this;
    if (closeBtn) {
      closeBtn.onclick = function () {
        const editForm = document.getElementById(`user-group-${this.groupId}-tutor-modal-form`);
        if (editForm) editForm.reset();
        self.tutor = {
          tutorId: -1,
          name: null,
          email: null,
          telephone: null,
          cv: null,
          tutoringTime: null,
          identificationValue: null,
          identification: {
            identification: null,
            value: null,
          },
          companyValue: "",
          company: null,
        };
        self.$emit('close');
        // let the event continue
      };
    }
  },

  methods: {
    identificationKeyDown(e) {
      const isValid = /^[a-zA-Z0-9\-_]*$/g.test(e.key);
      if (!isValid)e.preventDefault();
    },
    invalidInput(event) {
      if (event.target.validity.valueMissing) {
        event.target.setCustomValidity(this.$t('REQUIRED_FIELD') + '');
      }
    },
    tutorSelectChange(tutorId) {
      this.tutorId = tutorId;
    },

    showNewForm() {
      this.tutorBck = this.tutor;
      this.newTutor = true;
    },

    hideNewForm() {
      this.newTutor = false;
      if (this.tutorBck) {
        this.tutor = this.tutorBck;
        this.tutorBck = null;
      }
    },

    async saveTutorInfo() {
      try {
        this.errors.cv = null;
        this.savingTutor = true;
        const form =
          document.forms[`user-group-${this.groupId}-tutor-modal-form`];
        const formData = new FormData(form);
        formData.append("id", this.groupId);
        formData.append("tutoringTime", this.tutoringTime ?? "");
        formData.append("identification", JSON.stringify(this.identification));
        formData.append("company", JSON.stringify(this.company));
        if (this.cv == null && this.isSubsidized) {
          this.$toast.error(
            this.$t("ANNOUNCEMENT.FORM.ENTITY.GROUP.TUTOR_CV_REQUIRED") + ""
          );
          return;
        }

        if (this.cv instanceof File) formData.append("cv", this.cv);
        else formData.append("cv", JSON.stringify(this.cv));

        const { data, error } = await this.$store.dispatch(
          "announcementFormModule/saveGroupTutorInfo",
          formData
        );
        if (error) {
          if ((typeof data  === 'string' || data instanceof String) && data.startsWith('{"')) {
            // is json
            let errors = JSON.parse(data);
            Object.keys(errors).forEach(k => {
              let params = [];
              if (k === 'cv') params.push('PDF');
              this.errors[k] = this.$t(errors[k], params);
            })
          } else {
            this.$toast.error(this.$t(data) + '');
          }
        }
        else {
          // this.applyTutorChanges();
          this.tutor = data;
          // this.$emit('input', this.tutor);
          this.emitNewData(this.tutor);
          document
            .getElementById(`user-group-${this.groupId}-tutor-modal_close`)
            .click();
        }
      } finally {
        this.savingTutor = false;
      }
    },

    emitNewData(data) {
      this.newTutor = false;
      this.$emit("input", data);
      this.tutorBck = null;
      this.tutor = {
        tutorId: -1,
        name: null,
        email: null,
        telephone: null,
        cv: null,
        tutoringTime: null,
        identificationValue: null,
        identification: {
          identification: null,
          value: null,
        },
        companyValue: "",
        company: null,
      };
    },
  },
};
</script>

<template>
  <div class="TutorModal">
    <BaseModal
      :identifier="`user-group-${groupId}-tutor-modal`"
      :title="$t('ANNOUNCEMENT.MODALS.TUTOR_PROFILE')"
    >
      <template>
        <div class="col-12 d-flex justify-content-end">
          <button
            type="button"
            class="btn btn-secondary"
            @click="hideNewForm()"
            v-if="newTutor"
          >
            {{ $t("CANCEL") }}
          </button>
          <button
            type="button"
            class="btn btn-primary"
            @click="showNewForm()"
            v-else
          >
            {{ $t("NEW") }}
          </button>
        </div>
        <div
          v-if="savingTutor"
          class="d-flex align-items-center justify-content-center"
        >
          <spinner />
        </div>

        <new-user-tutor
          v-if="newTutor && showForm"
          :group-id="groupId"
          :type-identifications="typeIdentifications"
          :main-identification="mainIdentification"
          :companies="companies"
          @input="emitNewData"
        />
        <form
          v-else-if="showForm"
          action=""
          :id="`user-group-${groupId}-tutor-modal-form`"
          class="TutorSelection"
          @submit.prevent="saveTutorInfo()"
          v-show="!savingTutor"
        >
          <div class="w-100 d-flex">
            <div class="fa fa-tutor ml-auto mr-auto"></div>
          </div>
          <div class="form-group required">
            <label
              ><i class="fa fa-user mr-3"></i
              >{{ $t("ANNOUNCEMENT.FORM.GROUP.ENTITY.SELECT_TUTOR") }}</label
            >
            <Multiselect
              v-model="tutorSelected"
              :options="tutors"
              :show-labels="false"
              :placeholder="$t('ANNOUNCEMENT.ASSISTANCE_TUTOR')"
              track-by="id"
              label="name"
            />
            <input type="hidden" name="tutor-id" v-model="tutorId">
          </div>
          <div class="row form-group">
            <label class="w-100 pl-2" v-if="companies && companies.length > 0">
              <i class="fa fa-building card mr-3"></i
              >{{ $t("USER.USER_FIELDS_FUNDAE.USER_COMPANY") }}</label
            >
            <div class="col-12" v-if="companies && companies.length > 0">
              <Multiselect
                v-model="company"
                :options="companies"
                :allow-empty="false"
                :searchable="true"
                track-by="id"
                label="name"
                :placeholder="$t('MULTISELECT.PLACEHOLDER')"
                :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
              ></Multiselect>
            </div>
            <div class="col-12">
              <label
                ><i class="fa fa-address-card mr-3"></i>
                {{ nameIdentificationMain }}</label
              >
              <input
                type="text"
                class="form-control"
                name="identificationValue"
                @keydown="identificationKeyDown"
                :maxlength="20"
                v-model="identificationValue"
              />
            </div>
          </div>
          <div class="form-group required">
            <label
              ><i class="fa fa-envelope mr-3"></i>{{ $t("USER.EMAIL") }}</label
            >
            <input
              required
              type="email"
              class="form-control"
              v-model="email"
              name="email"
              @invalid="invalidInput"
            />
          </div>
          <div class="form-group" :class="isSubsidized ? 'required' : ''">
            <label
              ><i class="fa fa-mobile mr-3"></i> {{ $t("TELEPHONE") }}</label
            >
            <input
              type="text"
              class="form-control"
              v-model="telephone"
              name="telephone"
              :required="isSubsidized"
            />
          </div>
          <file-as-text
            :id="`modal-tutor-file-${groupId}`"
            :name="`modal-tutor-file-${groupId}`"
            label="Curriculum"
            v-model="cv"
            :error-msg="errors.cv"
          >
            <template v-slot:label-left>
              <i class="fa fa-file mr-3"></i>
            </template>
          </file-as-text>

          <div class="form-group col-12">
            <label class="w-100">
              <i class="fa fa-clock mr-3"></i
              >{{ $t("ANNOUNCEMENT.FORM.GROUP.ENTITY.TUTORING_TIME") }}</label
            >
            <froala
              tag="textarea"
              v-model="tutoringTime"
              :config="froalaConfig"
            ></froala>
          </div>

          <div class="d-flex w-100 mt-3 justify-content-end">
            <button type="submit" class="btn btn-primary">
              {{ $t("ANNOUNCEMENT.TUTOR.SAVE") }}
            </button>
          </div>
        </form>
      </template>
    </BaseModal>
  </div>
</template>

<style scoped lang="scss">
.TutorSelection {
  padding: 1rem;

  @media #{min-small-screen()} {
    padding: 1rem 5rem;
  }

  .form-group {
    padding: 0.15rem 0;
  }

  .avatar {
    @include avatar;
    width: 100px;
    height: 100px;
    margin-bottom: 2rem;
  }
}
</style>
