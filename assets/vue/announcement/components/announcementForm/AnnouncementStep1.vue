<template>
  <div class="AnnouncementStep1">
    <div class="col-12 p-0" v-if="edit">
      <input type="text" class="form-control" :placeholder="$t('ANNOUNCEMENT.FIND_COURSE')" v-model="courseQuery" @keydown.enter="pagination.current = 1;findCourses()">
      <input id="course" name="course" type="hidden" :value="selectedAsString">
    </div>
    <div class="d-flex flex-column align-items-center justify-content-center" v-if="loadingCourses">
      <spinner />
    </div>
    <div class="col-12 AnnouncementStep1--courses" v-else>
      <div class="AnnouncementStep1--courses--course" :class="selectedCourse?.id === course.id ? 'selected' : ''"
           v-for="course in pagedCourses"
           :key="course.id"
           @click="selectCourse(course)"
      >
        <img :src="course.image ? `uploads/images/course/${course.image}` : defaultImage" alt="" @error="$event.target.src = defaultImage">
        <p>
          <span>{{ course.code }} - {{ course.name }}</span>
        </p>
      </div>
    </div>
    <div class="col-12">
      <pagination
          :page-size="10"
          :items="courses"
          :prop-current-page="pagination.current"
          @items-page="onCurrentPage"
      />
    </div>
  </div>
</template>

<script>
import {get} from "vuex-pathify";
import Loader from "../../../admin/components/Loader.vue";
import Pagination from "../../../admin/components/Pagination.vue";
import Spinner from "../../../admin/components/base/Spinner.vue";

export default {
  name: "AnnouncementStep1",
  components: {Spinner, Pagination, Loader},
  props: {
    announcement: null,
    edit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      courseQuery: '',

      loadingCourses: false,
      courses: [],
      pagedCourses: [],
      selectedCourse: this.announcement?.selectedCourse ?? null,
      pagination: {
        current: 1
      }
    };
  },
  computed: {
    selectedAsString() {
      return JSON.stringify(this.selectedCourse);
    },
    defaultImage: get('configModule/defaultImageB64'),
  },
  watch: {
    announcement() {
      this.selectedCourse = this.announcement?.selectedCourse??null;
    }
  },
  created() {
    this.init();
  },
  methods: {
    onCurrentPage(items) {
      this.pagedCourses = items;
      // this.selectCourse(null)
      // this.pagination.current = page;
      // this.findCourses();
    },
    init() {
      if (!this.edit) {
        this.courses.push(this.announcement.selectedCourse);
        return;
      }

      const params = this.$route.params;
      if (params.origin && params.origin === "course") {
        this.loadPreSelectedCourse(params.id);
      } else {
        this.findCourses();
      }
    },

    findCourses() {
      this.loadingCourses = true;
      this.$store.dispatch('announcementModule/findCourses', { page: this.pagination.current, query: this.courseQuery }).then(res => {
        const { data, error } = res;
        this.courses = data.items;
        this.pagination.totalItems = data['total-items'];
      }).finally(() => {
        this.loadingCourses = false;
      });
    },

    loadPreSelectedCourse(courseId) {
      this.loadingCourses = true;
      this.$store.dispatch('announcementModule/loadPreSelectedCourse', courseId).then(res => {
        const { error, data } = res;
        this.courses.push(data);
        this.selectCourse(this.courses[0]);
      }).finally(() => {
        this.loadingCourses = false;
      });
    },
    selectCourse(course) {
      if (!this.edit) return;
      this.selectedCourse = course;
      this.$emit('selected', course);
    }
  }
}
</script>

 <style scoped lang="scss"> 
.AnnouncementStep1 {

  width: 100%;
  background-color: #FFFFFF;
  padding: 1rem 0.25rem;

  &--courses {
    width: 100%;
    display: flex;
    flex-flow: row wrap;
    row-gap: 1rem;
    padding: 1rem 0;
    overflow-y: auto;

    @media #{small-screen()} {
      display: grid;
      grid-template-columns: repeat(auto-fit, calc((100% - 1rem) / 2));
      gap: 1rem;
    }

    @media #{min-medium-screen()} {
      display: grid;
      grid-template-columns: repeat(auto-fit, calc((100% - 3rem) / 4));
      gap: 1rem;
    }

    &--course {
      position: relative;
      background-size: cover;
      background-position: center center;
      cursor: pointer;
      display: flex;
      color: #ffffff;
      width: 100%;
      height: 150px;

      img {
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
      }

      p {
        z-index: 5;
        font-size: 17px;
        color: #FFFFFF;
        width: 100%;
        overflow: hidden;
        text-shadow: 1px 1px 3px #000000;
        background: linear-gradient(0deg, rgba(2,0,36,0.3) 0%, rgba(129,129,129,0.3) 32%, rgba(129,129,129,0.3) 69%, rgba(4,4,4,0.3) 100%);
        height: 100%;
        display: flex;
        padding: 0.3rem;

        span {
          margin-top: auto;
          width: 100%;
          text-align: left;
        }
      }
      transition: all .5s ease-in;

      &:not(.selected):hover {
        transform: scale(1.05);
      }

      &:after {
        content: "";
        height: 2px;
        width: 0;
        position: absolute;
        bottom: 0;
        left: 0;
        background-color: $base-border-color-active;
      }

      &.selected {
        border: 5px solid $base-border-color-active;
      }

      span {
        text-align: center;
      }
    }
  }
}
</style>
