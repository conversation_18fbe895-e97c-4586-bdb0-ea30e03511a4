<template>
  <div class="ClientDefaultFields d-flex flex-row flex-wrap">
    <div class="form-group col-md-4">
      <label for="sGroup">{{ $t('ANNOUNCEMENT.DEFAULT.S_GROUP') }}</label>
      <input name="sGroup" id="sGroup" type="text" class="form-control" v-model="data.sGroup"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.DEFAULT.S_GROUP')">
    </div>
    <div class="form-group col-md-4">
      <label for="actionDenomination">{{ $t('ANNOUNCEMENT.DEFAULT.ACTION_DENOMINATION') }}</label>
      <input name="actionDenomination" id="actionDenomination" type="text" class="form-control" v-model="data.actionDenomination"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.DEFAULT.ACTION_DENOMINATION')">
    </div>
    <div class="form-group col-md-4">
      <label for="modality">{{ $t('ANNOUNCEMENT.DEFAULT.MODALITY') }}</label>
      <input name="modality" id="modality" type="text" class="form-control" v-model="data.modality"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.DEFAULT.MODALITY')">
    </div>
    <div class="form-group col-md-4">
      <label for="placeOfInstruction">{{ $t('ANNOUNCEMENT.DEFAULT.PLACE_OF_INSTRUCTION') }}</label>
      <input name="placeOfInstruction" id="placeOfInstruction" type="text" class="form-control" v-model="data.placeOfInstruction"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.DEFAULT.PLACE_OF_INSTRUCTION')">
    </div>
    <div class="form-group col-md-4">
      <label for="collaborationType">{{ $t('ANNOUNCEMENT.DEFAULT.COLLABORATION_TYPE') }}</label>
      <input name="collaborationType" id="collaborationType" type="text" class="form-control" v-model="data.collaborationType"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.DEFAULT.COLLABORATION_TYPE')">
    </div>
    <div class="form-group col-md-4">
      <label for="provider">{{ $t('ANNOUNCEMENT.PROVEEDOR') }}</label>
      <input name="provider" id="provider" type="text" class="form-control" v-model="data.provider"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.DEFAULT.PROVEEDOR')">
    </div>
    <div class="form-group col-md-4">
      <label for="providerCif">{{ $t('ANNOUNCEMENT.CIF_PROVEEDOR') }}</label>
      <input name="providerCif" id="providerCif" type="text" class="form-control" v-model="data.providerCif"
             :placeholder="$t('ANNOUNCEMENT.PLACEHOLDER.DEFAULT.CIF_PROVEEDOR')">
    </div>
  </div>
</template>

<script>
export default {
  name: "ClientDefaultFields",
  props: {
    announcement: null
  },
  data() {
    return {
      data: {
        sGroup: this.announcement?.sGroup ?? '',
        actionDenomination: this.announcement?.actionDenomination ?? '',
        modality: this.announcement?.modality ?? '',
        placeOfInstruction: this.announcement?.placeOfInstruction ?? '',
        collaborationType: this.announcement?.collaborationType ?? '',
        provider: this.announcement?.provider ?? '',
        providerCif: this.announcement?.providerCif ?? '',
      },
    };
  },
  watch: {
    data: {
      handler: function (old, newVal) {
        this.$emit('updated', this.data);
      },
      deep: true
    }
  }
}
</script>

 <style scoped lang="scss"> 

</style>
