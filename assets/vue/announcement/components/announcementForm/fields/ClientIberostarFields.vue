<template>
  <div class="ClientIberostarFields d-flex flex-row flex-wrap">
    <div class="form-group col-md-4">
      <label for="hotel">{{ $t('ANNOUNCEMENT.IBEROSTAR.HOTEL') }}</label>
      <input name="hotel" id="hotel" type="text" class="form-control" v-model="hotel">
    </div>
    <div class="form-group col-md-4">
      <label for="society">{{ $t('ANNOUNCEMENT.IBEROSTAR.SOCIETY') }}</label>
      <input name="society" id="society" type="text" class="form-control" v-model="society">
    </div>
    <div class="form-group col-md-4">
      <label for="subsidizedGroup">{{ $t('ANNOUNCEMENT.IBEROSTAR.SUBSIDIZED_GROUP') }}</label>
      <input name="subsidizedGroup" id="subsidizedGroup" type="text" class="form-control" v-model="subsidizedGroup">
    </div>
    <div class="form-group col-md-4">
      <label for="department">{{ $t('ANNOUNCEMENT.IBEROSTAR.DEPARTMENT') }}</label>
      <input name="department" id="department" type="text" class="form-control" v-model="department">
    </div>
    <div class="form-group col-md-4">
      <label for="clientContactPerson">{{ $t('ANNOUNCEMENT.IBEROSTAR.CLIENT_CONTACT_PERSON') }}</label>
      <input name="clientContactPerson" id="clientContactPerson" type="text" class="form-control" v-model="clientContactPerson">
    </div>
  </div>
</template>

<script>
export default {
  name: "ClientIberostarFields",
  props: {
    announcement: null
  },
  data() {
    return {
      hotel: this.announcement?.hotel ?? '',
      society: this.announcement?.society ?? '',
      subsidizedGroup: this.announcement?.subsidizedGroup ?? '',
      department: this.announcement?.department ?? '',
      clientContactPerson: this.announcement?.clientContactPerson ?? '',
    };
  },
  watch: {
    data: {
      handler: function (old, newVal) {
        console.log('handleruPDATED');
        this.$emit('updated', this.data);
      },
      deep: true
    }
  }
}
</script>

 <style scoped lang="scss"> 

</style>
