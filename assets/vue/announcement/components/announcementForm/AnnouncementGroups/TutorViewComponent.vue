<template>
  <div class="TutorViewComponent">
    <div class="TutorViewComponent--header">
      <div class="fa fa-tutor"></div>
      <h1>{{ tutor?.name }}</h1>
    </div>
    <div class="TutorViewComponent--profile">
      <table>
        <tr>
          <th scope="row">
            <i class="fa fa-address-card mr-3"></i
            >{{ mainIdentification.name ?? "DNI" }}:
          </th>
          <td>{{ tutor?.identificationValue }}</td>
        </tr>
        <tr>
          <th scope="row">
            <i class="fa fa-envelope mr-3"></i>{{ $t("USER.EMAIL") }}:
          </th>
          <td>
            <a :href="`mailto:${tutor?.email}`">{{ tutor?.email }}</a>
          </td>
        </tr>
        <tr
            v-if="
                    tutor?.telephone &&
                    tutor?.telephone?.length > 0
                  "
        >
          <th scope="row">
            <i class="fa fa-mobile mr-3"></i>{{ $t("TELEPHONE") }}:
          </th>
          <td>{{ tutor?.telephone }}</td>
        </tr>
        <tr v-if="tutor">
          <th scope="row">
            <i class="fa fa-mobile mr-3"></i>
            {{ $t("ANNOUNCEMENT.FORM.GROUP.PROFESSIONAL_PROFILE") }}:
          </th>
          <td>
            <a :href="tutor?.filename" target="_blank"
            >{{ $t("VIEW") }}/{{ $t("PRINT") }}</a
            >
          </td>
        </tr>
        <tr
            v-if="
                    tutor?.tutoringTime != null &&
                    tutor?.tutoringTime?.length > 0
                  "
        >
          <th scope="row" colspan="2">
            <i class="fa fa-clock mr-3"></i
            >{{ $t("ANNOUNCEMENT.FORM.GROUP.ENTITY.TUTORING_TIME") }}:
          </th>
        </tr>
        <tr v-if="
                    tutor?.tutoringTime != null &&
                    tutor?.tutoringTime?.length > 0
                  ">
          <td colspan="2">
            <div class="TutoringTimeContent" v-html="tutor?.tutoringTime"/>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import {get} from "vuex-pathify";

export default {
  name: "TutorViewComponent",
  props: {
    groupId: {
      type: Number | String,
      required: true
    },
    tutor: {
      type: Object | Array,
      default: null
    },
  },
  computed: {
    mainIdentification: get("announcementFormModule/mainIdentification"),
  }
}
</script>

<style scoped lang="scss">
.TutorViewComponent {
  padding: 2rem 3rem;
  background-color: var(--color-neutral-mid);

  &--header {
    display: flex;
    flex-flow: column;
    align-items: flex-start;
    justify-content: center;

    h1,
    h4 {
      color: var(--color-neutral-darkest);
      width: 100%;
      text-align: center;
      margin-top: 1rem;
    }

    h1 {
      font-size: 22px;
    }

    h4 {
      font-size: 18px;
      margin-top: unset;
    }
  }

  &--profile {
    border: 1px solid var(--color-neutral-mid-darker);
    border-radius: 10px;
    padding: 1rem 3rem;
    background-color: #ffffff;
    margin-top: 1rem;

    table {
      width: 100%;

      tr {
        padding-top: 1.25rem;
      }

      th {
        color: var(--color-neutral-darkest);
        font-weight: bold;
      }
    }
  }

  .TutoringTimeContent {
    border: 1px solid var(--color-neutral-mid-darker);
    padding: .5rem;
    border-radius: 5px;
  }
}
</style>
