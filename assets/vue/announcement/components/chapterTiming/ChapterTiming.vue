<script>
import BaseInputTime from "../../../base/BaseInputTime.vue";

export default {
  name: "ChapterTiming",
  components: {BaseInputTime},
  props: {
    startedAt: {
      type: Boolean,
      default: true
    },
    finishedAt: {
      type: Boolean,
      default: true
    },
    time: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => ({
        start: '',
        end: '',
        time: '00:01:00'
      })
    }
  },

  computed: {
    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  },
  methods: {
    setTime(time) {
      const v = structuredClone(this.innerValue);
      v.time = time;
      this.innerValue = v;
    }
  }
}
</script>

<template>
  <div class="ChapterTiming">
    <div class="ChapterTiming--header">
      <h1>{{ innerValue.title }}</h1>
    </div>
    <div class="ChapterTiming--body">
      <div class="form-group col-12" v-if="startedAt">
        <label for="">{{ $t('START_AT') }}</label>
        <input type="datetime-local" class="form-control" v-model="innerValue.start" :disabled="disabled">
      </div>
      <div class="form-group col-12" v-if="finishedAt">
        <label for="">{{ $t('FINISH_AT') }}</label>
        <input type="datetime-local" class="form-control" v-model="innerValue.end" :disabled="disabled">
      </div>
      <div class="form-group col-12" v-if="time && innerValue.type !== 'game'">
        <BaseInputTime :time="innerValue.time" @time-update="setTime" :disabled="disabled"/>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.ChapterTiming {
  width: 100%;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  &--header {
    width: 100%;
    background-color: #404b5c;
    padding: 0.5rem;

    h1 {
      color: #dedfe2;
      width: 100%;
      text-align: center;
      font-size: 18px;
    }
  }

  &--body {
    background-color: #e5e9ee;
    display: flex;
    flex-flow: row wrap;
    width: 100%;
    padding: 1rem;
  }
}
</style>
