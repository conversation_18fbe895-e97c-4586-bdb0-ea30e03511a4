<template>
  <div class="AddAnnouncementMaterial">
    <form-file-uploader
        id="form-upload-observation-files"
        :file-types="fileTypes"
        :uploading="uploading"
        :show-cancel="true"
        :show-submit="true"
        @on-type-selection="selectedType = $event"
        @cancel="$emit('cancel')"
        @upload="upload"
    >
    </form-file-uploader>
  </div>
</template>

<script>
import {get} from "vuex-pathify";

import FileSelector from "../../common/components/FileSelector.vue";
import FormFileUploader from "../../common/components/FormFileUploader.vue";
import Spinner from "../../admin/components/base/Spinner.vue";

/**
 * @event success
 * @event cancel
 */
export default {
  name: "AddAnnouncementMaterial",
  components: {FormFileUploader, Spinner, FileSelector},
  data() {
    return {
      uploadingFiles: false,
      uploading: false,
      selectedType: null
    };
  },
  computed: {
    fileTypes: get('materialCourseModule/allowedFileTypes'),
    announcement: get('announcementModule/announcement'),
  },
  methods: {
    upload(formData) {
      this.$alertify.confirmWithTitle(
          this.$t('FILE_UPLOAD.CONFIRM_UPLOAD.TITLE'),
          this.$t('FILE_UPLOAD.CONFIRM_UPLOAD.DESCRIPTION'),
          () => {
            this.uploadingFiles = true;
            this.$store.dispatch('announcementModule/uploadAnnouncementMaterials', { id: this.announcement.id, formData }).then(res => {
              const { error, data } = res;
              if (error) {
                this.$toast.error(this.$t('FILE_UPLOAD.UPLOAD.FAILED') + '');
              } else {
                this.$toast.success(this.$t('FILE_UPLOAD.UPLOAD.SUCCESS') + '');
                this.$emit('success');
              }
            }).finally(() => {
              this.uploadingFiles = false;
            })
          },
          () => {}
      );
    }
  }
}
</script>

 <style scoped lang="scss"> 
.AddAnnouncementMaterial {
  &--type {
    @include boxed-selector;

    button.selector {
      width: 80px !important;
      height: 80px !important;
    }
  }

  &--file {
    width: 100%;
    form {
      width: 100%;
      @media #{min-small-screen()} {
        display: grid;
        grid-template-columns: 400px auto;
      }
    }
  }
}
</style>
