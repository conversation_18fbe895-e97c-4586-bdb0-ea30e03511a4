<template>
  <div class="home">
    <home
      title="COMPANY.HOME.TITLE"
      description="COMPANY.HOME.DESCRIPTION"
    >
      <template v-slot:content-actions>
        <router-link class="btn btn-primary mr-auto" :to="{ name: 'CompanyCreate' }">{{ $t('COMPANY.CREATE_COMPANY') }}</router-link>
      </template>
      <template v-slot:content-main>
        <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
          <spinner />
        </div>    

        <table class="table table-condensed mt-3" v-else>
          <thead>
          <tr>
            <th></th>
            <th>{{ $t('NAME') }}</th>
            <th>{{ $t('DESCRIPTION') }}</th>
            <th>{{ $t('COMPANY.STATE') }}</th> 
            <th></th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(c, index) in companys" :key="c.id">
            <td></td>
            <td>{{ c.name }}</td>
            <td>{{ c.description }}</td>
            <td>
              <BaseSwitch :tag="`switcher-company-${c.id}`" v-model="c.state" @change="changeActiveStatus(index)" />
            </td>
            <td>
              <div class="dropdown">
                <button class="btn btn-default" type="button" :id="`dropdown-menu-${c.id}`" data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="fa fa-ellipsis-h"></i>
                </button>
                <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${c.id}`">
                  <li><router-link class="dropdown-item" :to="{ name: 'CompanyUpdate', params: {...$route.params, id: c.id} }">{{ $t('EDIT') }}</router-link></li>
                  <li @click="deleteCompany(index)" style="background-color: red; color: white; padding-left: 6px; cursor: pointer">{{ $t('COMMON.CLEAR') }}</li>
                </ul>
              </div>
            </td>
          </tr>
          </tbody>
        </table>
      </template>
    </home>
  </div>

</template>

<script>
import Spinner from "../../admin/components/base/Spinner.vue";
import Home from "../../base/Home.vue";
import {get, sync} from "vuex-pathify";
import JwPagination from 'jw-vue-pagination';
import Multiselect from "vue-multiselect";

const PAGE_SIZE = 10;
const customLabels = {
    first: '<<',
    last: '>>',
    previous: '<',
    next: '>'
};

export default {
  name: "CompanyGroup",
  components: { Spinner, JwPagination, Multiselect, Home},    
  data() {
    return {
      customLabels,
      defaultValue: '',
      companys:[],
      allCompanys:[],
      btnFilter: true,
      btnClearFilter: false,
    };
  },
  computed: {
    ...get("companyModule", ["loading", "getCompanies"]),

    companies() {
      return this.getCompanies();
    },
  },
  async created() { 
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: this.$t('COMPANY.TITLE'),
        params: {}
      }
    });

    await this.$store.dispatch('companyModule/load', '/admin/company/all');
    this.companys = this.allCompanys = this.companies;
    console.log(this.companys);
  },
  methods: {    
    changeActiveStatus(index) {
      const company = this.companys[index];
      this.$store.dispatch('companyModule/changeActiveState',
          {endpoint: `/admin/company/${company.id}/state`, requestData: {id: company.id}}).then(r => {

      });
    },
    filterActive(){
      this.btnFilter = !this.btnFilter;
    },
    clearfilter(){
      this.companys = this.allCompanys;
      this.btnClearFilter = false;
      this.defaultValue= '';
    },
    selectFilter(){
      let filterSettings=[];

      this.allCompanys.forEach((item) => {
        if(item.name === this.defaultValue) filterSettings.push(item);      
      });

      this.companys = filterSettings;     
      this.filterActive();
      this.btnClearFilter = true;
    },
    onChangePage(companys) {
        this.companys = companys;
    },
    deleteCompany(index){
      const company = this.companys[index];
      this.$alertify.confirmWithTitle(
          this.$t("DELETE"),
          this.$t("COMMON_AREAS.QUESTION_DELETE"),
          () => {
            this.$store
                .dispatch("companyModule/deleteCompany", {
                  endpoint: `/admin/company/${company.id}/delete`,
                })
                .then((r) => {
                  this.$toast.success(this.$t("DELETE_SUCCESS") + "");
                })
                .catch((e) => {
                  this.$toast.error("DELETE_FAILED");
                })
                .finally(() => {
                  location.reload()
                });
          },
          () => {}
      );
    }
  }
}
</script>
<style scoped lang="scss">

</style>
