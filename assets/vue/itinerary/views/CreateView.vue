<template>
  <div
    class="d-flex w-100 align-items-center justify-content-center"
    v-if="loading"
  >
    <spinner />
  </div>
  <div class="FormView m-5" v-else>
    <div class="row align-items-start">
      <div class="col-2 pe-3 border-right"></div>

      <Translation
        v-model="locale"
        :warning="warningLocales"
        v-for="t in translations"
        v-if="t.locale === locale"
      >
        <template v-slot:content>
          <div class="form-group col-12">
            <label
              >{{ $t("NAME") }}<span style="color: red">*</span></label
            >
            <input type="text" class="form-control" v-model="t.name" />
          </div>
          <div class="form-group col-12">
            <label>{{ $t("DESCRIPTION") }}</label>
            <froala
              tag="textarea"
              v-model="t.description"
              :config="froalaDescriptionConfig"
            ></froala>
          </div>
        </template>
      </Translation>
    </div>

    <div class="row mt-3">
      <div class="col-sm-6 col-xs-12">
        <div class="custom-control custom-switch">
          <input
              id="active"
              name="active"
              type="checkbox"
              class=" form-control custom-control-input"
              required
              v-model="formData.active"
          />
          <label class="custom-control-label" for="active">{{ $t("ACTIVE") }}</label>
        </div>
      </div>
      <div class="col-sm-6 col-xs-12">
        <Tags
            :title="$t('TAGS.ITINERARY')"
            :allow-enter-button="false"
            @tags-updated="formData.tags = $event"
            :prop-tags="formData.tags"
            auto-complete-url="/admin/itineraries/available-tags"
            class="h-100 align-content-start"
        ></Tags>

      </div>
    </div>

    <div class="col-12 mt-5 mb-3 text-center">
      <button class="btn btn-sm btn-primary mr-1" @click="submitForm()">{{ $t('SAVE') }}</button>
      <button class="btn btn-sm btn-primary ml-1" @click="submitForm(false)">{{ buttonSave2 }}</button>
    </div>
  </div>
</template>

<script>
import Tags  from "../../common/components/Tags.vue";
import {get} from "vuex-pathify";
import Spinner from "../../admin/components/base/Spinner.vue";
import Translation from "../../common/components/Translation.vue";
import translationWarningsMixin from '../../mixins/translationWarnings'


export default {
  name: "CreateView",
  components: {Tags,  Spinner, Translation},
  mixins: [translationWarningsMixin],
  data() {
    return {
      locale: 'es',
      formData: {
        id: 0,
        name: '',
        description: '',
        active: false,
        tags: [],
      },
      translations: [],
    }
  },
  computed: {
    contentTitle: get('configModule/config@contentTitle'),
    buttonSave2: get('configModule/config@buttonSave2'),
    locales: get("localeModule/locales"),
    defaultLocale: get("localeModule/defaultLocale"),
    preloadContent() {
      return this.$route.params || {}
    },
    ...get("itineraryModule", ["getTranslations", "loading"]),
    froalaDescriptionConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 150,
        pastePlain: true,
        pluginsEnabled: ["align","charCounter"],
        toolbarButtons: {
          'moreText': {
            buttons: ['bold', 'italic', 'underline']
          },
        },
      };
    },    
    translationsWithInfoToEdit() {
      return this.getTranslations();
    },
  },
  async created() {
    this.$store.dispatch('contentTitleModule/setContentTitle', [this.$t(this.contentTitle)]);
    if (this.preloadContent.id) {
      this.formData = {...this.preloadContent};
      let id= this.preloadContent.id;
      await this.$store.dispatch("itineraryModule/getItineraryTranslations", { endpoint: `/admin/itinerary/translation/${id}`}  );
    }
    
    this.locale = this.defaultLocale;
    this.translations = this.initTranslation(this.formData);
  },
  methods: {
    initTranslation(selectElement){
      let translations = [];
      let nameT = "";
      let descriptionT = "";
      const keys = Object.keys(this.locales);
      keys.forEach((k) => {
        const translated = this.translationsWithInfoToEdit.find((e) => e.locale === k);

        if(k === this.defaultLocale && !translated?.name){
          nameT = selectElement.name;
          descriptionT = selectElement.description;
        }else{
          nameT = "";
          descriptionT = "";
        }

        translations.push({
          locale: k,
          name: translated?.name ?? nameT,
          description: translated?.description ?? descriptionT,
        });
      });      

      return translations;
    },
    setWarningLocales(translations) {
      translations.forEach((t) => {
        const nameEmpty = !t.name || t.name.trim().length === 0;
        this.$set(this.warningLocales, t.locale, nameEmpty) ;
      });
    },
    getTranslationName() {
      let translationName = this.translations.filter(
        (translation) => translation.locale === this.defaultLocale
      );
      return translationName[0].name;
    },
    getTranslationDescription() {
      let translationName = this.translations.filter(
        (translation) => translation.locale === this.defaultLocale
      );
      return translationName[0].description;
    },
    prepareData() {
      const formData = new FormData();
      formData.append("id", this.formData.id);
      formData.append("name", this.getTranslationName());
      formData.append("description", this.getTranslationDescription());
      formData.append("active", this.formData.active);
      formData.append("tags", JSON.stringify(this.formData.tags.map((tag) => tag.id)));
      formData.append("translations", JSON.stringify(this.translations));
      return formData;
    },
    submitForm(goToDetails = true) {
      this.$toast.info(`${this.$t('SAVING')}`);
      const postAction = goToDetails ? 'detail' : 'createItineraryForm';

      this.$store.dispatch('itineraryModule/saveItinerary', this.prepareData()).then((response) => {
        this.$toast.clear();
        this.$toast.success(`${this.$t('ITINERARY.CONTENT.SAVE.SUCCESS')}`);
        this.$store.dispatch('itineraryModule/getUrl', {id: response.data.id, action: postAction});
      }).catch((axiosResponse) => {
        this.$toast.clear();
        this.$toast.error(`${this.$t('ITINERARY.CONTENT.SAVE.FAILED')} [${axiosResponse.response.data.data}]`);
      });
    }
  }
}
</script>

 <style scoped lang="scss"> 
.CreateView {
  padding: 0 1rem;

  .Tags {
    padding: 0.75rem 2rem;
    gap: 0.75rem;
  }
}
</style>
