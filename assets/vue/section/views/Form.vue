<template>
  <div
    class="d-flex w-100 align-items-center justify-content-center"
    v-if="loading"
  >
    <spinner />
  </div>
  <div class="FormView" v-else>
    <div class="w-100 d-flex flex-row Header">
      <h4>{{ $t("COURSE.COURSE_SECTION.LABEL.DETAIL") }}</h4>
    </div>
    <div class="w-100">
      <ul class="nav nav-tabs ps-4 user-select-none hideOnPrint">
        <li class="nav-item" role="presentation" aria-selected="true">
          <button
            class="nav-link active"
            id="divFicha-tab"
            data-bs-toggle="tab"
            data-bs-target="#divFicha"
            type="button"
            role="tab"
            aria-controls="divFicha"
            aria-selected="true"
          >
            <i class="fa fa-address-card"></i>
            {{ $t("COURSE.COURSE_SECTION.LABEL.DATA_SHEET") }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            id="divCategories-tab"
            data-bs-toggle="tab"
            data-bs-target="#divCategories"
            type="button"
            role="tab"
            aria-controls="divCategories"
            aria-selected="false"
          >
            <i class="fa fa-cogs"></i>
            {{ $t("COURSE.COURSE_SECTION.LABEL.CATEGORIES") }}
          </button>
        </li>
      </ul>
    </div>
    <div class="tab-content bg-white p-0 pt-1 pb-1">
      <div
        class="tab-pane fade show active"
        id="divFicha"
        role="tabpanel"
        aria-labelledby="divFicha-tab"
      >
        <div class="form-step basic-info" :class="currentStep === 'basic-info'">
          <h5 class="CurrentStepSectionTitle">
            <span><i class="fa fa-folder" aria-hidden="true"></i></span>
            {{ $t("COURSE.COURSE_SECTION.LABEL.BASIC_INFORMATION") }}
          </h5>
          <div
            class="basic-info--translation"
            :class="currentStep === 'translation'"
            v-show="numberLanguages > 1"
          >
            <div class="row align-items-start">
              <div class="col-2 pe-3 border-right"></div>

              <Translation
                v-model="locale"
                :warning="warningLocales"
                v-for="t in CourseSection.translations"
                v-if="t.locale === locale"
              >
                <template v-slot:content>
                  <div class="form-group col-12">
                    <label
                      >{{ $t("NAME") }}<span style="color: red">*</span></label
                    >
                    <input type="text" class="form-control" v-model="t.name" />
                  </div>
                  <div class="form-group col-12">
                    <label>{{ $t("DESCRIPTION") }}</label>
                    <froala
                      tag="textarea"
                      v-model="t.description"
                      :config="froalaDescriptionConfig"
                    ></froala>
                  </div>
                </template>
              </Translation>
            </div>
          </div>
          <div class="row align-items-start" v-show="numberLanguages == 1">
            <div class="form-group col-12">
              <label>{{ $t("NAME") }}<span style="color: red">*</span></label>
              <input
                type="text"
                class="form-control"
                v-model="CourseSection.name"
              />
            </div>

            <div class="form-group col-12">
              <label>{{ $t("DESCRIPTION") }}</label>
              <froala
                tag="textarea"
                v-model="CourseSection.description"
                :config="froalaDescriptionConfig"
              ></froala>
            </div>
          </div>
          <div
            class="form-step basic-info"
            :class="currentStep === 'basic-info'"
            v-show="currentStep === 'basic-info'"
          >
            <div
              class="form-group col-12 d-flex align-items-center justify-content-start m-2"
            >
              <BaseSwitch
                :tag="`switcher-hideCategoryName-${CourseSection.id}`"
                v-model="CourseSection.hideCategoryName"
              />
              <label class="ml-2">{{
                $t("COURSE.COURSE_SECTION.HIDECATEGORYNAME")
              }}</label>
            </div>

            <div class="StepForm--form--content">
              <div class="form-group">
                <label-with-info
                  class="CurrentStepSectionTitle"
                  :info="$t('COURSE.COURSE_SECTION.SECTIONS_INFO') + ''"
                  id="initial-info"
                  location="top"
                >
                  {{ $t("COURSE.COURSE_SECTION.SECTIONS") }}
                </label-with-info>
              </div>
              <table class="table table-condensed mt-3">
                <thead>
                  <tr>
                    <th>{{ $t("COURSE.COURSE_SECTION.SORT") }}</th>
                    <th>
                      {{ $t("NAME") }}
                    </th>
                    <th>{{ $t("COURSE.COURSE_SECTION.ACTIVATE") }}</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(subtype, index) in subtypesx"
                    :key="subtype.id"
                    draggable
                    @dragstart="startDrag($event, index)"
                    @drop="onDrop(index)"
                    @dragover.prevent
                    @dragenter.prevent
                  >
                    <td>
                      <i
                        class="fas fa-grip-lines"
                        aria-hidden="true"
                        style="color: #009bdb"
                      ></i>
                    </td>
                    <td>
                      {{ $t("COURSE.COURSE_SECTION.".concat(subtype.subtype)) }}
                    </td>
                    <td>
                      <BaseSwitch
                        :tag="`switcher-subtypes-${subtype.id}`"
                        v-model="subtype.active"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <div
        class="tab-pane fade"
        id="divCategories"
        role="tabpanel"
        aria-labelledby="divCategories-tab"
      >
        <div class="col-12 d-flex justify-content-start ms-2">
          <div class="p-3 m-2" :style="styleManualSelection">
            <i class="fa fa-cog" style="font-size: 30px; color: white"></i>
          </div>
          <div>
            <div
              class="col-12 d-flex align-items-center justify-content-start ms-2"
            >
              <BaseSwitch
                :tag="`switcher-isManualSelection-${CourseSection.id}`"
                v-model="CourseSection.isManualSelection"
                @change="manualSelection"
              />
              <label class="ms-2"
                ><b>
                  {{ $t("COURSE.COURSE_SECTION.ISMANUALSELECTION_LABEL") }}
                </b>
              </label>
            </div>
            <div class="ml-3">
              <label>
                {{ $t("COURSE.COURSE_SECTION.ISMANUALSELECTION_LABEL2") }}
              </label>
            </div>
          </div>
        </div>

        <div
          v-if="CourseSection.isManualSelection"
          class="basic-info--categoriesLinked"
          :class="currentStep === 'categoriesLinked'"
        >
          <div class="form-group col-12 mt-3 audience">
            <add-remove
              :source-items="categories"
              :realtime="false"
              :title="$t('COURSE.COURSE_CATEGORY.CATEGORIES') + ''"
              :enable-all="false"
              v-model="CourseSection.sectionCategories"
              :loading-source="false"
              :loading-selected="false"
              :fields="{ id: 'id', name: 'name' }"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="col-12 d-flex flex-row align-items-end justify-content-end">
      <div v-show="showCreateAt" class="mt-2">
        {{
          $t("COURSE.COURSE_CATEGORY.CREATEBY")
            .concat(createdBy, " ")
            .concat($t("COURSE.COURSE_CATEGORY.CONNECTOR").concat(createdAt))
        }}
      </div>
    </div>
    <div class="col-12 d-flex flex-row align-items-end justify-content-end">
      <div v-show="showUpdateAt">
        {{
          $t("COURSE.COURSE_CATEGORY.UPDATEBY")
            .concat(UpdateBy, " ")
            .concat($t("COURSE.COURSE_CATEGORY.CONNECTOR").concat(UpdateAt))
        }}
      </div>
    </div>
  </div>
</template>

<script>
import BaseForm from "../BaseForm.vue";
import { get } from "vuex-pathify";
import BaseSwitch from "../../base/BaseSwitch.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import AddRemove from "../../common/components/select/AddRemove.vue";
import StepForm from "../../common/views/StepForm.vue";
import Translation from "../../common/components/Translation.vue";
import LabelWithInfo from "../../common/components/ui/LabelWithInfo.vue";

export default {
  name: "Form",
  components: {
    BaseSwitch,
    BaseForm,
    StepForm,
    AddRemove,
    Spinner,
    Translation,
    LabelWithInfo,
  },
  data() {
    return {
      locale: "es",
      activeIndex: 0,
      CourseSection: {
        id: -1,
        name: "",
        description: "",
        sort: 0,
        active: false,
        hideCategoryName: false,
        translations: [],
        sectionCategories: [],
      },
      currentStep: "basic-info",
      currentStepNumber: 1,
      numberLanguages: 1,
      subtypesx: [],
      dragItem: null,
      dropItem: null,
      showBasicTranslation: true,
      showCreateAt: false,
      showUpdateAt: false,
      UpdateBy: "",
      UpdateAt: "",
      createdBy: "",
      createdAt: "",
      styleManualSelection: {
        backgroundColor: "#CBD5E1",
      },
      warningLocales: {},
    };
  },
  computed: {
    ...get("sectionModule", [
      "loading",
      "getSections",
      "getCourseCategories",
      "getSubtypes",
      "getLanguages",
      "getUserlocale",
    ]),
    locales: get("localeModule/locales"),
    defaultLocale: get("localeModule/defaultLocale"),

    sections() {
      return this.getSections();
    },
    categories() {
      return this.getCourseCategories();
    },
    subtypes() {
      return this.getSubtypes();
    },
    languages() {
      return this.getLanguages();
    },
    userlocale() {
      return this.getUserlocale();
    },
    numberOfSteps() {
      return 2;
    },
    froalaDescriptionConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 150,
        pluginsEnabled: [
          "align",
          "link",
          "url",
          "lists",
          "paragraphStyle",
          "paragraphFormat",
          "quote",
        ],
      };
    },
    innerValue: {
      get() {
        return this.value ?? [];
      },
      set(newValue) {
        this.$emit("input", newValue);
      },
    },
  },
  async created() {
    if (!this.sections.length && this.$route.params?.id){ 
      await this.$store.dispatch( "sectionModule/fetchSections", "/admin/section/all");
    }
    this.setButtonsTitle();
    this.numberLanguages = this.languages.length;
    this.locale = this.userlocale;

    let CourseSection = {
      id: -1,
      name: "",
      description: "",
      sort: 0,
      active: false,
      hideCategoryName: false,
      translations: [],
      sectionCategories: [],
      subtypes: [],
      atAndBy: [],
      disAtAndBy: false,
      isManualSelection: false,
    };
    CourseSection = this.initCoursection(CourseSection);
    if (CourseSection === undefined) {
      this.returnToList();
      return;
    }

    CourseSection.translations = this.getTranslations(
      CourseSection.translations
    );
    this.setWarningLocales(CourseSection.translations);
    this.CourseSection = CourseSection;
    this.subtypesx.sort((a, b) => a.sort - b.sort);
    
    const isUpdate = this.$route.name === 'SectionUpdate';
    await this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: isUpdate ? this.$t('SECTION.UPDATE') : this.$t('SECTION.CREATE'),
        params: this.$route.params
      }
    });
  },
  mounted() {
    this.$eventBus.$on("onSave", (e) => {
      this.submit();
    });
    this.$eventBus.$on("onDelete", (e) => {
      this.remove();
    });
  },
  beforeDestroy() {
    this.$eventBus.$off("onSave");
    this.$eventBus.$off("onDelete");
  },
  methods: {
    setButtonsTitle() {
      const actions = [];
      if (this.$route.name === "SectionUpdate") {
        actions.push({
          name: this.$t("DELETE"),
          event: "onDelete",
          class: "btn btn-danger",
        });
      }
      actions.push({
        name: this.$t("SAVE"),
        event: "onSave",
        class: "btn btn-primary",
      });
      
      this.$store.dispatch("contentTitleModule/setActions", {
        route: this.$route.name,
        actions,
      });
    },
    initCoursection(CourseSection) {
      if (this.$route.name === "SectionUpdate") {
        CourseSection = this.sections.find(
          (c) => c.id === this.$route.params.id
        );
        this.disAtAndBy = true;
        this.createdBy = CourseSection.atAndBy["createdBy"];
        this.createdAt = CourseSection.atAndBy["createdAt"];
        this.UpdateBy = CourseSection.atAndBy["UpdateBy"];
        this.UpdateAt = CourseSection.atAndBy["UpdateAt"];
        this.showCreateAt = true;
        this.showUpdateAt = true;
        this.dispSelCategories = false;
        this.subtypesx = CourseSection.subtypes;
      } else {
        this.disAtAndBy = false;
        this.createdBy = "";
        this.createdAt = "";
        this.UpdateBy = "";
        this.UpdateAt = "";
        this.subtypesx = this.subtypes;
      }

      return CourseSection;
    },
    getTranslations(Sectiontranslations) {
      let translations = [];
      const keys = Object.keys(this.locales);
      keys.forEach((k) => {
        const translated = Sectiontranslations.find((e) => e.locale === k);
        translations.push({
          locale: k,
          name: translated?.name ?? "",
          description: translated?.description ?? "",
        });
      });

      return translations;
    },
    setWarningLocales(translations) {
      translations.forEach((t) => {
        let warning = t.locale !== this.locale;
        if (t.locale !== this.locale)
          warning = t.name.length < 1 || t.description.length < 1;

        this.warningLocales[t.locale] = warning;
      });
    },
    manualSelection() {
      if (this.styleManualSelection.backgroundColor === "#CBD5E1")
        this.styleManualSelection.backgroundColor = "#009BDB";
      else this.styleManualSelection.backgroundColor = "#CBD5E1";

      return !this.CourseSection.isManualSelection;
    },
    returnToList() {
      this.$router.push({ name: "Home" });
    },

    startDrag(evt, index) {
      this.dragItem = index;
      evt.dataTransfer.dropEffect = "move";
      evt.dataTransfer.effectAllowed = "move";
    },
    onDrop(index) {
      let data = this.orderNewSort(this.dragItem, index + 1);

      this.dropItem = this.subtypesx.splice(this.dragItem, 1)[0];
      this.subtypesx.splice(index, 0, this.dropItem);
      this.dropItem = null;

      this.subtypesx.forEach((item, index) => {
        item.sort = index + 1;
      });
    },
    orderNewSort(antPosSort, newPosSort) {
      let newSortSubtypes = [];

      let elmentTras = {
        id: this.subtypesx[antPosSort].id,
        newSort: newPosSort,
      };
      newSortSubtypes.push(elmentTras);

      if (this.dragItem > newPosSort) {
        this.subtypesx.forEach((item) => {
          let element = {
            id: null,
            newSort: null,
          };

          if (item.sort >= newPosSort && item.sort <= antPosSort) {
            element.id = item.id;
            element.newSort = item.sort + 1;
            newSortSubtypes.push(element);
          }
        });
      } else {
        this.subtypesx.forEach((item) => {
          let element = {
            id: null,
            newSort: null,
          };
          if (item.sort > antPosSort + 1 && item.sort <= newPosSort) {
            element.id = item.id;
            element.newSort = item.sort - 1;
            newSortSubtypes.push(element);
          }
        });
      }

      return newSortSubtypes;
    },

    getTranslationName() {
      let translationName = this.CourseSection.translations.filter(
        (translation) => translation.locale === this.locale
      );
      return translationName[0].name;
    },
    getTranslationDescription() {
      let translationName = this.CourseSection.translations.filter(
        (translation) => translation.locale === this.locale
      );
      return translationName[0].description;
    },
    async submit() {
      if (this.numberLanguages > 1) {
        this.CourseSection.name = this.CourseSection.slug = this.getTranslationName();
        this.CourseSection.description = this.getTranslationDescription();
      }

      if (!this.CourseSection.name || this.CourseSection.name.length < 1) {
        this.$toast.error(this.$t("ERROR_NAME_FIELD_REQUIRED") + "");
        return;
      }

      if (this.$route.name === "SectionCreate") {
        this.CourseSection.active = true;
        this.CourseSection.sort = this.sections.length;
      }

      if (!this.isAtLeastOneSubtypeSelected()) {
        this.$toast.error(this.$t("COURSE.COURSE_SECTION.ERROR_SUBTYPES") + "");
        return;
      }

       if(!this.isAtLeastOneOptionNotContinueView()){
        this.$toast.error(this.$t("COURSE.COURSE_SECTION.ERROR_CONTINUE_VIEW") + "");
        return;
      } 

      if(this.CourseSection.isManualSelection && this.CourseSection.sectionCategories.length < 1){
        this.$toast.error(this.$t("COURSE.COURSE_SECTION.ERROR_CATEGORIES") + "");
        return;
      }
     
      if (this.$route.name === "SectionCreate") {
        this.CourseSection.active = true;
        this.CourseSection.sort = this.sections.length;
      }



      this.CourseSection.subtypes = this.subtypesx;

      const update = this.$route.name === "SectionUpdate";
      const endpoint = update
        ? "/admin/section/update"
        : "/admin/section/create";

      await this.$store.dispatch("sectionModule/save", {
        endpoint: endpoint,
        requestData: this.CourseSection,
      });

      this.returnToList();
    },

   
    isAtLeastOneSubtypeSelected() {
      let selected = this.subtypesx.filter((s) => s.active);
      return selected.length > 0;
    },

   
    isAtLeastOneOptionNotContinueView() {
      let selected = this.subtypesx.filter((s) => s.active);
      let continueView = selected.filter((s) => s.subtype === "COURSE_STARTED");

      
      return continueView.length < selected.length;
    },

    remove() {
      if (this.$route.name !== "SectionCreate") {
        this.$alertify.confirmWithTitle(
          this.$t("DELETE"),
          this.$t("COMMON_AREAS.QUESTION_DELETE"),
          () => {
            this.$store
              .dispatch("sectionModule/deleteSection", {
                endpoint: `/admin/section/${this.CourseSection?.id}/delete`,
              })
              .then((r) => {
                this.$toast.success(this.$t("DELETE_SUCCESS") + "");
              })
              .catch((e) => {
                this.$toast.error("DELETE_FAILED");
              })
              .finally(() => {
                this.returnToList();
              });
          },
          () => {}
        );
      }
    },
  },
};
</script>

<style scoped lang="scss">
.FormView {
  .Header {
    padding: 1rem;
    & > h4 {
      font-size: 22px;
      color: var(--color-neutral-darkest);
    }
  }

  .tab-pane {
    padding: 1rem;
  }

  .CurrentStepSectionTitle {
    font-weight: bold;
    border-bottom: 1px solid $base-border-color;
    width: 100%;
    font-size: 20px;
    padding-bottom: 0.3rem;
  }
}
</style>
