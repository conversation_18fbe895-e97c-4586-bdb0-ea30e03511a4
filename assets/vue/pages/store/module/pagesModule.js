import { make } from "vuex-pathify";
import axios from "axios";

const getDefaultState = () => ({
  loading: true,
  pagesbd: [],
  languages: [],
});

const state = () => getDefaultState();

export const getters = {
  getPagesBD: (state) => () => state.pagesbd,
  getLanguages: (state) => () => state.languages,
};

export const mutations = {
  ...make.mutations(state),

  SET_NEW_VALUE_PAGESBD(state, newValue) {
    state.pagesbd = state.pagesbd.map((page) => {
      if (page.id === newValue.id) {
        return newValue;
      }

      return page;
    });
  },

};

export const actions = {
  ...make.actions(state),

  async load({ commit }, endpoint) {
    try {
      commit("SET_LOADING", true);
      const { data, error } = await axios.get(endpoint);
      if (error) {
        throw new Error(data?.message);
      }

      commit("SET_PAGESBD", data?.data?.pages);
      commit("SET_LANGUAGES", data?.data?.languages);

    } catch (error) {
      console.log(error); 
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async changeStatus({ commit }, { endpoint}) {
    try {
      commit("SET_LOADING", true);

      const { data, error } = await axios.put(endpoint);

      if (error) {
        throw new Error(data?.message);
      }
      commit("SET_NEW_VALUE_PAGESBD", data?.data);
      
    } catch (error) {
      console.log(error);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async changeIsMain({ commit }, { endpoint }) {
    try {
      commit("SET_LOADING", true);

      const { data, error } = await axios.put(endpoint);

      if (error) {
        throw new Error(data?.message);
      }
      commit("SET_NEW_VALUE_PAGESBD", data?.data?.newSectionMain);
      commit("SET_NEW_VALUE_PAGESBD", data?.data?.oldSectionMain);

    } catch (error) {
      console.log(error);
    } finally {
      commit("SET_LOADING", false);
    }
  },
  
  async deletePages({ commit }, { endpoint }) {
    try {
      commit("SET_LOADING", true);

      const { data, error } = await axios.delete(endpoint);

      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.log(error);
    } finally {
      commit("SET_LOADING", false);
    }
  },
  
  async save({commit}, { endpoint, requestData }) {
    try {
      commit("SET_LOADING", true);

      const { data, error } = await axios.post(endpoint, requestData);
console.log(data)
      commit("SET_NEW_VALUE_PAGESBD", data?.data);

      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.log(error);
    }finally {
      //commit("SET_LOADING", false);
    }
  },

  async changePosition({ commit }, { endpoint, requestData }) {
    try {
      commit("SET_LOADING", true);

      const { data, error } = await axios.put(endpoint, requestData);

      commit("SET_NEW_VALUE_PAGESBD", data?.data);

      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.log(error);
    }finally {
      commit("SET_LOADING", false);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
