<script>
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import BaseForm from "../BaseForm.vue";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm},
  data() {
    return {
      locale: 'es',
      certificate: {
        id: -1,
        name: '',
        description: '',
        active: false,
        isMain: false,
        applyTo: 1,
        translations: [],
        extra: []
      }
    };
  },
  computed: {
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    if (this.catalogs.length === 0) {
      this.returnToList();
      return;
    }
    let certificate = {
      id: -1,
      name: '',
      description: '',
      active: false,
      isMain: false,
      applyTo: 1,
      translations: [],
      extra: []
    };

    if (this.$route.name === 'DiplomasTypeUpdate') {
      certificate = this.catalogs.find(c => c.id === this.$route.params.id);
      if (certificate === undefined) {
        this.returnToList();
        return;
      }
    }

    let translations = [];
    const keys = Object.keys(this.locales);
    keys.forEach((k) => {
      const translated = certificate.translations.find(e => e.locale === k);
      translations.push({
        locale: k,
        name: translated?.name ?? '',
        description: translated?.description ?? ''
      })
    });

    certificate.translations = translations;
    this.certificate = certificate;
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'DiplomasType', params: this.$route.params});
    },

    isValidApplyTo(applyTo) {
      return [1, 2, 3].includes(Number(applyTo));
    },

    async submit() {
      const update = this.$route.name === 'DiplomasTypeUpdate';
      const endpoint = `/admin/certificate-type/${update ? 'update' : 'create'}`;

      if (!this.isValidApplyTo(this.certificate.applyTo)) {
        this.$toast.error(this.$t('CATALOG.TYPE_DIPLOMA.INVALID_APPLY_TO'));
        return;
      }

      const save = () => {
        // Exclude 'extra' and 'translations' fields from data sent to backend
        const { extra, translations, ...dataToSend } = this.certificate;
        return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: dataToSend });
      }

      save().then(r => {
        const { error, data } = r;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('CATALOG.SAVED') + '');
          this.returnToList();
        }
      })
    }
  }
}
</script>

<template>
<base-form v-model="locale" :with-translations="false" @cancel="returnToList" @submit="submit">
  <template v-slot:form>
    <div class="form-group col-12">
      <label>{{ $t('NAME') }}</label>
      <input type="text" class="form-control" v-model="certificate.name">
    </div>

    <div class="form-group col-12">
      <label>{{ $t('DESCRIPTION') }}</label>
      <textarea class="form-control" v-model="certificate.description" rows="5"></textarea>
    </div>

    <div class="form-group col-12 d-flex align-items-center justify-content-start">
      <BaseSwitch :tag="`switcher-certificate-form-active-${certificate.id}`"
                   v-model="certificate.active"/>
      <label class="ml-1">{{ $t('ACTIVE') }}</label>
    </div>

    <div class="form-group col-12 d-flex align-items-center justify-content-start">
      <BaseSwitch :tag="`switcher-certificate-form-main-${certificate.id}`"
                   v-model="certificate.isMain"/>
      <label class="ml-1">{{ $t('CATALOG.TYPE_DIPLOMA.IS_MAIN') }}</label>
    </div>

    <div class="form-group col-12">
      <label>{{ $t('CATALOG.TYPE_DIPLOMA.APPLY_TO') }}</label>
      <select class="form-select" v-model="certificate.applyTo">
        <option v-if="!isValidApplyTo(certificate.applyTo)" value="" disabled selected>{{ $t('CATALOG.TYPE_DIPLOMA.UNKNOWN') }}</option>
        <option value="1">{{ $t('CATALOG.TYPE_DIPLOMA.APPLY_ALL') }}</option>
        <option value="2">{{ $t('CATALOG.TYPE_DIPLOMA.APPLY_COURSES') }}</option>
        <option value="3">{{ $t('CATALOG.TYPE_DIPLOMA.APPLY_ANNOUNCEMENTS') }}</option>
      </select>
      <div v-if="!isValidApplyTo(certificate.applyTo)" class="text-danger mt-1">
        {{ $t('CATALOG.TYPE_DIPLOMA.INVALID_APPLY_TO') }}
      </div>
    </div>
  </template>
</base-form>
</template>

<style scoped lang="scss">

</style>
