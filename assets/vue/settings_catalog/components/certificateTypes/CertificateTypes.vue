<script>
import BaseSwitch from "../../../base/BaseSwitch.vue";
import Spinner from "../../../admin/components/base/Spinner.vue";
import {get, sync} from "vuex-pathify";

export default {
  name: "CertificateTypes",
  components: {BaseSwitch, Spinner},
  computed: {
    loading: get('catalogModule/loading'),
    catalogs: sync('catalogModule/catalogs'),
  },
  created() {
    this.$store.dispatch('catalogModule/load', '/admin/certificate-type/all');
  },
  methods: {
    changeActiveStatus(index) {
      const value = this.catalogs[index];
      this.$store.dispatch('catalogModule/changeActiveState',
          {endpoint: `/admin/certificate-type/${value.id}/active`, data: {id: value.id, active: value.active}});
    },
    changeIsMainStatus(index) {
      const value = this.catalogs[index];

      // If we are trying to set as main
      if (value.isMain) {
        // Deactivate any other main diploma in the UI
        this.catalogs.forEach((diploma, i) => {
          if (i !== index && diploma.isMain) {
            diploma.isMain = false;
          }
        });
      }

      // Update the current diploma state on the server
      // The backend will handle deactivating any other main diploma
      this.$store.dispatch('catalogModule/changeActiveState',
          {endpoint: `/admin/certificate-type/${value.id}/is-main`, data: {id: value.id, isMain: value.isMain}});
    },
    changeApplyToStatus(index) {
      const value = this.catalogs[index];
      this.$store.dispatch('catalogModule/changeActiveState',
          {endpoint: `/admin/certificate-type/${value.id}/apply-to`, data: {id: value.id, applyTo: value.applyTo}});
    },
    getApplyToText(applyTo) {
      switch(applyTo) {
        case 1: return this.$t('CATALOG.TYPE_DIPLOMA.APPLY_ALL');
        case 2: return this.$t('CATALOG.TYPE_DIPLOMA.APPLY_COURSES');
        case 3: return this.$t('CATALOG.TYPE_DIPLOMA.APPLY_ANNOUNCEMENTS');
        default: return this.$t('CATALOG.TYPE_DIPLOMA.UNKNOWN');
      }
    },
    isValidApplyTo(applyTo) {
      return [1, 2, 3].includes(Number(applyTo));
    }
  }
}
</script>

<template>
  <div>
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>
    <table class="table table-condensed mt-3" v-else>
      <thead>
      <tr>
        <th></th>
        <th>{{ $t('NAME') }}</th>
        <th>{{ $t('ACTIVE') }}</th>
        <th>{{ $t('CATALOG.TYPE_DIPLOMA.IS_MAIN') }}</th>
        <th>{{ $t('CATALOG.TYPE_DIPLOMA.APPLY_TO') }}</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(c, index) in catalogs" :key="c.id">
        <td></td>
        <td>{{ c.name }}</td>
        <td>
          <BaseSwitch :tag="`switcher-certificate-type-${c.id}`" v-model="c.active" @change="changeActiveStatus(index)" />
        </td>
        <td>
          <BaseSwitch :tag="`switcher-certificate-type-main-${c.id}`" v-model="c.isMain" @change="changeIsMainStatus(index)" />
        </td>
        <td>
          <select class="form-select" v-model="c.applyTo" @change="changeApplyToStatus(index)">
            <option v-if="!isValidApplyTo(c.applyTo)" value="" disabled selected>{{ $t('CATALOG.TYPE_DIPLOMA.UNKNOWN') }}</option>
            <option value="1">{{ $t('CATALOG.TYPE_DIPLOMA.APPLY_ALL') }}</option>
            <option value="2">{{ $t('CATALOG.TYPE_DIPLOMA.APPLY_COURSES') }}</option>
            <option value="3">{{ $t('CATALOG.TYPE_DIPLOMA.APPLY_ANNOUNCEMENTS') }}</option>
          </select>
        </td>
        <td>
          <div class="dropdown">
            <button class="btn btn-default" type="button" :id="`dropdown-menu-${c.id}`" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${c.id}`">
              <li><router-link class="dropdown-item" :to="{ name: 'DiplomasTypeUpdate', params: {...$route.params, id: c.id} }">{{ $t('EDIT') }}</router-link></li>
              <li><router-link class="dropdown-item" :to="{ name: 'DiplomasTypePreview', params: {...$route.params, id: c.id, name: c.name} }">{{ $t('CATALOG.TYPE_DIPLOMA.PREVIEW') }}</router-link></li>
            </ul>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">

</style>
