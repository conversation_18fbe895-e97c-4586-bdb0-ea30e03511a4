
<template>
  <div>
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>
    <div v-else>
      <div class="col-12 d-flex align-items-center justify-content-end mt-0" >
        <router-link
          type="button"
          class="btn btn-primary"
          :to="{ name: 'UserExtraFieldsCreate' }"
        >
          {{ $t('USER.EXTRA_FIELDS.CREATE') }}
        </router-link>
      </div>
      <table class="table table-condensed mt-3" >
        <thead>
        <tr>
          <th></th>
          <th>{{ $t('USER.EXTRA_FIELDS.LABEL') }}</th>
          <th>{{ $t('NAME') }}</th>
          <th>{{ $t('USER.EXTRA_FIELDS.REQUIRED') }}</th>
          <th>{{ $t('TYPE') }}</th>
          <th></th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(c, index) in userExtraFields" :key="index">
          <td></td>
          <td>{{ c.label.default }}</td>
          <td>{{ c.name }}</td>
          <td>
            <BaseSwitch :tag="`switcher-userExtraFields-${index}`" v-model="c.required" @change="changeRequired(index)" />
          </td>
          <td>{{ c.type }}</td>
          <td>
            <div class="dropdown">
              <button class="btn btn-default" type="button" :id="`dropdown-menu-${index}`" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fa fa-ellipsis-h"></i>
              </button>
              <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${index}`">
                <li><router-link class="dropdown-item" :to="{ name: 'UserExtraFieldsUpdate', params: {...$route.params, id: index} }">{{ $t('EDIT') }}</router-link></li>
              </ul>
            </div>
          </td>
        </tr>
        </tbody>
      </table>
    </div>

  </div>
</template>

<script>
import Spinner from "../../../admin/components/base/Spinner.vue";
import {get, sync} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";

export default {
  name: "UserExtraFields",
  components: {BaseSwitch, Spinner},
  computed: {
    loading: get('userExtraFieldsModule/loading'),
    userExtraFields: get('userExtraFieldsModule/userExtraFields'),
  },

  created() { 
    this.$store.dispatch('userExtraFieldsModule/load', '/admin/userExtraFields/all');
  },
  methods: {
    changeRequired(index) {
      const value = this.userExtraFields[index];
      this.$store.dispatch('userExtraFieldsModule/changeSave');
    }
  }
}
</script>

<style scoped lang="scss">

</style>
