<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import Multiselect from "vue-multiselect";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm, Multiselect}, 
  data() {
    return {
      locale: 'es',
      AnnConfigurationType: {
        id: -1,
        name: '',
        description: '',
        active:'',
        configs: [],
        translations: []
      },
    };
  },
  computed: {
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    // if (this.catalogs.length === 0) {
    //   this.returnToList();
    //   return;
    // }

    let AnnConfigurationType = {
      id: -1,
      name: '',
      description: '',
      active:'',
      configs: [],
      translations: []
    };
    
    if (this.$route.name === 'ConfigurationClientAnnouncementUpdate') {
      AnnConfigurationType = this.catalogs.find(c => c.id === this.$route.params.id);   
      AnnConfigurationType = AnnConfigurationType.configs.find(c => c.id === this.$route.params.idtype);

      if (AnnConfigurationType === undefined) {
        this.returnToList();
        return;
      }
    }

    let translations = [];

    const keys = Object.keys(this.locales);
    keys.forEach((k) => {
      const translated = AnnConfigurationType.translations.find(e => e.locale === k);
      translations.push({
        locale: k,
        name: translated?.name ?? '',
        description: translated?.description ?? ''
      })
    });
    
    AnnConfigurationType.translations = translations;
    this.AnnConfigurationType = AnnConfigurationType;
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'ConfigurationClientAnnouncement', params: this.$route.params});
    },

    submit() {
      const update = this.$route.name === 'ConfigurationClientAnnouncementUpdate';
      const endpoint = update ? '/admin/configuration-client-announcement/update' : '/admin/configuration-client-announcement/create';
      const save = () => {
        return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.AnnConfigurationType });
      }

      save().then(r => {
        const { error, data } = r;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('CATALOG.SAVED') + '');
          this.returnToList();
        }
      })
    }
  }
}
</script>

<template>
  <base-form v-model="locale" @cancel="returnToList()" @submit="submit()">
    <template v-slot:form>
      <div class="form-group col-12">
        <label>{{ $t('NAME') }}</label>
        <input type="text" class="form-control" v-model="AnnConfigurationType.name">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('DESCRIPTION') }}</label>
        <textarea class="form-control" v-model="AnnConfigurationType.description" rows="5"></textarea>
      </div>

      <div class="form-group col-12 d-flex align-items-center justify-content-start">
        <BaseSwitch :tag="`switcher-type-money-form-active-${AnnConfigurationType.id}`"
                     v-model="AnnConfigurationType.active"/>
        <label class="ml-1">{{ $t('ACTIVE') }}</label>
      </div>
    </template>
    <template v-slot:translations>
      <div v-for="t in AnnConfigurationType.translations" :key="t.locale" v-if="t.locale === locale">
        <div class="form-group col-12">
          <label>{{ $t('NAME') }}</label>
          <input type="text" class="form-control" v-model="t.name">
        </div>

        <div class="form-group col-12">
          <label>{{ $t('DESCRIPTION') }}</label>
          <textarea class="form-control" v-model="t.description" rows="5"></textarea>
        </div>
      </div>
    </template>
  </base-form>
</template>

<style scoped lang="scss">

</style>
