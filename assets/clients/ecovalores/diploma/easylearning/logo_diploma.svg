<?xml version="1.0" encoding="UTF-8"?>
<svg id="Capa_1" data-name="Capa 1" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 595.3 251.4">
  <defs>
    <style>
      .cls-1 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_3-8);
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5, .cls-6, .cls-7, .cls-8, .cls-9, .cls-10, .cls-11, .cls-12, .cls-13, .cls-14, .cls-15, .cls-16, .cls-17, .cls-18, .cls-19, .cls-20, .cls-21, .cls-22, .cls-23, .cls-24, .cls-25, .cls-26, .cls-27, .cls-28, .cls-29, .cls-30, .cls-31, .cls-32, .cls-33, .cls-34 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: url(#Degradado_sin_nombre_273-2);
      }

      .cls-3 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_3-4);
      }

      .cls-4 {
        fill: url(#Degradado_sin_nombre_274-8);
      }

      .cls-5 {
        fill: url(#Degradado_sin_nombre_274-4);
      }

      .cls-6 {
        fill: url(#Degradado_sin_nombre_274-2);
      }

      .cls-7 {
        fill: url(#Degradado_sin_nombre_274-10);
      }

      .cls-8 {
        fill: url(#Degradado_sin_nombre_274-11);
      }

      .cls-9 {
        fill: url(#Degradado_sin_nombre_273-12);
      }

      .cls-10 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_3-3);
      }

      .cls-11 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_3-2);
      }

      .cls-12 {
        fill: url(#Degradado_sin_nombre_274-3);
      }

      .cls-13 {
        fill: url(#Degradado_sin_nombre_273-10);
      }

      .cls-14 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_2);
      }

      .cls-15 {
        fill: url(#Degradado_sin_nombre_274);
      }

      .cls-16 {
        fill: url(#Degradado_sin_nombre_273-6);
      }

      .cls-17 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_3-7);
      }

      .cls-18 {
        fill: url(#Degradado_sin_nombre_273-8);
      }

      .cls-19 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_3-6);
      }

      .cls-20 {
        fill: url(#Degradado_sin_nombre_273);
      }

      .cls-21 {
        fill: url(#Degradado_sin_nombre_273-7);
      }

      .cls-35 {
        opacity: .8;
      }

      .cls-22 {
        fill: url(#Degradado_sin_nombre_273-9);
      }

      .cls-23 {
        fill: url(#Degradado_sin_nombre_273-11);
      }

      .cls-36 {
        opacity: .9;
      }

      .cls-24 {
        fill: url(#Degradado_sin_nombre_273-4);
      }

      .cls-25 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_4);
      }

      .cls-26 {
        fill: url(#Degradado_sin_nombre_274-6);
      }

      .cls-27 {
        fill: url(#Degradado_sin_nombre_274-7);
      }

      .cls-28 {
        fill: url(#Degradado_sin_nombre_274-12);
      }

      .cls-29 {
        fill: url(#Degradado_sin_nombre_274-5);
      }

      .cls-30 {
        fill: url(#Degradado_sin_nombre_273-3);
      }

      .cls-31 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_3);
      }

      .cls-32 {
        fill: url(#Degradado_sin_nombre_273-5);
      }

      .cls-33 {
        fill: url(#Degradado_sin_nombre_274-9);
      }

      .cls-34 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_3-5);
      }
    </style>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_4" data-name="Áåçûìÿííûé ãðàäèåíò 4" x1="1323.3" y1="126.8" x2="1493.2" y2="-24.2" gradientTransform="translate(1425.4 945.7) rotate(-141.5) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#063435"/>
      <stop offset=".1" stop-color="#0c353b"/>
      <stop offset=".3" stop-color="#1d3a4e"/>
      <stop offset=".5" stop-color="#39426e"/>
      <stop offset=".8" stop-color="#604d99"/>
      <stop offset=".8" stop-color="#614e9a"/>
      <stop offset=".8" stop-color="#69509b"/>
      <stop offset=".9" stop-color="#7f579e"/>
      <stop offset="1" stop-color="#8e5ca0"/>
    </linearGradient>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_3" data-name="Áåçûìÿííûé ãðàäèåíò 3" x1="391.9" y1="170.2" x2="217.1" y2="17.7" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#063435"/>
      <stop offset=".1" stop-color="#0c353b"/>
      <stop offset=".3" stop-color="#1d3a4e"/>
      <stop offset=".5" stop-color="#39426e"/>
      <stop offset=".7" stop-color="#604d99"/>
      <stop offset=".7" stop-color="#614e9a"/>
      <stop offset=".8" stop-color="#654f9a"/>
      <stop offset="1" stop-color="#8e5ca0"/>
    </linearGradient>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_3-2" data-name="Áåçûìÿííûé ãðàäèåíò 3" x1="399.2" y1="161.8" x2="224.5" y2="9.3" xlink:href="#_Áåçûìÿííûé_ãðàäèåíò_3"/>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_3-3" data-name="Áåçûìÿííûé ãðàäèåíò 3" x1="385.5" y1="177.5" x2="210.7" y2="25" xlink:href="#_Áåçûìÿííûé_ãðàäèåíò_3"/>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_3-4" data-name="Áåçûìÿííûé ãðàäèåíò 3" x1="363.7" y1="202.5" x2="189" y2="49.9" xlink:href="#_Áåçûìÿííûé_ãðàäèåíò_3"/>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_3-5" data-name="Áåçûìÿííûé ãðàäèåíò 3" x1="374" y1="190.7" x2="199.2" y2="38.2" xlink:href="#_Áåçûìÿííûé_ãðàäèåíò_3"/>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_3-6" data-name="Áåçûìÿííûé ãðàäèåíò 3" x1="327.3" y1="244.2" x2="152.6" y2="91.7" xlink:href="#_Áåçûìÿííûé_ãðàäèåíò_3"/>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_3-7" data-name="Áåçûìÿííûé ãðàäèåíò 3" x1="381.7" y1="181.9" x2="206.9" y2="29.4" xlink:href="#_Áåçûìÿííûé_ãðàäèåíò_3"/>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_3-8" data-name="Áåçûìÿííûé ãðàäèåíò 3" x1="386.6" y1="176.2" x2="211.8" y2="23.7" xlink:href="#_Áåçûìÿííûé_ãðàäèåíò_3"/>
    <radialGradient id="_Áåçûìÿííûé_ãðàäèåíò_2" data-name="Áåçûìÿííûé ãðàäèåíò 2" cx="375.9" cy="87.9" fx="375.9" fy="87.9" r="299.8" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#063435"/>
      <stop offset=".3" stop-color="#39426e"/>
      <stop offset=".5" stop-color="#614e9a"/>
      <stop offset=".6" stop-color="#715299"/>
      <stop offset=".8" stop-color="#9b5c98"/>
      <stop offset="1" stop-color="#c76798"/>
    </radialGradient>
    <linearGradient id="Degradado_sin_nombre_274" data-name="Degradado sin nombre 274" x1="246.6" y1="161.2" x2="291.1" y2="161.2" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cc758c"/>
      <stop offset="1" stop-color="#a24c96"/>
    </linearGradient>
    <linearGradient id="Degradado_sin_nombre_274-2" data-name="Degradado sin nombre 274" x1="297.2" y1="158.4" x2="348.1" y2="158.4" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_274-3" data-name="Degradado sin nombre 274" x1="352.1" y1="155.4" x2="388.3" y2="155.4" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_274-4" data-name="Degradado sin nombre 274" x1="391.6" y1="152.3" x2="442.5" y2="152.3" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_274-5" data-name="Degradado sin nombre 274" x1="444.5" y1="148.5" x2="498.9" y2="148.5" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_274-6" data-name="Degradado sin nombre 274" x1="500.3" y1="144.4" x2="550.3" y2="144.4" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_273" data-name="Degradado sin nombre 273" x1="259" y1="161.5" x2="303.5" y2="161.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#e9c700"/>
      <stop offset="1" stop-color="#fff23e"/>
    </linearGradient>
    <linearGradient id="Degradado_sin_nombre_273-2" data-name="Degradado sin nombre 273" x1="309.5" y1="158.7" x2="360.4" y2="158.7" xlink:href="#Degradado_sin_nombre_273"/>
    <linearGradient id="Degradado_sin_nombre_273-3" data-name="Degradado sin nombre 273" x1="364.5" y1="155.7" x2="400.7" y2="155.7" xlink:href="#Degradado_sin_nombre_273"/>
    <linearGradient id="Degradado_sin_nombre_273-4" data-name="Degradado sin nombre 273" x1="404" y1="152.7" x2="454.9" y2="152.7" xlink:href="#Degradado_sin_nombre_273"/>
    <linearGradient id="Degradado_sin_nombre_273-5" data-name="Degradado sin nombre 273" x1="456.9" y1="148.8" x2="511.3" y2="148.8" xlink:href="#Degradado_sin_nombre_273"/>
    <linearGradient id="Degradado_sin_nombre_273-6" data-name="Degradado sin nombre 273" x1="512.7" y1="144.7" x2="562.7" y2="144.7" xlink:href="#Degradado_sin_nombre_273"/>
    <linearGradient id="Degradado_sin_nombre_274-7" data-name="Degradado sin nombre 274" x1="50.1" y1="103.6" x2="99.1" y2="103.6" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_274-8" data-name="Degradado sin nombre 274" x1="94.3" y1="100.4" x2="157.8" y2="100.4" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_274-9" data-name="Degradado sin nombre 274" x1="162.4" y1="96.5" x2="208.6" y2="96.5" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_274-10" data-name="Degradado sin nombre 274" x1="201.4" y1="93.8" x2="258.8" y2="93.8" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_274-11" data-name="Degradado sin nombre 274" x1="260.7" y1="89.9" x2="309.7" y2="89.9" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_274-12" data-name="Degradado sin nombre 274" x1="304.9" y1="86.7" x2="368.4" y2="86.7" xlink:href="#Degradado_sin_nombre_274"/>
    <linearGradient id="Degradado_sin_nombre_273-7" data-name="Degradado sin nombre 273" x1="36.4" y1="104.4" x2="85.4" y2="104.4" xlink:href="#Degradado_sin_nombre_273"/>
    <linearGradient id="Degradado_sin_nombre_273-8" data-name="Degradado sin nombre 273" x1="80.5" y1="101.2" x2="144.1" y2="101.2" xlink:href="#Degradado_sin_nombre_273"/>
    <linearGradient id="Degradado_sin_nombre_273-9" data-name="Degradado sin nombre 273" x1="148.6" y1="97.2" x2="194.9" y2="97.2" xlink:href="#Degradado_sin_nombre_273"/>
    <linearGradient id="Degradado_sin_nombre_273-10" data-name="Degradado sin nombre 273" x1="187.7" y1="94.5" x2="245.1" y2="94.5" xlink:href="#Degradado_sin_nombre_273"/>
    <linearGradient id="Degradado_sin_nombre_273-11" data-name="Degradado sin nombre 273" x1="247" y1="90.7" x2="296" y2="90.7" xlink:href="#Degradado_sin_nombre_273"/>
    <linearGradient id="Degradado_sin_nombre_273-12" data-name="Degradado sin nombre 273" x1="291.1" y1="87.4" x2="354.7" y2="87.4" xlink:href="#Degradado_sin_nombre_273"/>
  </defs>
  <g class="cls-36">
    <g>
      <circle class="cls-25" cx="293.1" cy="123" r="114.5" transform="translate(-1.2 243.3) rotate(-45)"/>
      <g class="cls-35">
        <path class="cls-31" d="M190.5,88.3c16.3-.8,29.9-9.2,43.8-16.9,14.7-8.2,29.3-16.5,44.3-23.9,7.4-3.7,14.9-7.1,22.5-10.2,6.2-2.6,12.5-5.1,17.8-9.4,4.1-3.3,8.8-8.8,7.2-14.5-1.9-.6-3.8-1.1-5.7-1.6,2.5,3.1-3.2,6.6-5.7,8.5-6,4.2-13,6.7-19.8,9.3-8,3-15.9,6.3-23.7,9.9-15.7,7.3-30.9,15.6-46.1,24-8.1,4.5-16.2,8.9-24.7,12.5-4.1,1.7-8.1,3.1-12.5,3.8-.3,0-.6,0-.8.1-1.1,2.7-2.1,5.5-3,8.3,2.2,0,4.3.2,6.5,0Z"/>
        <path class="cls-11" d="M282.7,13c7.6-2.3,15.5-3.5,23.4-3.8-5.5-.6-11.1-.8-16.7-.7-6.8,1.8-13.4,4.2-19.5,6.7-14.8,6.2-28.3,15.1-41.5,24-6.6,4.4-13,9.2-19.9,13-2.7,1.5-5.4,2.8-8.3,3.8-.5.6-.9,1.3-1.4,2,13.6-3.6,25.4-14.3,36.8-21.8,14.6-9.6,30.3-18.3,47.1-23.3Z"/>
        <path class="cls-10" d="M297.9,72.8c20-9.4,40.2-18.5,56.8-33.6,2.6-2.4,5.1-4.9,7.4-7.6-3-2.3-6.2-4.4-9.5-6.4-8.8,9.9-20.2,17.7-31.4,24.8-19.9,12.8-40.9,23.7-61.7,34.8-21.7,11.6-43.6,25-67.3,32.2-4.5,1.4-9,2.4-13.6,3.2,0,.8,0,1.5,0,2.3,16.5-1.5,32.6-7.1,47.8-13.8,24.4-10.8,47.4-24.6,71.5-35.9Z"/>
        <path class="cls-3" d="M221.6,195.2c12.8-1.6,25.7-3.3,38.5-4,6.3-.4,12.7-.5,19,0,6.4.6,12.8,1.7,19.3,1.7,11.8,0,24.1-2.5,34.8-7.4,10.5-4.8,20.4-10.6,30.3-16.5,9-5.4,17.6-12.1,23-21.3,5.7-9.8,9-20,17.1-28.2,1.2-1.2,2.5-2.5,3.7-3.7-.1-1.9-.3-3.9-.5-5.9-.1-.9-.2-1.8-.3-2.7-.6.7-1.1,1.3-1.7,2-3.9,4.1-8.4,7.6-11.9,12.1-7.1,9.2-10,20.7-19.4,28.2-8.9,7.2-20.5,11.9-30.9,16.7-10.9,5.1-22.5,10.2-34.3,12.9-12.8,2.9-25.8.6-38.7,2.3-13.1,1.7-26.1,4.7-39,7.2-6.4,1.2-12.8,2.2-19.2,3.3-2.7.5-5.6.9-8.3,1.7.9,1.2,1.8,2.3,2.8,3.4,5.1-1.2,10.7-1.3,15.8-2Z"/>
        <path class="cls-34" d="M240.3,170.4c6-.5,11.2-2,16.9-3.9,5.8-2,10.8,0,16.7.5,6.4.5,12.8,0,19.1-1.1,12-2.1,23.4-7.2,34-12.9,10.5-5.6,21.8-11.8,30.8-19.6,9.5-8.2,12.7-20.5,17.7-31.5,5.3-11.6,11.4-22.8,18.3-33.5,0,0,0,0,0,0-1.5-2.8-3.1-5.5-4.8-8.1-1.3,1.6-2.5,3.3-3.7,4.9-7.3,9.8-14,20.1-20,30.7-5.5,9.7-9.1,21-18.6,27.5-9.3,6.3-19.2,12.2-29.3,17.3-9.6,4.9-19.9,9-30.6,10.2-11.9,1.4-23.4-2.6-34.1,4.4-4.7,3-8.4,5.5-14,6.5-6.5,1.1-13.1,2.1-19.6,3.1-10.1,1.6-20.1,3.2-30.2,4.8.7,1.5,1.3,2.9,2.1,4.4,10.2-.8,20.5-1.5,30.7-2.3,6.3-.5,12.6-.9,18.9-1.4Z"/>
        <path class="cls-19" d="M272.1,213.4c6.7.2,13.3,1,19.8,2.2,6.3,1.2,12.3,1.7,18.6.6,12.3-2.1,24.4-7,36.3-10.9,2.8-.9,1.7-5.3-1.2-4.5-6.1,1.8-12.2,3.5-18.3,5.4-6,1.8-11.9,4-18.1,5-13,2.2-25.7-4-38.7-4.8-5.8-.4-11.7-.2-17.5.4-2.6.3-5.1.8-7.7.6-3.4-.3-7-.4-10.4-.3-5.5.2-11,2.1-16.4,2.7,0,0,0,0,0,0,5.3,4.6,11.1,8.7,17.2,12.2,5.3-1.9,9.8-5.7,15.3-7,6.8-1.7,14.3-1.8,21.2-1.6Z"/>
        <path class="cls-17" d="M179.7,138.7c3.4.3,6.7.6,10.1.6,6,0,12-.8,17.9-1.7,12.7-1.9,25.1-4.6,37.8-6.2,5.6-.7,11.5-1.3,16.9-3.1,6.3-2.1,12.4-5.1,18.3-8.3,10.9-5.7,21-12.8,31.3-19.4,10.8-6.9,21.7-13.7,32.2-21,9.9-7,18.4-15,26.5-24,2.4-2.7,5.1-5.6,7.7-8.8-1.3-1.4-2.6-2.9-4-4.2-5.9,7-12.7,13.4-19.9,18.8-10.3,7.8-22.1,13.4-33.4,19.4-12,6.4-24,12.7-36,19.1-11.3,6-21.9,12.8-33.8,17.8-12.2,5.1-24.9,8.8-37.7,12.3-6.4,1.7-12.7,3.2-19.3,3.9-5.1.5-10.2.4-15.3.2,0,.7.1,1.5.2,2.2.1.9.2,1.7.3,2.6Z"/>
        <path class="cls-1" d="M388,166.2c-4.9,5.4-10.1,10.4-15.6,15.1-5.6,4.9-11.8,9.1-17.3,14-1.2,1,.3,2.5,1.5,2,10.2-4.5,19-12.4,27.2-19.9,5.9-5.4,11.7-10.9,17.4-16.6,1.9-5.3,3.3-10.8,4.4-16.4-1.1,1.5-2.2,3.1-3.3,4.6-4.5,5.9-9.3,11.6-14.4,17.1Z"/>
      </g>
    </g>
    <path class="cls-14" d="M491.3,45.2c-12.1-30.9-70.7-38.6-144.5-23.3,14.5,7.7,27.2,18.4,37.2,31.4,19.4,1.9,32.9,8.3,37.3,19.4,4.3,11.1-1.2,25-14.1,39.6-18.7,21.1-52.8,43.4-94.7,59.8-41.9,16.4-82.1,23.3-110.1,20.6-19.4-1.9-32.9-8.3-37.3-19.4-4.3-11.1,1.2-25,14.1-39.6-1.6-16.8.6-33.2,5.8-48.3-64.5,39-102.2,84.5-90,115.4,12.1,30.9,70.7,38.6,144.5,23.3,26.4-5.5,54.7-13.9,83.5-25.2,28.8-11.3,55.3-24.4,78.4-38.4,64.5-39,102.2-84.5,90-115.4Z"/>
  </g>
  <g>
    <g>
      <path class="cls-15" d="M291.1,190.4c0,1.4-.3,1.8-1.6,2.2-4.4,1.4-10.3,2.9-17.9,3.3-9.3.6-23.7-3.7-24.6-28.4l-.4-9.6c-1-25,12.6-30.8,23.4-31.5,8.1-.5,13.9.6,17.1,1.4,1.2.3,1.5.5,1.5,1.6l.4,11.5c0,1-.3,1.5-1,1.5h-.2c-3,0-9.2-.2-15,.2-7.5.5-10.6,3.3-10.2,14.3l.4,9.7c.5,12.2,5,13.7,8,13.5,1.6,0,3-.3,4.3-.5l-.7-18.7c0-1,.6-1.9,1.4-1.9l12.3-.8c.9,0,1.6.7,1.6,1.7l1.2,30.5Z"/>
      <path class="cls-6" d="M346.9,190.3l-13.5.9c-.9,0-1.4-.6-1.6-1.6l-2.7-12.5-13.9.9-1.7,12.8c-.2,1-.6,1.8-1.5,1.8l-13.5.9c-.9,0-1.3-.4-1.4-1.4,0-.3,0-.2,0-.5l8.3-56.4c1.4-9.2,7.2-11.3,14.5-11.8,7.3-.5,13.3.9,15.4,9.9l12.6,55.1c0,.3,0,.4,0,.5,0,1-.3,1.5-1.2,1.5ZM321.5,139.7l-.3-.9c0-.3-.2-.3-.6-.3-.4,0-.5.1-.6.4l-.2.9-2.9,23.4,9.3-.6-4.7-22.9Z"/>
      <path class="cls-12" d="M388.3,185.6c0,1-.5,1.7-1.5,1.9-2.7.9-8.1,1.6-12.2,1.9-10,.6-20.1-1.4-20.8-19.1l-1.8-46.3c0-1,.7-1.9,1.6-1.9l12.7-.8c.9,0,1.6.7,1.6,1.7l1.8,46.3c.1,3.6,1.1,4.5,3.9,4.3l12.6-.8c.9,0,1.6.7,1.6,1.7l.4,11.1Z"/>
      <path class="cls-5" d="M441.3,184.3l-13.5.9c-.9,0-1.4-.6-1.6-1.6l-2.7-12.5-13.9.9-1.7,12.8c-.2,1-.6,1.8-1.5,1.8l-13.5.9c-.9,0-1.3-.4-1.4-1.4,0-.3,0-.2,0-.5l8.3-56.4c1.4-9.2,7.2-11.3,14.5-11.8,7.3-.5,13.3.9,15.4,9.9l12.6,55.1c0,.3,0,.4,0,.5,0,1-.3,1.5-1.2,1.5ZM416,133.7l-.3-.9c0-.3-.2-.3-.6-.3-.4,0-.5.1-.6.4l-.2.9-2.9,23.4,9.3-.6-4.7-22.9Z"/>
      <path class="cls-29" d="M497.9,180.7l-15.8,1c-.9,0-1.4-.5-2-1.5l-9-15.7-6.9,16.5c-.5,1-.9,1.9-1.8,1.9l-14.6.9c-.6,0-1-.4-1.1-1,0,0,0-.4.1-.7l14.6-34.5-16.9-29.6c0-.3-.2-.4-.2-.6,0-.7.3-1.1,1-1.2l15.9-1c.9,0,1.4.8,1.9,1.6l8.6,15,6.3-15.9c.4-.8.9-1.8,1.8-1.8l14.6-.9c.7,0,1,.3,1.1,1,0,.2,0,.4-.1.6l-14.4,33.2,17.6,30.8c.2.3.3.5.3.8,0,.6-.3,1-1,1.1Z"/>
      <path class="cls-26" d="M550.2,111.5l-10.2,32.2c-1.5,4.6-3.2,7-5.2,8.2l.9,24.7c0,1-.6,1.9-1.5,1.9l-12.7.8c-.9,0-1.5-.7-1.5-1.7l-.9-24.7c-2.1-.9-4-3.1-5.8-7.5l-12.6-30.7c0-.4-.2-.6-.2-.7,0-.8.3-1.2,1-1.3l14.1-.9c.9,0,1.3.6,1.6,1.6l8.4,23.7c.3.6.5.9.8.9.3,0,.5-.3.7-1l6.5-24.6c.3-1,.6-1.8,1.5-1.8l14.1-.9c.7,0,1.1.3,1.1,1.1,0,0,0,.4-.1.7Z"/>
    </g>
    <g>
      <path class="cls-20" d="M303.5,190.7c0,1.4-.3,1.8-1.6,2.2-4.4,1.4-10.3,2.9-17.9,3.3-9.3.6-23.7-3.7-24.6-28.4l-.4-9.6c-1-25,12.6-30.8,23.4-31.5,8.1-.5,13.9.6,17.1,1.4,1.2.3,1.5.5,1.5,1.6l.4,11.5c0,1-.3,1.5-1,1.5h-.2c-3,0-9.2-.2-15.1.2-7.5.5-10.6,3.3-10.2,14.3l.4,9.7c.5,12.2,5,13.7,8,13.5,1.6,0,3-.3,4.3-.5l-.7-18.7c0-1,.6-1.9,1.4-1.9l12.3-.8c.9,0,1.6.7,1.6,1.7l1.2,30.5Z"/>
      <path class="cls-2" d="M359.3,190.7l-13.5.9c-.9,0-1.4-.6-1.6-1.6l-2.7-12.5-13.9.9-1.7,12.8c-.2,1-.6,1.8-1.5,1.8l-13.5.9c-.9,0-1.3-.4-1.4-1.4,0-.3,0-.2,0-.5l8.3-56.4c1.4-9.2,7.2-11.3,14.5-11.8s13.3.9,15.4,9.9l12.6,55.1c0,.3,0,.4,0,.4,0,1-.3,1.5-1.2,1.5ZM333.9,140l-.3-.9c0-.3-.2-.3-.6-.3-.4,0-.5.1-.6.4l-.2.9-2.9,23.4,9.3-.6-4.7-22.9Z"/>
      <path class="cls-30" d="M400.7,185.9c0,1-.5,1.7-1.5,1.9-2.7.9-8.1,1.6-12.2,1.9-10,.6-20.1-1.4-20.8-19.1l-1.8-46.3c0-1,.7-1.9,1.6-1.9l12.7-.8c.9,0,1.6.7,1.6,1.7l1.8,46.3c.1,3.6,1.1,4.5,3.9,4.3l12.6-.8c.9,0,1.6.7,1.6,1.7l.4,11.1Z"/>
      <path class="cls-24" d="M453.7,184.6l-13.5.9c-.9,0-1.4-.6-1.6-1.6l-2.7-12.5-13.9.9-1.7,12.8c-.2,1-.6,1.8-1.5,1.8l-13.5.9c-.9,0-1.3-.4-1.4-1.4,0-.3,0-.2,0-.5l8.3-56.4c1.4-9.2,7.2-11.3,14.5-11.8s13.3.9,15.4,9.9l12.6,55.1c0,.3,0,.4,0,.4,0,1-.3,1.5-1.2,1.5ZM428.4,134l-.3-.9c0-.3-.2-.3-.6-.3s-.5.1-.6.4l-.2.9-2.9,23.4,9.3-.6-4.7-22.9Z"/>
      <path class="cls-32" d="M510.3,181l-15.8,1c-.9,0-1.4-.5-2-1.5l-9-15.7-6.9,16.5c-.5,1-.9,1.9-1.8,1.9l-14.6.9c-.6,0-1-.4-1.1-1,0,0,0-.4.1-.7l14.6-34.5-16.9-29.6c0-.3-.2-.4-.2-.6,0-.7.3-1.1,1-1.2l15.9-1c.9,0,1.4.8,1.9,1.6l8.6,15,6.3-15.9c.4-.8.9-1.8,1.8-1.8l14.6-.9c.7,0,1,.3,1.1,1,0,.2,0,.4-.1.6l-14.4,33.2,17.6,30.8c.2.3.3.5.3.8,0,.6-.3,1-1,1.1Z"/>
      <path class="cls-16" d="M562.6,111.8l-10.2,32.2c-1.5,4.6-3.2,7.1-5.2,8.2l.9,24.7c0,1-.6,1.9-1.5,1.9l-12.7.8c-.9,0-1.5-.7-1.5-1.7l-.9-24.7c-2.1-.9-4-3.1-5.8-7.5l-12.6-30.7c0-.4-.2-.6-.2-.7,0-.8.3-1.2,1-1.3l14.1-.9c.9,0,1.3.6,1.6,1.6l8.4,23.7c.3.6.5.9.8.9.3,0,.5-.3.7-1l6.5-24.6c.3-1,.6-1.8,1.5-1.8l14.1-.9c.7,0,1.1.3,1.1,1.1,0,0,0,.4-.1.7Z"/>
    </g>
  </g>
  <g>
    <g>
      <path class="cls-27" d="M84.9,134.1c-.2,1-.9,1.7-2,1.9-3.2.7-8.2,1.6-16,2.1-10.2.7-19.9-1.3-15.9-18.9l6.3-28.3c3.9-17.3,14.8-20.9,25-21.5,7.8-.5,12.5-.2,15.5,0,1,.1,1.4.7,1.2,1.7l-2.5,11c-.2,1-1.1,1.8-2,1.9l-16.2,1.1c-2.9.2-4.1,1.2-4.8,4.8l-1.5,6.6,19.7-1.3c.9,0,1.4.7,1.2,1.7l-2.4,10.6c-.2,1-1.1,1.9-2,1.9l-19.7,1.3-1.7,7.5c-.8,3.5,0,4.4,2.8,4.3l16.2-1.1c.9,0,1.4.7,1.2,1.7l-2.5,11Z"/>
      <path class="cls-4" d="M143.9,129c-.4,1.8-2.1,3.4-3.7,3.5l-10.7.7c-1.4,0-2.3-.6-2.8-2.2l-9-29.2c-.1-.4-.4-.7-.6-.7-.2,0-.5.3-.6.8l-6.9,30.7c-.2,1-1.1,1.9-2,1.9l-12,.8c-.9,0-1.5-.7-1.3-1.7l13.9-61.9c.4-1.8,2.1-3.4,3.7-3.5l10.6-.7c1.5,0,2.1.8,2.6,2.4l9.3,30c.1.5.3.8.5.8.2,0,.4-.1.6-.8l7.2-31.9c.2-1,1.1-1.9,2-1.9l12-.8c.9,0,1.4.7,1.2,1.7l-13.9,61.9Z"/>
      <path class="cls-33" d="M206,75.2c-.2,1-1.2,1.8-2.1,1.8l-11.9.8-11.3,50.2c-.2,1-1.1,1.9-2,1.9l-13,.9c-.9,0-1.4-.7-1.2-1.7l11.3-50.2-12.2.8c-.9,0-1.4-.6-1.2-1.6l2.6-11.5c.2-1,1.1-1.8,2-1.8l40.3-2.6c.9,0,1.5.6,1.3,1.6l-2.6,11.5Z"/>
      <path class="cls-7" d="M248.4,125.4l-14.7,1c-.9,0-1-.7-1.2-1.7l-5.1-21-.5-.3c-.9,0-3.3.1-4.1.2l-5,22.1c-.2,1-1.1,1.9-2,1.9l-13,.8c-.9,0-1.5-.7-1.3-1.7l13.2-59c1-4.3,2.4-5.4,6-6.3,3.4-.7,11.4-1.9,17.1-2.3,15-1,23.8,3.7,20.1,20.2l-.3,1.4c-2.2,9.6-8.1,15.6-13.7,18.4l6.1,23.8c0,.3,0,.7,0,1-.2.9-.8,1.4-1.7,1.4ZM241.8,80.5c.9-4.2-1.2-6.2-6.6-5.9-.6,0-5.7.6-6.2.6l-3.2,14.3c.2,0,1.5,0,2.9,0l3-.2c6.2-.4,8.9-3,9.8-7.2l.3-1.5Z"/>
      <path class="cls-8" d="M295.5,120.3c-.2,1-.9,1.7-2,1.9-3.2.7-8.2,1.6-16,2.1-10.2.7-19.9-1.3-15.9-18.9l6.3-28.3c3.9-17.3,14.8-20.9,25-21.5,7.8-.5,12.5-.2,15.5,0,1,.1,1.4.7,1.2,1.7l-2.5,11c-.2,1-1.1,1.8-2,1.9l-16.2,1.1c-2.9.2-4.1,1.2-4.9,4.8l-1.5,6.6,19.7-1.3c.9,0,1.4.7,1.2,1.7l-2.4,10.6c-.2,1-1.1,1.9-2,1.9l-19.7,1.3-1.7,7.5c-.8,3.5,0,4.4,2.8,4.3l16.2-1.1c.9,0,1.4.7,1.2,1.7l-2.5,11Z"/>
      <path class="cls-28" d="M354.5,115.2c-.4,1.8-2.1,3.4-3.7,3.5l-10.7.7c-1.4,0-2.3-.6-2.8-2.2l-9-29.2c-.1-.4-.4-.7-.6-.7-.2,0-.5.3-.6.8l-6.9,30.7c-.2,1-1.1,1.9-2,1.9l-12,.8c-.9,0-1.5-.7-1.3-1.7l13.9-61.9c.4-1.8,2.1-3.4,3.7-3.5l10.6-.7c1.5,0,2.1.8,2.6,2.4l9.3,30c.1.5.3.8.5.8.2,0,.4-.1.6-.8l7.2-31.9c.2-1,1.1-1.9,2-1.9l12-.8c.9,0,1.4.7,1.2,1.7l-13.9,61.9Z"/>
    </g>
    <g>
      <path class="cls-21" d="M71.2,134.8c-.2,1-.9,1.7-2,1.9-3.2.7-8.2,1.6-16,2.1-10.2.7-19.9-1.3-15.9-18.9l6.3-28.3c3.9-17.3,14.8-20.9,25-21.5,7.8-.5,12.5-.2,15.5,0,1,.1,1.4.7,1.2,1.7l-2.5,11c-.2,1-1.1,1.9-2,1.9l-16.2,1.1c-2.9.2-4.1,1.2-4.8,4.8l-1.5,6.6,19.7-1.3c.9,0,1.4.7,1.2,1.7l-2.4,10.6c-.2,1-1.1,1.8-2,1.9l-19.7,1.3-1.7,7.5c-.8,3.5,0,4.4,2.8,4.3l16.2-1.1c.9,0,1.4.7,1.2,1.7l-2.5,11Z"/>
      <path class="cls-18" d="M130.2,129.7c-.4,1.8-2.1,3.4-3.7,3.5l-10.7.7c-1.4,0-2.3-.6-2.8-2.2l-9-29.2c-.1-.4-.4-.7-.6-.7-.2,0-.5.3-.6.8l-6.9,30.7c-.2,1-1.1,1.9-2,1.9l-12,.8c-.9,0-1.5-.7-1.3-1.7l13.9-61.9c.4-1.8,2.1-3.4,3.7-3.5l10.6-.7c1.5,0,2.1.8,2.6,2.4l9.3,30c.1.5.3.8.5.8.2,0,.4-.1.6-.8l7.2-31.9c.2-1,1.1-1.9,2-1.9l12-.8c.9,0,1.4.7,1.2,1.7l-13.9,61.9Z"/>
      <path class="cls-22" d="M192.3,76c-.2,1-1.2,1.8-2.1,1.8l-11.9.8-11.3,50.2c-.2,1-1.1,1.9-2,1.9l-13,.9c-.9,0-1.4-.7-1.2-1.7l11.3-50.2-12.2.8c-.9,0-1.4-.6-1.2-1.6l2.6-11.5c.2-1,1.1-1.8,2-1.8l40.3-2.6c.9,0,1.5.6,1.3,1.6l-2.6,11.5Z"/>
      <path class="cls-13" d="M234.6,126.2l-14.7,1c-.9,0-1-.7-1.2-1.7l-5.1-21-.5-.3c-.9,0-3.3.1-4.1.2l-5,22.1c-.2,1-1.1,1.9-2,1.9l-13,.8c-.9,0-1.5-.7-1.3-1.7l13.2-59c1-4.3,2.4-5.4,6-6.3,3.4-.7,11.4-1.9,17.1-2.3,15-1,23.8,3.7,20.1,20.2l-.3,1.4c-2.2,9.6-8.1,15.6-13.7,18.4l6.1,23.8c0,.3,0,.7,0,1-.2.9-.8,1.4-1.7,1.4ZM228.1,81.2c.9-4.2-1.2-6.2-6.6-5.9-.6,0-5.7.6-6.2.6l-3.2,14.3c.2,0,1.5,0,2.9,0l3-.2c6.2-.4,8.9-3,9.8-7.2l.3-1.5Z"/>
      <path class="cls-23" d="M281.8,121.1c-.2,1-.9,1.7-2,1.9-3.2.7-8.2,1.6-16,2.1-10.2.7-19.9-1.3-15.9-18.9l6.3-28.3c3.9-17.3,14.8-20.9,25-21.5,7.8-.5,12.5-.2,15.5,0,1,.1,1.4.7,1.2,1.7l-2.5,11c-.2,1-1.1,1.9-2,1.9l-16.2,1.1c-2.9.2-4.1,1.2-4.8,4.8l-1.5,6.6,19.7-1.3c.9,0,1.4.7,1.2,1.7l-2.4,10.6c-.2,1-1.1,1.8-2,1.9l-19.7,1.3-1.7,7.5c-.8,3.5,0,4.4,2.8,4.3l16.2-1.1c.9,0,1.4.7,1.2,1.7l-2.5,11Z"/>
      <path class="cls-9" d="M340.8,116c-.4,1.8-2.1,3.4-3.7,3.5l-10.7.7c-1.4,0-2.3-.6-2.8-2.2l-9-29.2c-.1-.4-.4-.7-.6-.7-.2,0-.5.3-.6.8l-6.9,30.7c-.2,1-1.1,1.9-2,1.9l-12,.8c-.9,0-1.5-.7-1.3-1.7l13.9-61.9c.4-1.8,2.1-3.4,3.7-3.5l10.6-.7c1.5,0,2.1.8,2.6,2.4l9.3,30c.1.5.3.8.5.8.2,0,.4-.1.6-.8l7.2-31.9c.2-1,1.1-1.9,2-1.9l12-.8c.9,0,1.4.7,1.2,1.7l-13.9,61.9Z"/>
    </g>
  </g>
</svg>