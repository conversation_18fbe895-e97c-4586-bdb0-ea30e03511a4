* {
    box-sizing: border-box;
  }
  
  html {
    min-height: 100vh;
    display: grid;
    font-family: "Courier New", Courier, monospace;
  }
  
  .maintenance-wrapper {
    height: 100%;
    background: linear-gradient(45deg, var(--color-primary-darkest), var(--color-primary));
    padding: 3rem;
  }
  
  .maintenance {
    max-width: 1200px;
    margin: 1rem auto 0;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto auto 1fr;
    grid-column-gap: 0;
    grid-row-gap: 1rem;
    place-content: center;
    justify-items: center;
  
    text-align: center;
    color: #fff;
  }
  
  .logo {
    grid-area: 1 / 1 / 2 / 3;
    background: transparent $logo-image no-repeat center center / contain;
    height: $logo-height;
    width: 100%;
  }
  
  .icon {
    grid-area: 2 / 1 / 3 / 3;
    margin-bottom: 2rem;
  
    .icon-wrapper {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
  
      svg {
        height: 100px;
      }
    }
  }
  
  .message {
    padding: 2rem;
  }
  
  .message-es {
    grid-area: 3 / 1 / 4 / 2;
    margin-bottom: 1rem;
    border-right: 1px solid var(--color-primary-lighter);
  }
  
  .message-en {
    grid-area: 3 / 2 / 4 / 3;
    margin-bottom: 1rem;
  }
  
  
  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }
  
  p {
    font-size: 1.125rem;
  }
  
  
  @media (max-width: 768px) {
    .maintenance-wrapper {
      padding-top: 1rem;
    }
  
    .maintenance {
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto auto;
      row-gap: 0;
    }
  
    .icon {
      margin-bottom: 0;
      .icon-wrapper svg {
          height: 80px;
      }
    }
  
    .message {
      padding: 1rem ;
    }
    .message-es {
      grid-area: 3 / 1 / 4 / 2;
      border-right: none;
      border-bottom: 1px solid var(--color-primary-lighter);
      margin-bottom: 0;
    }
  
    .message-en{
      grid-area: 4 / 1 / 5 / 2;
      margin-bottom: 1rem;
    }
  }