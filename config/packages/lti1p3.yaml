lti1p3:
    scopes:
        - 'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
        - 'https://purl.imsglobal.org/spec/lti-ags/scope/result.readonly'
        - 'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem'
        - 'https://purl.imsglobal.org/spec/lti-ags/scope/score'
    key_chains:
        platformKey:
            key_set_name: "platformSet"
            public_key: "file://%kernel.project_dir%/config/secrets/lti/public.key"
            private_key: "file://%kernel.project_dir%/config/secrets/lti/private.key"
            private_key_passphrase: null
    platforms:
        #only one platform  - make reference to server url and generic name
        learnflixPlatform:
            name: "Local platform"
            audience: '%app.url_server%'
            oidc_authentication_url: "%app.url_server%/lti1p3/oidc/authentication"
            oauth2_access_token_url: "%app.url_server%/lti1p3/auth/platformKey/token"
