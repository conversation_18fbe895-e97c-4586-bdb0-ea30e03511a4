security:
  password_hashers:
    App\Entity\User:
      algorithm: auto
      cost: 4 # Lowest possible value for bcrypt
      time_cost: 3 # Lowest possible value for argon
      memory_cost: 10 # Lowest possible value for argon
    App\Entity\AnnouncementInspectorAccess:
      algorithm: auto
      cost: 4 # Lowest possible value for bcrypt
      time_cost: 3 # Lowest possible value for argon
      memory_cost: 10 # Lowest possible value for argon