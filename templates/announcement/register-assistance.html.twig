{% extends 'base.html.twig' %}

{% block title %}
  Assistance
{% endblock %}

{% block stylesheets %}
  {{ parent() }}
  {{ encore_entry_link_tags('app') }}
{% endblock %}

{% block body %}
  <div id="login" class="activeAccount">
    <div class="contentAccount">
       {{ include('reset_password/header.html.twig') }}
      <div>
      <br />
        <h2>{{ 'email.template_email.greet'|trans({}, 'email', announcementUser.user.locale) }} <strong>{{ announcementUser.user.firstName }}</strong></h2><br />
        {% if confirmation == 'yes' %}
          <p class="h4 text-primary">
            <i class="fas fa-check-circle"></i>  {{ 'email.template_email.confirmation_assistance'|trans({}, 'email', announcementUser.user.locale) }}
          </p><br />
        {% else %}
          <p class="h4 text-info">
            <i class="fas fa-exclamation-circle"></i>{{ 'email.template_email.no_assistance'|trans({}, 'email', announcementUser.user.locale) }}
          </p><br />
        {% endif %}
      </div>

      <div class="d-grid gap-2">
        <button class="btn btn-secondary" onclick="window.close()">{{ 'email.template_email.close'|trans({}, 'email', announcementUser.user.locale) }}</button> 
        <a class="btn btn-primary" href="/campus">{{ 'email.template_email.go_campus'|trans({}, 'email', announcementUser.user.locale) }}</a>
      </div>
    </div>
  </div>
{% endblock %}
