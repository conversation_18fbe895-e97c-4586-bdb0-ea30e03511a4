<div class="course-data">
  <h2>{{ 'course.panel.class'|trans({}, 'messages', app.user.locale) }}</h2>
  <ul>
    <li>
      <strong>{{ 'course.label_in_singular'|trans({}, 'messages', app.user.locale) }} :</strong>
      {{ announcement.course.name }}
    </li>
    <li>
      <strong>{{ 'taskCourse.configureFields.startDate'|trans({}, 'messages', app.user.locale) }} :</strong>
      {{ announcement.startAt|date('d/m/Y') }}
    </li>
    <li>
      <strong>{{ 'stats.export.end_date'|trans({}, 'messages', app.user.locale) }}:</strong>
      {{ announcement.finishAt|date('d/m/Y') }}
    </li>
    <li>
      <strong>{{ 'announcements.client.iberostar.client_contact_point'|trans({}, 'messages', app.user.locale) }}:</strong>
      {{ announcement.contactPerson }}
    </li>
    <li>
      <strong>{{ 'announcements.client.imq.telephone'|trans({}, 'messages', app.user.locale) }}:</strong>
      {{ announcement.contactPersonTelephone }}
    </li>
    <li>
      <strong>{{ 'user.configureFields.email'|trans({}, 'messages', app.user.locale) }}:</strong>
      {{ announcement.contactPersonEmail }}
    </li>
    <li>
      <strong>{{ 'announcements.configureFields.max_users'|trans({}, 'messages', app.user.locale) }}:</strong>
      {{ announcement.maxUsers }}
    </li>

    <li>
      <strong>{{ 'announcements.configureFields.total_hours'|trans({}, 'messages', app.user.locale) }}:</strong>
      {{ announcement.totalHours }}
    </li>
  </ul>
</div>
