{% macro menu_links(menu, folder) %}
    <ul>
        {% for link in menu.submenu %}
            {% if link.href != '' %}
                <li class="link"><a class="frmOpnr mnlvl" href="" data-dest="{{ folder }}/{{ link.href }}">{{ link.title }}</a>
            {% else %}
                <li>{{ link.title }}
            {% endif %}
            {% if link.submenu %}
                {{ _self.menu_links(link, folder) }}
            {% endif %}
            </li>
        {% endfor %}
    </ul>
{% endmacro %}

{% if scorm is defined %}
    {%  for menu in menus %}
        <div class="card m-2 shadow-sm">
            <div class="card-header">
                {{ menu.title }}
            </div>
            <div class="card-body d-none d-md-block">
                {{ _self.menu_links(menu, scorm.folder) }}
            </div>
        </div>
    {% endfor %}
{% endif %}
