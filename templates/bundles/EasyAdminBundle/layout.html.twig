{% trans_default_domain ea.i18n.translationDomain %}

<!DOCTYPE html>
<html lang="{{ ea.i18n.htmlLocale }}">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noodp, noimageindex, notranslate, nocache"/>
		<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
		<meta name="generator" content="EasyAdmin"/>

		<title>
			{% block page_title %}
				{{ block('content_title')|striptags|raw }}
			{% endblock %}
		</title>

		{% block head_stylesheets %}
			<link rel="stylesheet" href="{{ asset('bundles/easyadmin/app.css') }}">
			{{ encore_entry_link_tags('app') }}
			{#        {{ encore_entry_link_tags('tags') }}#}
		{% endblock %}

		{% block configured_stylesheets %}
			{% for css_asset in ea.assets.cssFiles ?? [] %}
				<link rel="stylesheet" href="{{ asset(css_asset) }}">
			{% endfor %}
		{% endblock %}

		{% block head_favicon %}
			<link rel="shortcut icon" href="{{ asset(ea.dashboardFaviconPath) }}">
		{% endblock %}

		{% block head_javascript %}
			<script src="{{ asset('bundles/easyadmin/app.js') }}"></script>
		{% endblock head_javascript %}

		{% if 'rtl' == ea.i18n.textDirection %}
			<link rel="stylesheet" href="{{ asset('bundles/easyadmin/app-rtl.css') }}">
		{% endif %}

		{% block configured_head_contents %}
			{% for htmlContent in ea.assets.headContents ?? [] %}
				{{ htmlContent|raw }}
			{% endfor %}
		{% endblock %}
	</head>

	{% block body %}
		{% block globalVariable %}
			{{ include('/bundles/EasyAdminBundle/globalTranslationsVue.html.twig')}}
		{% endblock %}

		<body id="{% block body_id %}{% endblock %}" class="ea {% block body_class %}{% endblock %}">
			<script>
				document.body.classList.add('ea-content-width-' + (
localStorage.getItem('ea/content/width') || 'normal'
), 'ea-sidebar-width-' + (
localStorage.getItem('ea/sidebar/width') || 'normal'
));
			</script>

			{% block wrapper_wrapper %}
				{% block flash_messages %}
					{{ include(ea.templatePath('flash_messages')) }}
					{% for label, messages  in app.flashes(['success', 'danger','warning']) %}
						{% for message in messages %}
							<div class="flash-{{ label }}">
								{{ message }}
							</div>
						{% endfor %}
					{% endfor %}
				{% endblock flash_messages %}

				<div class="wrapper">
					{% block wrapper %}
						<header class="main-header px-3 pb-3">
							{% block header %}
								<button id="navigation-toggler" type="button" aria-label="Toggle navigation">
									<i class="fa fa-fw fa-bars"></i>
								</button>

								<nav class="navbar" role="navigation">
									<div id="header-logo">
										{% block header_logo %}
											<a class="logo {{ ea.dashboardTitle|length > 14 ? 'logo-long' }} py-0" title="{{ ea.dashboardTitle|striptags }}" href="{{ path(ea.dashboardRouteName) }}">
												{{ ea.dashboardTitle|raw }}
											</a>
										{% endblock header_logo %}
									</div>
								</nav>

								{% set _user_menu_content %}
								<div class="popover-content-section user-details {{ ea.userMenu.items|length > 0 ? 'user-has-actions' }}">
									<p class="small text-muted mb-0">{{ 'user.logged_in_as'|trans(domain = 'EasyAdminBundle') }}</p>
									<p class="user-details-name">
										{{ ea.user is null ? 'user.anonymous'|trans(domain = 'EasyAdminBundle') : ea.userMenu.name }}
									</p>
								</div>

								{% block user_menu %}
									{% if ea.userMenu.items|length > 0 %}
										<div class="popover-content-section user-menu">
											{% for item in ea.userMenu.items %}
												<a href="{{ item.linkUrl }}" class="user-action {{ item.cssClass }}" target="{{ item.linkTarget }}" rel="{{ item.linkRel }}" referrerpolicy="origin-when-cross-origin">
													{% if item.icon is not empty %}
														<i class="fa fa-fw {{ item.icon }}"></i>
													{% endif %}
													<span>{{ item.label }}</span>
												</a>
											{% endfor %}
										</div>
									{% endif %}
								{% endblock user_menu %}
								{% endset %}

								<div class="content-top py-0">
									{% set has_search = ea.crud is not null and ea.crud.isSearchEnabled %}
									<aside class="content-top {{ has_search ? 'ea-search-enabled' : 'ea-search-disabled' }} pr-1 py-0">
										{% block content_top_header %}
											<div class="content-search">
												{% if has_search %}
													{% block search %}
														<form class="form-action-search" method="get">
															{% block search_form %}
																{% block search_form_filters %}
																	{% for field, array in ea.search.appliedFilters %}
																		{% for key, value in array %}
																			{# This code re-applies your filters on searches, an iterable check is needed in cases we have more than one object for a filter #}
																			{% if value is iterable %}
																				{% for index, iterValue in value %}
																					<input type="hidden" name="filters[{{ field }}][{{ key }}][{{ index }}]" value="{{ iterValue }}">
																				{% endfor %}
																			{% else %}
																				<input type="hidden" name="filters[{{ field }}][{{ key }}]" value="{{ value }}">
																			{% endif %}
																		{% endfor %}
																	{% endfor %}
																{% endblock %}

																<input type="hidden" name="crudAction" value="index">
																<input type="hidden" name="crudControllerFqcn" value="{{ ea.request.query.get('crudControllerFqcn') }}">
																<input type="hidden" name="menuIndex" value="{{ ea.request.query.get('menuIndex') }}">
																<input type="hidden" name="submenuIndex" value="{{ ea.request.query.get('submenuIndex') }}">
																<input type="hidden" name="page" value="1">
																<input type="hidden" name="signature" value="{{ ea_url().unsetAll().setAction('index').setController(ea.request.query.get('crudControllerFqcn')).getSignature() }}">
																<div class="form-group mb-0">
																	<div class="form-widget">
																

																	{% if ea.request.query.get('crudControllerFqcn') == 'App\\Controller\\Admin\\UserCrudController' or ea.request.query.get('crudControllerFqcn') == 'App\\Controller\\Admin\\CourseCrudController' or ea.request.query.get('crudControllerFqcn') == 'App\\Modules\\Courses\\Controller\\CoursesAppCrudController' or ea.request.query.get('crudControllerFqcn') == 'App\\Controller\\Admin\\ItineraryCrudController'  or ea.request.query.get('crudControllerFqcn') == 'App\\Controller\\Admin\\ManagerCrudController'  %}
																		<i class="fas fa-search content-search-icon"></i>

																		<label class="content-search-label" data-value="{{ app.request.get('query') }}">
																			<input class="form-control {{ app.request.get('query') is null ? 'is-blank' }}" type="search" name="query" value="{{ app.request.get('query') ?? '' }}" placeholder="{{ 'action.search'|trans(ea.i18n.translationParameters, 'EasyAdminBundle') }}" spellcheck="false" autocorrect="false" oninput="this.parentNode.dataset.value=this.value">
																		</label>
																	{% endif %}

																		{% if app.request.get('query') %}
																			<a href="{{ ea_url().unset('query') }}" class="content-search-reset" style="position: absolute; left: 220px; bottom: 25px; background-color: transparent;" >
																				<i class="fas fa-fw fa-times"></i>
																			</a>
																		{% endif %}
																	</div>
																</div>
															{% endblock %}
														</form>
													{% endblock search %}
												{% endif %}
											</div>

											<div class="navbar-custom-menu">
												{% block header_custom_menu %}
													{% if constant('Symfony\\Component\\Security\\Core\\Authorization\\Voter\\AuthenticatedVoter::IS_IMPERSONATOR') is defined %}
														{% set impersonator_permission = 'IS_IMPERSONATOR' %}
													{% else %}
														{% set impersonator_permission = 'ROLE_PREVIOUS_ADMIN' %}
													{% endif %}

													<div class="user user-menu-wrapper {{ is_granted(impersonator_permission) ? 'user-is-impersonated' }}" data-toggle="popover" data-placement="bottom" data-container=".user-menu-wrapper" data-content="{{ _user_menu_content|e('html_attr') }}" data-html="true">
														{% if ea.userMenu.isAvatarDisplayed %}
															{% if null == ea.userMenu.avatarUrl %}
																<i class="fa fa-fw {{ ea.user is not null ? 'fa-user-circle' : 'fa-user-times' }} user-avatar"></i>
															{% else %}
																<img class="user-avatar" src="{{ ea.userMenu.avatarUrl }}"/>
															{% endif %}
														{% endif %}
														{% if ea.userMenu.isNameDisplayed %}
															<span class="user-name">{{ ea.userMenu.name }}</span>
														{% endif %}
													</div>
												{% endblock header_custom_menu %}

												<div class="dropdown ml-3">

													<button class="btn btn-secondary" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasRight" aria-controls="offcanvasRight">
														<i class="fa fa-globe"></i>
														{# {{ app.user.locale }} #}
													</button>

													<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight" aria-labelledby="offcanvasRightLabel" style="height: 32vh;margin-left: auto;z-index:9999;top:3rem;right:10%;max-width:310px;">
														<div class="offcanvas-header">
															<h5 id="offcanvasRightLabel">{{ 'user.configureFields.configureLocale'|trans({}, 'messages') }}</h5>
															<button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
														</div>
														<div class="offcanvas-body">
															<div class="row">
																<div class="col-md-12">
																	<p>{{ 'user.configureFields.configureLocaleAdmin'|trans({}, 'messages') }}</p>
																	<button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">

																		{% if languagesName[app.user.locale] is defined and languagesName[app.user.locale] %}
																			{{ languagesName[app.user.locale] }}
																		{% else %}
																			{{ app.user.locale }}
																		{% endif %}

																	</button>
																	<ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1" id="changeLangues">
																		{% for lang in languagesAdmin %}

																			<li>
																				<a class="dropdown-item" href="#" data-lang="{{ lang }}">
																					{% if languagesName[lang] is defined and languagesName[lang] %}
																						{{ languagesName[lang] }}

																					{% else %}
																						{{ lang }}
																					{% endif %}

																				</a>
																			</li>
																		{% endfor %}
																	</ul>
																</div>

																<div class="col-md-12">
																	<br>
																	<p>{{ 'user.configureFields.configureLocaleCampus'|trans({}, 'messages') }}</p>
																	<button
																			class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
																		{# <i class="fa fa-globe"></i> #}
																		{{ languagesName[app.user.localeCampus] }}
																	</button>
																	<ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1" id="changeLocaleCampus">
																		{% for lang in languagesCampus %}
																			<li>
																				<a class="dropdown-item" href="#" data-lang="{{ lang }}">{{ languagesName[lang] }}</a>
																			</li>
																		{% endfor %}
																	</ul>
																</div>
															</div>
														</div>
													</div>

												</div>
											</div>
										{% endblock content_top_header %}
									</aside>
								</div>

							{% endblock header %}
						</header>

						<aside class="main-sidebar">
							{% block sidebar %}
								<section class="sidebar">
									{% block main_menu_wrapper %}
										{{ include(ea.templatePath('main_menu')) }}
									{% endblock main_menu_wrapper %}
								</section>

							{% endblock sidebar %}
						</aside>

						<div class="content-wrapper">
							<div id="sidebar-resizer-handler" class="resizer-handler resizer-handler-left"></div>

							{% block content %}
								<div class="content">
									{% block content_header_wrapper %}
										{% set has_help_message = (ea.crud.helpMessage ?? '') is not empty %}
										<section class="content-header">
											{% block content_header %}
												<div class="d-flex flex-row justify-content-between align-content-center w-100">
													<div class="content-header-title">
														<h1 class="title">
															{% block content_title %}{% endblock %}

															{% block content_help %}
																{% if has_help_message %}
																	<span class="content-header-help">
																		<i class="far fa-question-circle" data-toggle="tooltip" title="{{ ea.crud.helpMessage|e('html_attr') }}"></i>
																	</span>
																{% endif %}
															{% endblock %}
														</h1>
													</div>

													{% block page_actions_wrapper %}
														<div class="page-actions">
															{% block page_actions %}{% endblock %}
														</div>
													{% endblock %}
												</div>
											{% endblock content_header %}
										</section>
									{% endblock content_header_wrapper %}

									<section id="main" class="content-body">
										{% block main %}{% endblock %}
									</section>

									{% block content_footer_wrapper %}
										{% set content_footer = block('content_footer') is defined ? block('content_footer') : '' %}
										{% if content_footer is not empty %}
											<section class="content-footer">
												{{ content_footer }}
											</section>
										{% endif %}
									{% endblock %}
								</div>
							{% endblock content %}

							{# <div id="content-resizer-handler" class="resizer-handler resizer-handler-right"></div> #}
						</div>
					{% endblock wrapper %}
				</div>
			</body>
		</html>
	</div>
{% endblock wrapper_wrapper %}

{% block body_javascript %}
	{{ encore_entry_script_tags('app') }}
	{#        {{ encore_entry_script_tags('tags') }}#}
{% endblock body_javascript %}

{% block configured_javascripts %}
	{{ include('@EasyAdmin/includes/_js_assets.html.twig', { assets: ea.assets.jsAssets ?? [] }, with_context = false) }}
	{{ include('@EasyAdmin/includes/_encore_script_tags.html.twig', { assets: ea.assets.webpackEncoreAssets ?? [] }, with_context = false) }}

	{% for js_asset in ea.assets.jsFiles ?? [] %}
		<script src="{{ asset(js_asset) }}"></script>
	{% endfor %}
{% endblock %}

{% block configured_body_contents %}
	{% for htmlContent in ea.assets.bodyContents ?? [] %}
		{{ htmlContent|raw }}
	{% endfor %}
{% endblock %}</body>{% endblock body %}</html>
s