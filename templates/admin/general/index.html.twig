{% extends '@!EasyAdmin/crud/index.html.twig' %}

{% block content_title %}
	<div class="d-flex justify-content-between flex-wrap align-items-center">
		<div>
			{{ parent() }}
		</div>

		<div>
			{% if entity != 'Nps' %}
				<a href="{{ ea_url().unsetAll().setController(controller).setAction('new') }}" type="button" class="btn btn-primary">
					{{ 'common_areas.create'|trans({}, 'messages', app.user.locale) }}
					{{ translateEntity |trans({}, 'messages', app.user.locale) }}
				</a>
			{% endif %}

			{% if video %}
				<button type="button" class="btn btn-secondary" data-toggle="modal" data-target=".bd-example-modal-video">
					<i class="fab fa-youtube"></i>
				</button>
			{% endif %}

			{% if pdf %}
				<a class="btn btn-secondary" href="/assets/pdf_help/{{ pdf_archive |trans({}, 'messages', app.user.locale) }}.pdf" target="blank">
					<i class="fas fa-question-circle"></i>
				</a>
			{%  endif %}
		</div>
	</div>
{% endblock content_title %}

{% block main %}
	{{ parent() }}

	<div class="modal fade bd-example-modal-video" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div style="padding:56.25% 0 0 0;position:relative;">
					<iframe src="https://player.vimeo.com/video/{{ identifierVideo |trans({}, 'messages', app.user.locale) }}?texttrack={{locale}}&amp;badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen style="position:absolute;top:0;left:0;width:100%;height:100%;" title="help"></iframe>
				</div>
				<script src="https://player.vimeo.com/api/player.js"></script>

			</div>
		</div>
	</div>
{% endblock main %}
