{% extends '@!EasyAdmin/page/content.html.twig' %}

{% block head_stylesheets %}
	{{ parent() }}
	{{ encore_entry_link_tags('managerEdit') }}
{% endblock %}

{% block page_title %}
	{{ 'course.configureFields.access_level'|trans({}, 'messages',  app.user.locale) }}
{% endblock page_title %}

{% block page_actions %}
	<a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\CourseCrudController').setAction('detail').setEntityId(course.id) }}" class="btn btn-secondary">{{ 'course.back_to_course'|trans({}, 'messages',  app.user.locale) }}</a>
{# 	<button type="button" class="btn btn-primary" @click="save">{{ 'common_areas.save'|trans({}, 'messages',  app.user.locale) }}</button> #}
{% endblock page_actions %}


{% block content %}
	<div id="access-level">{{ parent() }}</div>
{% endblock content %}


{% block main %}

	{% if user_use_filters %}
		<div class="col-12">
			<h5>{{ 'filter.label_in_plural'|trans({}, 'messages', app.user.locale) }} </h5>
			<category-filter :save-in-realtime="true"
							 :use-rest-methods="true"
							 url-selected-filters="/admin/courses/{{ course.id }}/load-filters"
							 url-add-filter="/admin/courses/{{ course.id }}/filter"
							 url-remove-filter="/admin/courses/{{ course.id }}/filter"
                             filters-add-all="{{ 'filters.add_all' |trans({}, 'messages', app.user.locale)|e('js') }}"
                             filters-remove-all="{{ 'filters.remove_all' |trans({}, 'messages', app.user.locale)|e('js') }}"
                             filters-search="{{ 'filters.placeholder' |trans({}, 'messages', app.user.locale)|e('js') }}"
			></category-filter>
		</div>
	{% else %}
		<div class="content-panel p-2">
			<h5>{{ 'fundae_catalogs.user_professional_category.label_in_plural'|trans({}, 'messages') }}</h5>
			<div class="card mb-3 p-2">
				<div class="row">
					<div class="col-md-6">
						<div class="mb-2">
							<div class="input-group">
								<input class="form-control py-2 border-right-0 border" type="search" value="search" id="example-search-input" v-model="searchProfessionalCategory">
								<span class="input-group-append">
									<button class="btn btn-outline-secondary border-left-0 border" type="button" @click="searchProfessionalCategory = ''" :disabled="searchProfessionalCategory.length === 0">
										<i class="fa fa-times"></i>
									</button>
								</span>
							</div>
						</div>
						<div class="list-group" >
							<a href="#" v-for="professionalCategory in filteredProfessionalCategories" class="list-group-item list-group-item-action p-2" :class="{'list-group-item-primary': professionalCategory.parent === null}" @click="addProfessionalCategoryToCourse(professionalCategory)">
								${ professionalCategory.code}: ${ professionalCategory.name}
							</a>
						</div>
					</div>

					<div class="col-md-6 mt-5">
						<div v-show="!courseProfessionalCategories.length" class="text-muted text-center">
							{{ 'fundae_catalogs.user_professional_category.not_label_in_plural'|trans({}, 'messages') }}
						</div>
						<div class="list-group"  v-show="courseProfessionalCategories.length">
							<a href="#" v-for="professionalCategory in courseProfessionalCategories" class="list-group-item list-group-item-action p-2  d-flex justify-content-between align-items-center" :class="{'list-group-item-primary': professionalCategory.parent === null}" @click="removeProfessionalCategoryFromCourse(professionalCategory)">
								${ professionalCategory.code}: ${ professionalCategory.name}

								<span class="badge badge-danger badge-pill">
									<span class="fas fa-times"></span>
								</span>
							</a>
						</div>

					</div>
				</div>
			</div>
		</div>
	{% endif %}

	<div class="content-panel p-2">
		<h5>{{ 'menu.users_managment.managers'|trans({}, 'translations') }}</h5>
		<div class="card mb-3 p-2">
			<div class="row">
				<div class="col-md-6">
					<div class="mb-2">
						<div class="input-group">
							<input class="form-control py-2 border" type="search" value="search" id="example-search-input" v-model="searchManager" placeholder="Searh Manager">
							{# <span class="input-group-append">
								<button class="btn btn-outline-secondary border-left-0 border" type="button" @click="searchManager = ''" :disabled="searchManager.length === 0">
									<i class="fa fa-times"></i>
								</button>
							</span> #}
						</div>

					</div>
					<div class="list-group list-category" >
						<a href="#" v-for="manager in filteredManagers" class="list-group-item list-group-item-action p-2 d-flex justify-content-between align-items-center" @click="addManagerToCourse(manager)">
							${ manager.fullName} - ${ manager.email }
							<span class="badge badge-success badge-pill">
								<i class="fa fa-plus text-white" ></i>
							</span>
						</a>
					</div>

				</div>

				<div class="col-md-6 mt-5">
					<div v-show="!courseManagers.length" class="text-muted text-center">
						{{ 'menu.users_managment.nomanagers'|trans({}, 'translations') }}
					</div>
					<div class="list-group list-category"  v-show="courseManagers.length">
						<a href="#" v-for="manager in courseManagers" class="list-group-item list-group-item-action p-2  d-flex justify-content-between align-items-center" @click="removeManagerFromCourse(manager)">
							${ manager.fullName} - ${ manager.email }
							<span class="badge badge-danger badge-pill">
								<span class="fas fa-times"></span>
							</span>
						</a>
					</div>

				</div>
			</div>
		</div>
	</div>
{% endblock main %}

{% block body_javascript %}
	{{ parent() }}
	<script type="text/javascript">
		let courseId = {{ course.id }};
	</script>
	{{ encore_entry_script_tags('accessLevel') }}
{% endblock %}
