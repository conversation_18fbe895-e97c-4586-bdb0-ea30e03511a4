
{% extends '@!EasyAdmin/page/content.html.twig' %}


{% block body_id %}settings{% endblock %}

{% block page_title %}
    {{ 'settings.header.title'|trans({}, 'messages',  app.user.locale) }}
{% endblock page_title %}

{% block page_actions %}
    <button class="btn btn-primary" @click="save">{{'common_areas.save'|trans({}, 'messages') }} </button>
{% endblock %}
{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('settings') }}
{% endblock %}

{% block body_javascript %}
    {{ parent() }}
    {{ encore_entry_script_tags('settings') }}
{% endblock %}

{% block main %}
    <div id="settings-wrapper">
        <form id="settings-form">
            <div class="content">
                <div
                    class="mb-4"
                    v-for="group in settingGroups"
                >
                    <template v-if="group.settings.length">
                        <h2>${group.title}</h2>

                        <div class="row">
                            <div class="col-md-4 mb-3" v-for="setting in group.settings">
                                <setting :setting="setting" />
                            </div>
                        </div>

                    </template>
                </div>
            </div>
            <div class="col-12">
                <add-remove
                        :title="$t('TIMEZONE')"
                        :source-items="timezones"
                        v-model="selectedTimezones"
                        :realtime="false"
                ></add-remove>
            </div>
        </form>
    </div>
{% endblock main %}
