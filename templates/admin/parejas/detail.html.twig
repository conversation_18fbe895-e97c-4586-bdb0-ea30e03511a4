{% block head_stylesheets %}
  {{ encore_entry_link_tags('parejas') }}
{% endblock %}

{% block main %}
  <div id="parejas">
    <parejasgame :blocks="{{ blocks }}" :parejaimagenes="{{ parejaImagenes }}" :chapter-id="{{ chapterId }}"></parejasgame>
  </div>
{% endblock %}
<script>
	let typeChapter =  {{ chapter.type.id | json_encode | raw }};
	let chapterId = {{ chapter.id | json_encode | raw }};
	let messageMinimumQuestion = {{ 'message_api.alert.minimal_pair'|trans({'%number%': chapter.getMinimumQuestions}, 'message_api', app.user.locale)| json_encode |raw }};
    let minimalQuestion = {{ chapter.getMinimumQuestions | json_encode | raw }};

</script>

{% block body_javascript %}
  {{ encore_entry_script_tags('parejas') }}
{% endblock %}
