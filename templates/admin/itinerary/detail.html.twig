{% extends '@!EasyAdmin/crud/detail.html.twig' %}

{% block content_title %}
{{ itinerary.name }}
{% endblock content_title %}

{% block head_stylesheets %}
{{ parent() }}

<style>
.modal-header{
  background-color: var(--color-neutral-darker);
  color: #ffff;
}

</style>
{{ encore_entry_link_tags('itinerary') }}
{% endblock %}


{% block content %}
    <div id="itinerary-app" itinerary-id="{{ itinerary.id }}"
         image-base-path="{{ asset(course_uploads_path) }}"
         translations="{{ translations | json_encode }}"
         locale="{{ app.user.locale }}"
    >{{ parent() }}</div>
{% endblock content %}

{% block main %}
{{ parent() }}
<div class="row itinerary">
  <div class="parent-data">
    {{ parent() }}
  </div>

  <div class="col-12 itinerary-stats">
            <box-info :name="usersTotalRaw   + '{{ 'itinerary.chart.users_title'|trans({}, 'messages',  app.user.locale)}}' + ' ' +  {{ totalUsers}} " :type="2" icon="fas fa-map-marker-alt">
      <template v-slot:title>
                    <loader v-if="loadingUsersRaw" :is-loaded="loadingUsersRaw"></loader>
        <div v-else>
          <a
            data-bs-toggle="modal"
            data-bs-target="#staticBackdrop"
            v-on:click="getStateProcess('COMPLETED')"
            style="cursor: pointer"
          >
            <b>${ usuariosRawItinerarioCompletado }</b>

            <span style="text-decoration: underline">
              {{ 'itinerary.chart.users'|trans({}, 'messages',  app.user.locale) }}
            </span>
          </a>
        </div>
      </template>
      <template v-slot:subtitle>
        <div
          v-if="!loadingUsersRaw"
          class="d-flex gap-1 flex-wrap"
        >
          <div>
            <a
              data-bs-toggle="modal"
              data-bs-target="#staticBackdrop"
              v-on:click="getStateProcess('IN_PROCESS')"
              style="cursor: pointer"
            >
              <b>${ usuariosRawItinerarioProceso }</b>
              <span style="text-decoration: underline"> 
                {{ 'itinerary.chart.users_process'|trans({}, 'messages',  app.user.locale) }}
              </span>
            </a>
          </div>

          <div style="color: var(--color-neutral-mid-darker)">
            <a
              data-bs-toggle="modal"
              data-bs-target="#staticBackdrop"
              v-on:click="getStateProcess('NOT_STARTED')"
            >
              +
              <b>
                ${ usersTotalRaw - (usuariosRawItinerarioCompletado + usuariosRawItinerarioProceso) }
              </b>
              <span style="cursor: pointer; text-decoration: underline">
                {{ 'itinerary.chart.users_incomplete'|trans({}, 'messages',  app.user.locale) }}
              </span>
            </a>
          </div>
        </div>
      </template>
    </box-info>

    <box-info name="{{ 'itinerary.chart.total_time'|trans({}, 'messages',  app.user.locale) }}" :type="6" icon="fas fa-clock">
      <template v-slot:title>
                    <loader v-if="loadingTimeSpent" :is-loaded="loadingTimeSpent"></loader>
                    <div v-else v-html="totalTimeFormatted"></div>
      </template>
    </box-info>

    <box-info name="{{ 'itinerary.chart.avg_time'|trans({}, 'messages',  app.user.locale) }}" :type="3" icon="fas fa-clock">
      <template v-slot:title>
                    <loader v-if="loadingTimeSpent" :is-loaded="loadingTimeSpent"></loader>
                    {#<div v-else v-html="avgTimeFormatted"></div> #}
                     <div v-if="!loadingTimeSpent" :is-loaded="loadingTimeSpent" class="d-flex gap-1 flex-wrap">
                        <div style="clear:right">
                          <div style="float:left" v-html="avgTimeFormatted"></div>
                          <div style="text-align: right;float: right; background:none" class="name"><span>{{ 'itinerary.chart.avg_time_active'|trans({}, 'messages',  app.user.locale) }}</span></div>
                        </div>
                        <div style="clear:right">
                          <div style="float:left" v-html="avgAllTimeFormatted"></div>
                          <div style="text-align: right;float: right; background:none" class="name"><span>{{ 'itinerary.chart.avg_time_all'|trans({}, 'messages',  app.user.locale) }}</span></div>
                        </div>
                     </div>
      </template>
    </box-info>
  </div>

  <div class="col-md-12">
            <ul class="nav nav-tabs" id="itinerary-tabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link"
                   
                     :class="tab === 'courses' ? 'active' : ''" 
                    id="courses-tab" data-toggle="tab" href="#courses" role="tab" 
                    aria-controls="courses" aria-selected="true"
                      v-on:click="changeTab('courses')"
                    
                     >
          {{ 'itinerary.tab.courses'|trans({}, 'messages',  app.user.locale) }}
        </a>
      </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" :class="tab === 'users' ? 'active' : ''" id="courses-tab" data-toggle="tab" href="#users" role="tab" aria-controls="users" aria-selected="false"   v-on:click="changeTab('users')">
                        {{ 'itinerary.tab.users'|trans({}, 'messages',  app.user.locale) }}
                    </a>
                </li>
                {% if not app.user.isTeamManager %}
                <li class="nav-item" role="presentation">
                    <a class="nav-link" :class="tab === 'managers' ? 'active' : ''" id="courses-tab" data-toggle="tab" href="#managers" role="tab" aria-controls="managers" aria-selected="false" v-on:click="changeTab('managers')">
          {{ 'menu.users_managment.managers'|trans({}, 'messages',  app.user.locale) }}
        </a>
      </li>
               <!--
                   <li class="nav-item" role="presentation">
                    <a class="nav-link" :class="tab === 'filters' ? 'active' : ''" id="filters-tab" data-toggle="tab" href="#filters" role="tab" aria-controls="filters" aria-selected="false">
                        ${ $t('ITINERARY.FILTERS.LABEL') }
                    </a>
                </li>
                -->
                {% endif %}
                <li class="nav-item" role="presentation">
                    <a class="nav-link" :class="{'active': tab === 'statistics'}" id="statistics-tab" data-toggle="tab" href="#statistics" role="tab" aria-controls="statistics" aria-selected="false" v-on:click="changeTab('statistics')">
          {{ 'user.configureFields.stats'|trans({}, 'messages') }}
        </a>
      </li>
    </ul>
  
            <div class="row nav-content bg-white" id="itinerary-content">
            <div class="tab-pane fade collapse" :class="{'show active': tab === 'courses'}" id="courses" role="tabpanel" aria-labelledby="courses-tab">
               {% include 'admin/itinerary/_tab-courses.html.twig' %}    
            </div>
      <div class="tab-pane fade collapse"
                 :class="{'show active': tab === 'users'}" id="users" role="tabpanel" aria-labelledby="users-tab">
        {% include 'admin/itinerary/_tab-users.html.twig' %}
      </div>

      {% if not app.user.isTeamManager %}
                <div class="tab-pane fade collapse" :class="{'show active': tab === 'managers'}" id="managers" role="tabpanel" aria-labelledby="users-tab">
        {% include 'admin/itinerary/_tab-managers.html.twig' %}
      </div>

                <div class="tab-pane fade collapse" :class="{'show active': tab === 'filters'}" id="filters" role="tabpanel" aria-labelledby="filters-tab">
        {% include 'admin/itinerary/_tab-filters.html.twig' %}
      </div>
      {% endif %}

          <div class="tab-pane fade collapse" :class="{'show active': tab === 'statistics'}" id="statistics" role="tabpanel" aria-labelledby="statistics-tab" >
        {% include 'admin/itinerary/_tab-stats.html.twig' %}
      </div>
    </div>
  </div>

        {% if not app.user.isTeamManager %}
            {% include 'admin/itinerary/_modal-export-users.html.twig' %}
        {% endif %}

        {% include 'admin/itinerary/_modal-add-courses.html.twig' %}
        {{ include ("admin/itinerary/_modal_users_by_process.html.twig") }}


</div>
{% endblock main %}

{% block body_javascript %}
<script>
  let filterCategories = {{ filterCategories | json_encode | raw }};
  let itineraryActive = {{ itinerary.active | json_encode | raw }};
  let isTeamManager = {{ app.user.isTeamManager ? 'true' : 'false' }};
  const assetsDir = "{{ asset('assets/chapters/') }}"
  const courseTranslations = {
    course_started_in_period_title: "{{ 'stats.export.filter.course_started_in_period_title'|trans({}, 'messages',  app.user.locale) }}",
    course_finished_in_period_title: "{{ 'stats.export.filter.course_finished_in_period_title'|trans({}, 'messages',  app.user.locale) }}",
    export_success: "{{ 'stats.export.export_success'|trans({}, 'messages',  app.user.locale) }}",
    export_error: "{{ 'stats.export.export_error'|trans({}, 'messages',  app.user.locale) }}",
    export_dir: "{{ 'stats.export.export_dir'|trans({}, 'messages',  app.user.locale) }}",
    itinerary_validation: "{{ 'itinerary.delete.confirm.validation'|trans({}, 'messages',  app.user.locale) }}",
    itinerary_title: "{{ 'itinerary.delete.confirm.title'|trans({}, 'messages',  app.user.locale) }}",
  }
  const courseStatus = {{courseStatus != null ? courseStatus | json_encode | raw : 'null'}}
  let filters = []
  fetch('/admin/api/v1/filters/by-categories')
    .then((res) => res.json())
    .then((data) => {
        filters = (data.data || []).map(item => ({
            ...item,
            filters: (item.filters || []).filter(filter => !!filter.name),
            key: `filter-${item.id}`,
        }))
    })
    .catch(() => {
  })
</script>

    {{ parent() }}
    {{ encore_entry_script_tags('itinerary') }}
{% endblock %}