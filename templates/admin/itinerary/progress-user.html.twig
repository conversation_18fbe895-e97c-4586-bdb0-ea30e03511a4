<div class="modal fade itinerary-courses-progress" :id="`staticBackdrop${user.id}`" data-bs-backdrop="static" data-bs-keyboard="false"
     tabindex="-1" aria-labelledby="staticBackdropLabel" >
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header py-2 align-items-center bg-dark">
                <h5 class="modal-title text-white w-100 d-flex justify-content-between mr-3" id="staticBackdropLabel">
                    <span>
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        {{ itinerary }}
                    </span>

                    <span class="fw-normal">
                        <i class="fas fa-user mr-1"></i>
                        ${user.first_name} ${user.last_name}
                    </span>
                </h5>

                <button type="button" class="border-0 text-white bg-transparent" data-bs-dismiss="modal" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div v-if="progressUsers.length">
                    <div v-if="userCoursesTotal">
                        <strong>${ $t('PROGRESS') }:</strong>
                        <span class="text-primary">
                            ${ getUserPercentProgress(userCoursesCompleted, userCoursesTotal) } %
                        </span>
                        <br>
                        <strong>${ $t('COURSES_COMPLETED') }:</strong>
                        <span :class="{'text-primary': userCoursesCompleted === userCoursesTotal}">
                            ${ userCoursesCompleted || 0 } <b class="text-primary">/ ${ userCoursesTotal || 0 }</b>
                        </span>
                        <div class="text-end" style="margin-bottom: 10px">
                            <button type="button" class="btn btn-primary" @click="downloadReport(user.id)"><i class="fa fa-download mr-2"></i>${ $t('DOWNLOAD_REPORT') }</button>
                        </div>
                    </div>
                    <div class="col-md-12 p-0">
                        <table class="table">
                            <thead>
                                <th>${ $t('USER.COURSES.COURSE') }</th>
                                <th>${ $t('ANNOUNCEMENT.MODALS.START_END') }</th>
                                <th>{{ 'state'|trans({}) }}</th>
                                <th>${ $t('ANNOUNCEMENT.FORM.STEPS.SURVEY') }</th>
                                <th>${ $t('PROGRESS') }</th>
                            </thead>
                            <tbody>
                                <tr v-for="progress in progressUsers"
                                :key="progress.id"
                                :class="{'unstarted': progress.status === 'unstarted'}">
                                    <td>
                                        <img :src="`uploads/images/course/${progress.image }`" width="30px" height="30px" onerror="this.src='assets/chapters/default-image.svg'" />
                                        <span>${ progress.name }</span>
                                    </td>
                                    <td>
                                        <p>
                                            ${ progress.started } <br>
                                            ${ progress.finished }
                                        </p>
                                    </td>
                                    <td>
                                        <span class="badge py-1 px-2" :class="{
                                            'badge-primary': progress.status === 'started',
                                            'badge-success': progress.status === 'completed',
                                            'badge-secondary': progress.status === 'unstarted'}">
                                            ${progress.message}
                                        </span>
                                    </td>
                                    <td class="d-flex align-items-center">
                                        <icon-notification
                                            icon="fa-clipboard-check"
                                            :value="progress.details.details && progress.details.details.survey != '-' ? 0 : 1"
                                        />
                                    </td>
                                    <td>
                                	    <div class="td-progress" @click="setUserCourseDetails(progress.details, progress.courseId)" data-bs-toggle="modal" data-bs-target="#userCourseDetailModal">
									        <span :class="progress.chaptersProgress.finished === progress.chaptersProgress.totalChapters ? 'completed' : ''">
										        ${ progress.chaptersProgress.finished || 0 }
										        <b>/ ${ progress.chaptersProgress.totalChapters || 0 }</b>
									        </span>

									        <div class="progress" :style="{}">
										        <div :style="{width: getChaptersProgress(progress.chaptersProgress)}"></div>
									        </div>
								        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                        <p class="mt-2 mb-0 text-right pr-3">
                            ${$t('TOTAL_TIME')}: <b>${ totalCourseTime }</b>
                        </p>
                </div>

                <loader v-else :is-loaded="true"></loader>
                {# <div v-else>
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div> #}
            </div>

        </div>
    </div>
</div>
