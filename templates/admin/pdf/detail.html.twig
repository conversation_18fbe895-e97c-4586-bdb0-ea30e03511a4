<style>
	.offcanvas-bottom {
		right: 0;
		left: 0;
		height: 90vh !important;
		max-height: 100%;
		border-top: 1px solid rgb(0 0 0 / 20%);
		transform: translateY(100%);
	}

	.offcanvas-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1rem 1rem;
		background: hsl(210, 11%, 22%);
		color	: #fff !important;
	}

	.offcanvas-title {
		margin-bottom: 0;
		line-height: 1.5;
		color: rgb(255 255 255);
	}

	offcanvas-body, .small {
		 margin-top: 0rem !important;
		font-size: 1rem;
		padding: 0rem;
		color: rgb(0, 0, 0);
		border-radius: 7px;
		font-weight: 500;
	
	}
</style>
{% block main %}
  <div class="content_chapter_8">
    <div class="d-flex flex-row justify-content-between align-content-center w-100 pb-3">
      <div class="content-header-title">
        <!--      <h1 class="title">
            {% trans %}Paquete pdf{% endtrans %}
        </h1>      -->
      </div>
      {% if not pdf %}
        {% block pdf_add %}
          {{ include('admin/pdf/_new_form_pdf.html.twig') }}
        {% endblock %}
        {#  <div class="page-actions">
        <a class=" action-new btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\PdfCrudController').setAction('new').set('chapterId', chapter.id).set('referrer', referrer) }}">Add Pdf Package</a>
    </div> #}
      {% else %}
        <div class="page-actions">
          <button class="btn btn-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasBottom" aria-controls="offcanvasBottom">{{ 'chapter.chapter.show_pdf'|trans({}, 'chapters') }}</button>
          <a class="action-delete btn btn-danger" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\PdfCrudController').setAction('delete').setEntityId(pdf.id).set('referrer', referrerChapter) }}" formaction="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\PdfCrudController').setAction('delete').setEntityId(pdf.id).set('referrer', referrerChapter) }}" data-bs-toggle="modal" data-bs-target="#modal-delete"><i class="fa fa-trash"></i></a>
          <a class="d-none action-edit btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\PdfCrudController').setAction('edit').setEntityId(pdf.id).set('referrer', referrer) }}">Edit Pdf Package</a>
        </div>
      {% endif %}
    </div>
    {% if pdf %}
      {# <div class="viewPdf">
 <embed src="uploads/pdf/packages/{{ pdf.pdf }}#toolbar=0" type="application/pdf" class="iframePdf">
</embed>
<div> #}
      <div id="modalpdf">
        <div class="offcanvas offcanvas-bottom" tabindex="-1" id="offcanvasBottom" aria-labelledby="offcanvasBottomLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="offcanvasBottomLabel">{{ chapter.title }}</h5>
            <button type="button" class="btn-close text-reset btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body small">
            <embed src="uploads/pdf/packages/{{ pdf.pdf }}#toolbar=0" type="application/pdf" class="iframePdf" width="100%" height="98%" />
          </div>
        </div>
      </div>
    {% else %}
      No
    {% endif %}
  </div>
{% endblock %}
