{% extends '@!EasyAdmin/page/content.html.twig' %}

{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('subsidizerChapterInfo') }}
{% endblock %}

{% block page_title %}
   {{ 'announcements.label_in_singular'|trans({}, 'messages',  app.user.locale) }} : {{ announcementUser.announcement.course.name }}
    / {{ announcementUser.user.fullName }}
{% endblock page_title %}

{% block page_actions %}
    <a class="action-report btn btn-primary" href="{{ path('report-pdf-announcement-user', {'id': announcementUser.id}) }}"><i
                class="fas fa-file-pdf"></i> {{ 'announcements.configureFields.report'|trans({}, 'messages',  app.user.locale) }}
    </a>
    <a class="action-detail btn btn-secondary"
       href="{{ ea_url().setAction('detail').setEntityId(announcementUser.announcement.id).unset('announcement_user_id') }}">{{ 'back'|trans({}, 'messages',  app.user.locale) }}</a>
{% endblock %}

{% block main %}
    <div id="subsidizer-user">
        {% include "admin/subsidizer/user/information_user_course.html.twig" %}
        {% include "admin/subsidizer/user/content_course.html.twig" %}
         {% include "admin/subsidizer/user/time_by_day.html.twig" %}
          

        <div class="modal fade" id="message-modal" tabindex="-1" aria-labelledby="message-modal-label"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="message-modal-label">{% trans %}Message{% endtrans %}</h5>
                        <button type="button" class="close" data-dismiss="modal"
                                aria-label="{% trans %}Close{% endtrans %}">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        ...
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">{% trans %}Close{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="results-modal" tabindex="-1" aria-labelledby="results-modal-label"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="message-modal-label">{% trans %}Results{% endtrans %}</h5>
                        <button type="button" class="close" data-dismiss="modal"
                                aria-label="{% trans %}Close{% endtrans %}">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <quiz-results :quiz="quiz" :number="index+1" v-for="(quiz, index) in quizzes"
                                      :key="'quiz'+index"></quiz-results>
                        <roulette-results :questions="roulette" v-show="roulette.length"></roulette-results>
                        <puzzle-results :puzzle="puzzle" :number="index+1" v-for="(puzzle, index) in puzzles"
                                      :key="'puzzle'+index"></puzzle-results>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">{% trans %}Close{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </div>
         {% include "admin/subsidizer/user/task_user.html.twig" %}
    </div>

{% endblock main %}

{% block body_javascript %}
  <script type="text/javascript">
			let idAnnouncement 		    	= {{ announcementUser.announcement.id | json_encode | raw }} ;
    </script>

    {{ parent() }}
    {{ encore_entry_script_tags('subsidizerChapterInfo') }}
    {{ encore_entry_script_tags('subsidizerMessage') }}
   
{% endblock %}
