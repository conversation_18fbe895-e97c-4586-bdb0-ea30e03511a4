 <div class="tab-pane fade  mt-lg-5" id="announcement-users" role="tabpanel"
                     aria-labelledby="announcement-users">
                    <div class="content-panel">
                        <div class="content-panel-body with-rounded-top without-padding">
                            <table class="table datagrid with-rounded-top ">
                                <thead class="thead-light">
                                <tr>
                                    <th><span>{{ 'user.label_in_singular'|trans({}, 'messages',  app.user.locale) }}</th>
                                    <th><span>{{ 'announcements.configureFields.start_at'|trans({}, 'messages',  app.user.locale) }}</span></th>
                                    <th><span>{{ 'announcements.configureFields.finish_at'|trans({}, 'messages',  app.user.locale) }}</span></th>
                                    <th class="text-center"><span>{{ 'user.configureFields.finished'|trans({}, 'messages',  app.user.locale) }}</span></th>
                                    <th><span>{{ 'message_api.diploma.diploma'|trans({}, 'message_api',  app.user.locale) }}</span></th>
                                    <th></th>
                                    <th>
                                        <span class="sr-only"></span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                {% for announcementUser in announcementUsers %}
                                    <tr>
                                        <td>
                                            {% if announcement.course.typeCourse  != 2 %}
                                            <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\SubsidizerCrudController')
                                            .setAction('user')
                                            .set('announcement_user_id', announcementUser.id) }}">{{ announcementUser.user.fullName }}</a>
                                                {% else %}
                                                <span>{{ announcementUser.user.fullName }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if userCourses[announcementUser.user.id].startedAt is defined and userCourses[announcementUser.user.id].startedAt is not null %}
                                                {{ userCourses[announcementUser.user.id].startedAt | date('Y-m-d H:i') }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
                                                {{ userCourses[announcementUser.user.id].finishedAt | date('Y-m-d H:i') }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
                                                <span class="fas fa-check-circle text-success"></span>
                                            {% else %}
                                                <span class="fas fa-times-circle text-danger"></span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
                                                <a class="btn btn-primary btn-sm"
                                                   href="{{ path('print-diploma-user', {'user': announcementUser.user.id, 'announcement': announcement.id }) }}" target="_blank">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            {% else %}
                                              --
                                            {% endif %}
                                        </td>

                                        <td class="actions">
                                            {% if announcement.course.typeCourse != 2 %}
                                            <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\SubsidizerCrudController')
                                            .setAction('user')
                                            .set('announcement_user_id', announcementUser.id) }}">{% trans %}Show{% endtrans %}</a>

                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="content-panel-footer without-padding without-border">
                            <div class="list-pagination">
                                <div class="list-pagination-counter">
                                    <strong>{{ announcementUsers | length }}</strong> {{ 'announcements.configureFields.called_user'|trans({}, 'messages',  app.user.locale) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
