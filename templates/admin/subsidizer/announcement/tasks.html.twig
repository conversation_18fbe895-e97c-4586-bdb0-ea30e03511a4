<div class="tab-pane fade show  mt-lg-5" id="nav-tasks" role="tabpanel" aria-labelledby="nav-tasks-tab">

    <div class="content-panel-body with-rounded-top without-padding">

        {% if taskAnnouncement %}
            <table class="table">
                <thead>
                <tr>
                    <th scope="col">{{ 'content.configureFields.title'|trans({}, 'messages') }}</th>

                    <th scope="col">{{ 'taskCourse.configureFields.startDate'|trans({}, 'messages') }}</th>
                    <th scope="col">{{ 'taskCourse.configureFields.dateDelivery'|trans({}, 'messages') }}</th>
                    <th scope="col">{{ 'material_course.configureFields.file'|trans({}, 'messages') }}</th>
                    <th scope="col" class="text-right">{{ 'Actions'|trans({}, 'messages') }}</th>
                </tr>
                </thead>
                <tbody>
                {% for task in taskAnnouncement %}
                    <tr style="{{ task.announcement == null ? 'background:#d9dee0 ' : '' }}">
                        <td>{{ task.title }}</td>

                        <td>{% if task.announcement == null %} {{  announcement.startAt |date}} {% else %} {{ task.startDate |date }} {% endif %}</td>
                        <td>{% if task.announcement == null %} {{  announcement.finishAt |date}} {% else %} {{ task.dateDeliveryAnnouncement |date }} {% endif %}</td>
                        <td>{{ task.filesTasks|length }}</td>
                        <td class="text-right">
                            <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\TaskCourseCrudController').setAction('detailTaskSubsidizer').set('task_id', task.id) }}" class="btn btn-secondary btn-sm"><i class="fa fa-eye"></i></a>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div >
                <div class="card text-center">
                    <div class="card-header" style="height:20rem">
                        <h3 style="padding-top:8rem"> {{ 'no_content'|trans({}, 'messages') }}</h3>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>