<div class="content-panel p-3 course-contents">
    <h3>{{ 'user.configureFields.course_content'|trans({}, 'messages',  app.user.locale) }}</h3>
    <table class="table datagrid with-rounded-top " width="100%">
        <thead class="thead-light">
        <tr>
            <th><span>{{ 'user.configureFields.chapter'|trans({}, 'messages',  app.user.locale) }}</span></th>
            <th><span>{{ 'user.configureFields.content_type'|trans({}, 'messages',  app.user.locale) }}</span></th>
            <th><span>{{ 'user.configureFields.started_at'|trans({}, 'messages',  app.user.locale) }}</span></th>
            <th><span>{{ 'user.configureFields.finished_at'|trans({}, 'messages',  app.user.locale) }}</span></th>
            <th><span>{{ 'user.configureFields.time_spent'|trans({}, 'messages',  app.user.locale) }}</span></th>
            <th class="text-center"><span>{{ 'user.configureFields.finished'|trans({}, 'messages',  app.user.locale) }}</span></th>
        </tr>
        </thead>

        <tbody>
        {% for chapter in announcementUser.announcement.course.chapters %}
            <tr>
                <td>
                    {{ chapter.position }}.
                    {{ chapter.title }}
                </td>
                <td>
                    {{ chapter.type }}
                </td>
                <td>
                    {% if userCourses[announcementUser.user.id].id is defined and userChapters[announcementUser.user.id][chapter.id] is defined %}
                        {{ userChapters[announcementUser.user.id][chapter.id].startedAt | date('Y-m-d H:i') }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td>
                    {% if  userCourses[announcementUser.user.id].id is defined and userChapters[announcementUser.user.id][chapter.id] is defined and userChapters[announcementUser.user.id][chapter.id].finishedAt is not null %}
                        {{ userChapters[announcementUser.user.id][chapter.id].finishedAt | date('Y-m-d H:i') }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td>
                    {% if  userCourses[announcementUser.user.id].id is defined and userChapters[announcementUser.user.id][chapter.id] is defined %}
                        {{ userChapters[announcementUser.user.id][chapter.id].timeSpent | niceTime }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if  userCourses[announcementUser.user.id].id is defined and userChapters[announcementUser.user.id][chapter.id] is defined and userChapters[announcementUser.user.id][chapter.id].finishedAt is not null %}
                        {{ 'yes'|trans({}, 'messages',  app.user.locale) }}
                    {% else %}
                        {{ 'no'|trans({}, 'messages',  app.user.locale) }}
                    {% endif %}
                </td>

            </tr>
        {% endfor %}

        </tbody>
    </table>
</div>