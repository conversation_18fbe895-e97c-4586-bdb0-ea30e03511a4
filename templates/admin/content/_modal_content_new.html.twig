<div class="modal fade" id="modal-chapter-2" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="modal-chapter-2Label" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modal-chapter-2Label">{{ 'content.configureFields.add_content'|trans({}, 'messages', app.user.locale) }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="close-modal-chapter-2"></button>
      </div>
      <div class="modal-body">
        {% set route = '/admin/new-content/' ~ chapter.id %}
        {{ form_start(formContent, { action: route, method: 'POST' }) }}
        {{ form_widget(formContent) }}
        <div class="text-center mt-4">
          <button class="btn btn-primary">{{ button_label|default('component_video.button_save'|trans({}, 'messages', app.user.locale)) }}</button>
        </div>
        {{ form_end(formContent) }}
      </div>
    </div>
  </div>
</div>
