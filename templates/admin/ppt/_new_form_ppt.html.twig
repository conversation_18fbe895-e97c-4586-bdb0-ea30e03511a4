<!-- Button trigger modal -->
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-chapter-24">
  {{ 'chapter.add_ppt'|trans({}, 'chapters') }}
</button>

<!-- Modal -->
<div class="modal fade" id="modal-chapter-24" tabindex="-1" aria-labelledby="modal-chapter-24Label" aria-hidden="true" data-bs-backdrop="static">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content" style="max-width: 35rem">
      <div class="modal-header">
        <h5 class="modal-title" id="modal-chapter-24Label">{{ 'chapter.add_ppt'|trans({}, 'chapters') }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="close-modal-chapter-24"></button>
      </div>
      <form action="/admin/new-ppt/chapter/{{ chapter.id }}" method="post" enctype="multipart/form-data" onsubmit="handleFormSubmitFile()">
        <input type="hidden" name="referrer" id="referrerInput" value="{{ app.request.uri }}" />
        <div class="modal-body" style="margin: 0 3rem 0 3rem;">
          <div class="card" style="width: 26rem; height: 24rem; margin: auto;">
            <div class="card-body text-center">
              <img src="{{ asset('assets/chapters/ppt.svg') }}" />
            </div>
          </div>

          <div class="mb-3 mt-5">
            <input class="form-control" type="file" id="formFile" name="file" required accept=".ppt, .pptx, application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation" />
          </div>
        
          <div class="mb-3 mt-5">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="isDonwload" name="isDonwload" value="something" />
              <label class="form-check-label">{{ 'ppt.downloadable'|trans({}, 'chapters') }}</label>
            </div>

            <div class="mb-3 mt-4 text-center">
              <button type="submit" class="btn btn-primary" id="submit-btn">
                <span id="submit-text">{{ 'component_video.button_save'|trans({}, 'messages') }}</span>
                <span id="submit-spinner" class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

{{ encore_entry_script_tags('handleFormSubmitFile') }}
