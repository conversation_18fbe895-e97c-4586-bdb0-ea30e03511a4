<style>
	.offcanvas-bottom {
		right: 0;
		left: 0;
		height: 90vh !important;
		max-height: 100%;
		border-top: 1px solid rgb(0 0 0 / 20%);
		transform: translateY(100%);
	}

	.offcanvas-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1rem 1rem;
		background: hsl(210, 11%, 22%);
		color	: #fff !important;
	}

	.offcanvas-title {
		margin-bottom: 0;
		line-height: 1.5;
		color: rgb(255 255 255);
	}

	offcanvas-body, .small {
		 margin-top: 0rem !important;
		font-size: 1rem;
		padding: 0rem;
		color: rgb(0, 0, 0);
		border-radius: 7px;
		font-weight: 500;
	
	}
</style>
{% block main %}
  <div class="content_chapter_24">
    <div class="d-flex flex-row justify-content-between align-content-center w-100 pb-3">
      <div class="content-header-title">
        <!--      <h1 class="title">
            {% trans %}Paquete ppt{% endtrans %}
        </h1>      -->
      </div>
      {% if not ppt %}
        {% block ppt_add %}
          {{ include('admin/ppt/_new_form_ppt.html.twig') }}
        {% endblock %}
        {#  <div class="page-actions">
        <a class=" action-new btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\PptCrudController').setAction('new').set('chapterId', chapter.id).set('referrer', referrer) }}">Add Ppt Package</a>
    </div> #}
      {% else %}
        <div class="page-actions">
          <button class="btn btn-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasBottom" aria-controls="offcanvasBottom">{{ 'chapter.chapter.show_ppt'|trans({}, 'chapters') }}</button>
          <a class="action-delete btn btn-danger" href="{{ path('ppt_custom_delete', { id: ppt.id, referrer: referrerChapter }) }}" formaction="{{ path('ppt_custom_delete', { id: ppt.id, referrer: referrerChapter }) }}" data-bs-toggle="modal" data-bs-target="#modal-delete">
            <i class="fa fa-trash"></i>
          </a>
          <a class="d-none action-edit btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\PptCrudController').setAction('edit').setEntityId(ppt.id).set('referrer', referrer) }}">Edit Ppt Package</a>
        </div>
      {% endif %}
    </div>
    {% if ppt %}
      {# <div class="viewPpt">
 <!-- <embed src="uploads/ppt/packages/{{ ppt.ppt }}#toolbar=0" type="application/ppt" class="iframePpt"> -->
</embed>
<div> #}
      <div id="modalppt">
        <div class="offcanvas offcanvas-bottom" tabindex="-1" id="offcanvasBottom" aria-labelledby="offcanvasBottomLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="offcanvasBottomLabel">{{ chapter.title }}</h5>
            <button type="button" class="btn-close text-reset btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body small">
            <iframe src="https://view.officeapps.live.com/op/embed.aspx?src={{ absolute_url(asset('uploads/ppt/packages/' ~ ppt.ppt)) }}" 
                    class="iframePpt" 
                    width="100%" 
                    height="98%" 
                    frameborder="0">
            </iframe>

          </div>
          
        </div>
      </div>
    {% else %}
      No
    {% endif %}
  </div>
{% endblock %}
