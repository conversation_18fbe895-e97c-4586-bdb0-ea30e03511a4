<!-- Button trigger modal -->
<button type="button" class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#videoVimeo">{{ 'chapter.chapter.show_video'|trans({}, 'messages') }}</button>

<div class="modal fade" id="videoVimeo" tabindex="-1" aria-labelledby="videoVimeoLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-xl modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header" style="background-color: var(--color-neutral-darker) !important; color: #fff !important;">
        <h5 class="modal-title" id="videoVimeoLabel" style="color: #fff">{{ chapter.title }}</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" style="padding: 0 !important;">
        <div id="iframe-vimeo">
          {% if type == 'Archivo' %}
            <iframe-vimeo :urlvideo="'{{ video.urlVideo }}'"></iframe-vimeo>
          {% else %}
            <iframe-vimeo :urlvideo="'{{ video.urlVideo }}'"></iframe-vimeo>
            {# <iframe class="video" src="https://player.vimeo.com/video/{{video.identifier}}?color=ffffff&title=0&byline=0&portrait=0&rel=0" width="504" height="315" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe> #}
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
