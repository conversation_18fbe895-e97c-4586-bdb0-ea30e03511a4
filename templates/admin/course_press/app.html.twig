{% extends '@!EasyAdmin/page/content.html.twig' %}

{% block head_stylesheets %}
  {{ parent() }}
  {{ encore_entry_link_tags('course_press_app') }}
{% endblock %}

{% block content %}
  <div id="app" class="VueApp" locales="{{ locales|json_encode }}" default-locale="{{ defaultLocale }}" user-locale="{{ userLocale }}" activeRoute="{{ activeRoute }}" activeParams="{{ activeParams }}" routeStatus="{{ routeStatus }}" config="{{ config }}">
    {{ parent() }}

    <div id="froala_key" froala-key="{{ froalaKey }}"></div>
  </div>
{% endblock %}

{% block content_title %}
  <content-title></content-title>
{% endblock %}

{% block page_actions %}
  {# <page-actions></page-actions> #}
{% endblock %}

{% block main %}
  <div class="row p-0 m-0">
    <div class="col-12 p-0 m-0">
      <course-app></course-app>
    </div>
  </div>
{% endblock %}

{% block body_javascript %}
  <script>   
    let fieldsAnnouncementClient = {{ fieldsAnnouncementClient | json_encode | raw }} ;
    let froalaKey = {{ froalaKey | json_encode | raw }} ;
</script>

  {{ parent() }}
  {{ encore_entry_script_tags('course_press_app') }}
{% endblock %}
