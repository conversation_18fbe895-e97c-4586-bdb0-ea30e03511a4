{% extends '@!EasyAdmin/page/content.html.twig' %}

{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('newStats') }}
{% endblock %}

{% block page_title %}
    {{ 'stats.general_stats'|trans({}, 'messages',  app.user.locale) }}
{% endblock page_title %}

{% block main %}
    <div id="general-stats" class="stats-panel" v-cloak locale="{{ app.user.locale }}">
        {% if statsFormationSettings | length > 0 
            or statsEvolutionSettings | length > 0 
            or statsDemographySettings | length > 0 
            or statsActivitySettings | length > 0 
            or statsItinerarySettings | length > 0 %}
        {{ include("admin/stats/templates_index/newFiltersSection.html.twig") }}
        {% endif %}


        <sort-modal v-model="modalData.showModal" :config="modalData" @apply="applySorting"></sort-modal>
        <content-template
                :visible-panels="visiblePanels"
                :chart-data="chartData"
                :stats-formation-settings="statsFormationSettings"
                :stats-evolution-settings="statsEvolutionSettings"
                :stats-demography-settings="statsDemographySettings"
                :stats-activity-settings="statsActivitySettings"
                :stats-itinerary-settings="statsItinerarySettings"
                @show-modal="showModalOptions">
        </content-template>        
        <div id="print-content">
            <content-template 
            :chart-data="chartData"
            :stats-formation-settings="statsFormationSettings"
            :stats-evolution-settings="statsEvolutionSettings"
            :stats-demography-settings="statsDemographySettings"
            :stats-activity-settings="statsActivitySettings"
            :stats-itinerary-settings="statsItinerarySettings"
            :visible-panels="visiblePanelsPrint" ></content-template>
        </div>
    </div>
{% endblock main %}

{% block body_javascript %}
    {{ parent() }}
    <script type="text/javascript">
			const countries = {{ countries | json_encode | raw }};
			const courses = {{ courses | json_encode | raw }};
			const centers = {{ centers | json_encode | raw }};
			const professionalCategories = {{ professionalCategories | json_encode | raw }};
			const departaments = {{ departaments | json_encode | raw }};
			const genders = {{ genders | json_encode | raw }};
			const divisions = {{ divisions | json_encode | raw }};
			const divisionCountries = {{ divisionCountries | json_encode | raw }};
			const filterCategories = {{ filterCategories | json_encode | raw }};   
            const statsFormationSettings = {{ statsFormationSettings | json_encode | raw }};
            const statsEvolutionSettings = {{ statsEvolutionSettings | json_encode | raw }};
            const statsDemographySettings = {{ statsDemographySettings | json_encode | raw }};
            const statsActivitySettings = {{ statsActivitySettings | json_encode | raw }};
            const statsItinerarySettings = {{ statsItinerarySettings | json_encode | raw }};
    </script>
    {{ encore_entry_script_tags('newStats') }}
{% endblock %}
