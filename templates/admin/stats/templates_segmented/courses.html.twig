<div class="courses_information">
    <div class="row row-title">
        <div class="col-12">
            <h2 class="subtitle" @click="toggleAccordion('courses')">
                <span>
                    <i class="fas fa-graduation-cap"></i> {{ 'segmented_stats.title3'|trans({}, 'messages',  app.user.locale) }}
                </span>
                <i class="fa"
                   :class="{'fa-angle-down': !accordion.courses, 'fa-angle-up': accordion.courses}"
                ></i>
            </h2>
        </div>
    </div>
    <div class="row-panel" :class="{'hidePanel': !accordion.courses}">
        <div class="row">
            <div class="col-12">
                <h2 class="subtitle">
                    <i class="fas fa-chart-line"></i>
                    {{ 'segmented_stats.courses_finished'|trans({}, 'messages',  app.user.locale) }}
                </h2>
                <line-chart-double :series-data="data.courses.doubleLineChart.started"></line-chart-double>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <h2 class="subtitle">
                    <i class="fas fa-chart-line"></i>
                    {{ 'segmented_stats.certificates'|trans({}, 'messages',  app.user.locale) }}
                </h2>
                <line-chart-double :series-data="data.courses.doubleLineChart.finished"></line-chart-double>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-xs-12 col-sm-6 col-md-4 d-none">
                <h2 class="subtitle"><i class="fas fa-users"></i> {{ 'segmented_stats.title1' |trans({}, 'messages',  app.user.locale) }}</h2>
                <pie-chart :series-data="data.courses.pieChart" :colors="['#80CBC4', '#AED581', '#009688']" :inner-size="'80%'"></pie-chart>
            </div>
            <div class="col-xs-12">
                <h2 class="subtitle">
                    <i class="fas fa-globe-americas"></i>
                    {{ 'segmented_stats.distribution_by_country'|trans({}, 'messages',  app.user.locale) }}
                    [{{ 'segmented_stats.courses_finished'|trans({}, 'messages',  app.user.locale) }}]
                </h2>
                <bar-chart :series-data="data.courses.distribution.started" :tooltip="'{point.y}'" :colors="defaultColors"></bar-chart>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-xs-12">
                <h2 class="subtitle">
                    <i class="fas fa-globe-americas"></i>
                    {{ 'segmented_stats.distribution_by_country'|trans({}, 'messages',  app.user.locale) }}
                    [{{ 'segmented_stats.certificates'|trans({}, 'messages',  app.user.locale) }}]
                </h2>
                <bar-chart :series-data="data.courses.distribution.finished" :tooltip="'{point.y}'" :colors="defaultColors"></bar-chart>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div class="infoCard">
                    <p><i class="fas fa-book-open"></i> {{ 'segmented_stats.total_courses_finished'|trans({}, 'messages',  app.user.locale) }}</p>
                    <p class="number">${data.courses.department.totalStructure}</p>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-xs-12 col-sm-6">
                <div>
                    <h2 class="subtitle mb-1 mt-3">
                        <i class="fas fa-chart-bar"></i>
                        {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                        ({{ 'segmented_stats.hotel'|trans({}, 'messages',  app.user.locale) }})
                    </h2>
                    <bar-chart :series-data="data.courses.hotel.started" :tooltip="'{point.y}'"></bar-chart>
                </div>
            </div>
            <div class="col-xs-12 col-sm-6">
                <div>
                    <h2 class="subtitle mb-1 mt-3">
                        <i class="fas fa-chart-bar"></i>
                        {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                        ({{ 'segmented_stats.structure'|trans({}, 'messages',  app.user.locale) }})
                    </h2>
                    <bar-chart :series-data="data.courses.structure.started" :tooltip="'{point.y}'"></bar-chart>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div class="infoCard infoCard-colective2">
                    <p><i class="fas fa-graduation-cap"></i> {{ 'segmented_stats.total_certificates'|trans({}, 'messages',  app.user.locale) }}</p>
                    <p class="number">${data.courses.department.totalHotel}</p>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-xs-12 col-sm-6">
                <h2 class="subtitle mb-1 mt-3">
                    <i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                    ({{ 'segmented_stats.hotel'|trans({}, 'messages',  app.user.locale) }})
                </h2>
                <bar-chart :series-data="data.courses.hotel.finished"
                           :colors="['#E57373', '#EF9A9A']"
                           :tooltip="'{point.y}'"></bar-chart>
            </div>
            <div class="col-xs-12 col-sm-6">
                <h2 class="subtitle mb-1 mt-3">
                    <i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                    ({{ 'segmented_stats.structure'|trans({}, 'messages',  app.user.locale) }})
                </h2>
                <bar-chart :series-data="data.courses.structure.finished"
                           :colors="['#E57373', '#EF9A9A']"
                           :tooltip="'{point.y}'"></bar-chart>
            </div>
        </div>
    </div>
</div>
