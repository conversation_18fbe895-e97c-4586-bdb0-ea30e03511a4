<div class="persons_information">
    <div class="row row-title">
        <div class="col-12">
            <h2 class="subtitle" @click="toggleAccordion('persons')">
                <span>
                    <i class="fas fa-users"></i> {{ 'segmented_stats.title1'|trans({}, 'messages',  app.user.locale) }}
                </span>
                <i class="fa"
                   :class="{'fa-angle-down': !accordion.persons, 'fa-angle-up': accordion.persons}"
                ></i>
            </h2>
        </div>
    </div>
    <div class="row-panel" :class="{'hidePanel': !accordion.persons}">
        <div class="row">
            <div class="col-12">
                <h2 class="subtitle">
                    <i class="fas fa-chart-line"></i>
                    {{ 'stats.accumulative.chart'|trans({}, 'messages',  app.user.locale) }}
                </h2>
                <line-chart-double :series-data="data.persons.doubleLineChart"></line-chart-double>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-xs-12 col-sm-6 col-md-4">
                <h2 class="subtitle"><i class="fas fa-users"></i> {{ 'segmented_stats.title1'|trans({}, 'messages',  app.user.locale) }}</h2>
                <pie-chart :series-data="data.persons.pieChart" :colors="['#80CBC4', '#AED581', '#009688']" :inner-size="'80%'"></pie-chart>
            </div>
            <div class="col-xs-12 col-sm-6 col-md-8">
                <h2 class="subtitle">
                    <i class="fas fa-globe-americas"></i>
                    {{ 'segmented_stats.distribution_by_country'|trans({}, 'messages',  app.user.locale) }}
                </h2>
                <bar-chart :series-data="data.persons.distribution" :tooltip="'{point.y}'" :colors="defaultColors"></bar-chart>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-xs-12 col-sm-6">
                <div class="infoCard">
                    <p><i class="fa fa-university"></i> {{ 'segmented_stats.structure'|trans({}, 'messages',  app.user.locale) }}</p>
                    <p class="number">${data.persons.department.totalStructure}</p>
                </div>
                <div>
                    <h2 class="subtitle mb-1 mt-3">
                        <i class="fas fa-chart-bar"></i>
                        {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                        ({{ 'segmented_stats.structure'|trans({}, 'messages',  app.user.locale) }})
                    </h2>
                    <stacked-bar :series-data="data.persons.department.structure"></stacked-bar>
                </div>
            </div>
            <div class="col-xs-12 col-sm-6">
                <div class="infoCard infoCard-colective2">
                    <p><i class="fa fa-building"></i> {{ 'segmented_stats.hotel'|trans({}, 'messages',  app.user.locale) }}</p>
                    <p class="number">${data.persons.department.totalHotel}</p>
                </div>
                <div>
                    <h2 class="subtitle mb-1 mt-3">
                        <i class="fas fa-chart-bar"></i>
                        {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                        ({{ 'segmented_stats.hotel'|trans({}, 'messages',  app.user.locale) }})
                    </h2>
                    <stacked-bar :series-data="data.persons.department.hotel" :colors="['#E57373', '#EF9A9A']"></stacked-bar>
                </div>
            </div>
        </div>
    </div>
</div>
