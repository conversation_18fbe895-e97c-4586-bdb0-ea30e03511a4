<div class="pdf">
    <div class="row row-title">
        <div class="col-12 mt-4">
            <h2 style="border: 1px solid #37474F; border-radius: 7px; padding: 1rem; text-align: center;"><i class="fas fa-users"></i> {{ 'segmented_stats.title1'|trans({}, 'messages',  app.user.locale) }}</h2>
        </div>
    </div>
    <div class="row-panel">
        <div class="row mt-4">
            <div class="col-12" style="margin-inline: auto;">
                <line-chart-double :series-data="data.persons.doubleLineChart"></line-chart-double>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <h4 style="text-align: center;"> <i class="fas fa-globe-americas"></i> {{ 'segmented_stats.distribution_by_country'|trans({}, 'messages',  app.user.locale) }}</h4>
                <bar-chart :series-data="data.persons.distribution" :tooltip="'{point.y}'" :colors="defaultColors"></bar-chart>
            </div>
        </div>
        <div class="row" style="margin-top: 18rem;">
            <div class="col-5">
                <h4 style="text-align: center;"><i class="fas fa-users"></i> {{ 'segmented_stats.title1'|trans({}, 'messages',  app.user.locale) }}</h4>
                <pie-chart :series-data="data.persons.pieChart" :colors="['#80CBC4', '#AED581', '#009688']" :inner-size="'80%'"></pie-chart>
            </div>
            <div class="col-1"></div>
            <div class="col-6">
                <div class="col-12">
                    <div style="background-color: #80CBC4; text-align: center; padding: 1rem 0; border-radius: 7px; margin-top: 4rem;">
                        <h5 style="color: white;"><i class="fa fa-university"></i> {{ 'segmented_stats.structure'|trans({}, 'messages',  app.user.locale) }}</h5>
                        <h6 style="color: white;">${data.persons.department.totalStructure}</h6>
                    </div>
                </div>
                <div class="col-12">
                    <div style="background-color: #FFAB91; text-align: center; padding: 1rem 0; border-radius: 7px; margin-top: 1rem;">
                        <h5 style="color: white;"><i class="fa fa-building"></i> {{ 'segmented_stats.hotel'|trans({}, 'messages',  app.user.locale) }}</h5>
                        <h6 style="color: white;">${data.persons.department.totalHotel}</h6>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <h4 style="text-align: center;"><i class="fas fa-chart-bar"></i> {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }} ({{ 'segmented_stats.structure'|trans({}, 'messages',  app.user.locale) }})</h4>
                <stacked-bar :series-data="data.persons.department.structure"></stacked-bar>
            </div>
            <div class="col-12" style="margin-top: 20rem;">
                <h4 style="text-align: center;"><i class="fas fa-chart-bar"></i> {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }} ({{ 'segmented_stats.hotel'|trans({}, 'messages',  app.user.locale) }})</h4>
                <stacked-bar :series-data="data.persons.department.hotel" :colors="['#E57373', '#EF9A9A']"></stacked-bar>
            </div>
        </div>
    </div>
</div>
