<div class="pdf" style="margin-top: 40rem;">
    <div class="row row-title">
        <div class="col-12 mt-4">
            <h2 style="border: 1px solid #37474F; border-radius: 7px; padding: 1rem; text-align: center;">
                <i class="fas fa-graduation-cap"></i> {{ 'segmented_stats.title3'|trans({}, 'messages',  app.user.locale) }}
            </h2>
        </div>
    </div>
    <div class="row-panel">
        <div class="row">
            <div class="col-12 mt-4" style="margin-inline: auto;">
                <h4 style="text-align: center;"><i class="fas fa-chart-line"></i>
                    {{ 'segmented_stats.courses_started'|trans({}, 'messages',  app.user.locale) }}</h4>
                <line-chart-double :series-data="data.courses.doubleLineChart.started"></line-chart-double>
            </div>
            <div class="col-12 mt-4" style="margin-inline: auto;">
                <h4 style="text-align: center;"><i class="fas fa-chart-line"></i>
                    {{ 'segmented_stats.courses_finished'|trans({}, 'messages',  app.user.locale) }}</h4>
                <line-chart-double :series-data="data.courses.doubleLineChart.finished"></line-chart-double>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12" style="margin-top: 7rem;">
                <h4 style="text-align: center;"><i class="fas fa-users"></i> {{ 'segmented_stats.title1' |trans({}, 'messages',  app.user.locale) }}</h4>
                <pie-chart :series-data="data.courses.pieChart" :colors="['#80CBC4', '#AED581', '#009688']" :inner-size="'80%'"></pie-chart>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <h4 style="text-align: center;"> <i class="fas fa-globe-americas"></i>
                    {{ 'segmented_stats.distribution_by_country'|trans({}, 'messages',  app.user.locale) }}
                    [{{ 'segmented_stats.courses_started'|trans({}, 'messages',  app.user.locale) }}]</h4>
                <bar-chart :series-data="data.courses.distribution.started" :tooltip="'{point.y}'" :colors="defaultColors"></bar-chart>
            </div>
            <div class="col-12">
                <h4 style="text-align: center;"> <i class="fas fa-globe-americas"></i>
                    {{ 'segmented_stats.distribution_by_country'|trans({}, 'messages',  app.user.locale) }}
                    [{{ 'segmented_stats.courses_finished'|trans({}, 'messages',  app.user.locale) }}]</h4>
                <bar-chart :series-data="data.courses.distribution.finished" :tooltip="'{point.y}'" :colors="defaultColors"></bar-chart>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div style="background-color: #80CBC4; text-align: center; padding: 1rem 0; border-radius: 7px; margin-top: 4rem;">
                    <h5 style="color: white;"><i class="fas fa-book-open"></i>
                        {{ 'segmented_stats.total_courses_started'|trans({}, 'messages',  app.user.locale) }}</h5>
                    <h6 style="color: white;">${data.courses.department.totalStructure}</h6>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h4 style="text-align: center;"><i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                    ({{ 'segmented_stats.hotel'|trans({}, 'messages',  app.user.locale) }})</h4>
                <bar-chart :series-data="data.courses.hotel.started" :tooltip="'{point.y}'"></bar-chart>
            </div>
            <div class="col-12">
                <h4 style="text-align: center;"><i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                    ({{ 'segmented_stats.structure'|trans({}, 'messages',  app.user.locale) }})</h4>
                <bar-chart :series-data="data.courses.structure.started" :colors="['#E57373', '#EF9A9A']" :tooltip="'{point.y}'"></bar-chart>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12" style="margin-top: 18rem;">
                <div style="background-color: #FFAB91; text-align: center; padding: 1rem 0; border-radius: 7px; margin-top: 1rem;">
                    <h5 style="color: white;"><i class="fas fa-graduation-cap"></i>
                        {{ 'segmented_stats.total_courses_finished'|trans({}, 'messages',  app.user.locale) }}
                    </h5>
                    <h6 style="color: white;">${data.courses.department.totalHotel}</h6>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h4 style="text-align: center;"><i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                    ({{ 'segmented_stats.hotel'|trans({}, 'messages',  app.user.locale) }})</h4>
                <bar-chart :series-data="data.courses.hotel.finished" :tooltip="'{point.y}'"></bar-chart>
            </div>
            <div class="col-12">
                <h4 style="text-align: center;"><i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                    ({{ 'segmented_stats.structure'|trans({}, 'messages',  app.user.locale) }})</h4>
                <bar-chart :series-data="data.courses.structure.finished" :colors="['#E57373', '#EF9A9A']" :tooltip="'{point.y}'"></bar-chart>
            </div>
        </div>
    </div>
</div>
