{% extends '@!EasyAdmin/page/content.html.twig' %}

{% block page_title %}
    {{ 'emailing.send_email_page_title'|trans({}, 'messages',  app.user.locale) }}
{% endblock page_title %}


{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('emailing') }}
{% endblock head_stylesheets %}


{% block body_javascript %}
    {{ parent() }}
    <script type="text/javascript">
        let professionalCategories = {{ professionalCategories | json_encode | raw }};
        let departments = {{ departments | json_encode | raw }};
        let centers = {{ centers | json_encode | raw }};
        let countries = {{ countries | json_encode | raw }};
        let email = {{ email.id }};
        let indexUrl = '{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\EmailTemplateCrudController').setAction('index') | raw }}';
    </script>
    {{ encore_entry_script_tags('emailing') }}
{% endblock body_javascript %}


{% block page_content %}
    <div id="email-send">
        <div class="field-text form-group">
            <label class="form-control-label" for="recipients">{{ 'emailing.recipients'|trans({}, 'messages',  app.user.locale) }}:</label>
            <div class="form-widget">
                <select v-model="recipients" id="recipients" class="form-control">
                    <option value="test">{{ 'emailing.test_shipment'|trans({}, 'messages',  app.user.locale) }}</option>
                    <option value="users">{{ 'emailing.select_recipients'|trans({}, 'messages',  app.user.locale) }}</option>
                </select>
            </div>
        </div>

        <div class="field-text form-group" v-show="recipients == 'test'">
            <label class="form-control-label" for="emails"></label>
            <div class="form-widget">
                <textarea v-model="testEmails" id="emails" class="form-control"
                          placeholder="{{ 'emailing.test_emails_placeholder'|trans({}, 'messages',  app.user.locale) }}"></textarea>

                <small>{{ 'emailing.test_emails_help'|trans({}, 'messages',  app.user.locale) }}</small>
            </div>
        </div>

        <div v-show="recipients == 'users'">
            <div class="field-text form-group">
                <label class="form-control-label" for="sendAt">{{ 'emailing.send_at'|trans({}, 'messages',  app.user.locale) }}</label>
                <div class="form-widget">
                    <input v-model="sendAt" id="sendAt" type="datetime-local" class="form-control" />
                </div>
            </div>
            <div class="field-text form-group">
                <label class="form-control-label" for="category">{{ 'emailing.category'|trans({}, 'messages',  app.user.locale) }}</label>
                <div class="form-widget">
                    <Multiselect
                            v-model="filters.categories"
                            :options="professionalCategories"
                            :multiple="true"
                            track-by="name"
                            label="name"
                            :placeholder="'{{ 'emailing.blank_to_include_all'|trans({}, 'messages',  app.user.locale) }}'"
                    ></Multiselect>
                </div>
            </div>
            <div class="field-text form-group">
                <label class="form-control-label" for="center">{{ 'emailing.department'|trans({}, 'messages',  app.user.locale) }}</label>
                <div class="form-widget">
                    <Multiselect
                            v-model="filters.departments"
                            :options="departments"
                            :multiple="true"
                            track-by="name"
                            label="name"
                            :placeholder="'{{ 'emailing.blank_to_include_all'|trans({}, 'messages',  app.user.locale) }}'"
                    ></Multiselect>
                </div>
            </div>
            <div class="field-text form-group">
                <label class="form-control-label" for="center">{{ 'emailing.center'|trans({}, 'messages',  app.user.locale) }}</label>
                <div class="form-widget">
                    <Multiselect
                            v-model="filters.centers"
                            :options="centers"
                            :multiple="true"
                            track-by="name"
                            label="name"
                            :placeholder="'{{ 'emailing.blank_to_include_all'|trans({}, 'messages',  app.user.locale) }}'"
                    ></Multiselect>
                </div>
            </div>
            <div class="field-text form-group">
                <label class="form-control-label" for="country">{{ 'emailing.country'|trans({}, 'messages',  app.user.locale) }}</label>
                <div class="form-widget">
                    <Multiselect
                            v-model="filters.countries"
                            :options="countries"
                            :multiple="true"
                            track-by="name"
                            label="name"
                            :placeholder="'{{ 'emailing.blank_to_include_all'|trans({}, 'messages',  app.user.locale) }}'"
                    ></Multiselect>
                </div>
            </div>
        </div>

        <div class="field-text form-group">
            <label class="form-control-label"></label>
            <div class="form-widget">
                <button class="btn btn-primary" @click="send">{{ 'emailing.send_email_btn'|trans({}, 'messages',  app.user.locale) }}</button>
            </div>
        </div>
    </div>
{% endblock page_content %}
