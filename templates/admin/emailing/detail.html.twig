{% extends '@!EasyAdmin/page/content.html.twig' %}

{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('emailingDetail') }}
{% endblock %}

{% block page_title %}
    {% trans %}Email{% endtrans %} - {{ emailTemplate.subject }}
{% endblock page_title %}

{% block body_javascript %}
    {{ parent() }}
    <script type="text/javascript">
        let email = {{  emailTemplate.id }};
    </script>
    {{ encore_entry_script_tags('emailingDetail') }}
{% endblock body_javascript %}

{% block page_content %}
<div id="email-detail">
    <div class="row mb-2">
        <div class="col-md-2 text-right font-weight-bold">
            {{ 'emailing.configureFields.subject'|trans({}, 'messages',  app.user.locale) }}:
        </div>
        <div class="col-md-9">
            {{ emailTemplate.subject }}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-md-2 text-right font-weight-bold">
            {{ 'emailing.configureFields.status'|trans({}, 'messages',  app.user.locale) }}:
        </div>
        <div class="col-md-9">
            {{ emailTemplate.status }}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-md-2 text-right font-weight-bold">
            {{ 'common_areas.created_at'|trans({}, 'messages',  app.user.locale) }}:

        </div>
        <div class="col-md-9">
            {{ emailTemplate.createdAt | date('Y-m-d H:i') }}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-md-2 text-right font-weight-bold">
            {{ 'emailing.configureFields.sent_at'|trans({}, 'messages',  app.user.locale) }}:
        </div>
        <div class="col-md-9">
            {{ emailTemplate.sentAt is not null ? emailTemplate.sentAt | date('Y-m-d H:i') : '' }}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-md-2 text-right font-weight-bold">
            {{ 'emailing.configureFields.completed_at'|trans({}, 'messages',  app.user.locale) }}:
        </div>
        <div class="col-md-9">
            {{ emailTemplate.completedAt is not null ? emailTemplate.completedAt | date('Y-m-d H:i') : '' }}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-md-2 text-right font-weight-bold">
        {% trans %}Body{% endtrans %}:
        </div>
        <div class="col-md-9">
            {{ emailTemplate.body | raw }}
        </div>
    </div>

    <div class="row">
        <div class="col">
            <table class="table datagrid" v-if="recipients.length">
                <thead>
                <tr>
                    <th><span>{% trans %}User{% endtrans %}</span></th>
                    <th><span>{% trans %}Status{% endtrans %}</span></th>
                    <th><span>{% trans %}Sent at{% endtrans %}</span></th>
                </tr>
                </thead>
                <tbody>
                    <tr v-for="recipient in recipients">
                        <td>${ recipient.user.fullName }</td>
                        <td>${ recipient.status }</td>
                        <td>${ recipient.sentAt}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-right">
                            {{ 'emailing.showing'|trans({}, 'messages',  app.user.locale) }}:
                            ${init} - ${end} / ${totalCount}
                            <button @click="prevPage" class="btn btn-primary" :disabled="page <= 1">Prev</button>
                            <button @click="nextPage" class="btn btn-primary" :disabled="page >= totalPages">Next</button>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

{% endblock page_content %}
