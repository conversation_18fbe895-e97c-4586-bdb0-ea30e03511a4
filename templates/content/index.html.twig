{% extends 'content/base.html.twig' %}

{% block title %}
  Hello ScormController!
{% endblock %}

{% block body %}
  {# {% if user != null %} #}
  {# <div id="content-player"> #}
  {# <content-player id="{{ chapter.id }}" /> #}
  {# </div> #}
  {# {% else %} #}
  {# <p>Usuario no logueado</p> #}
  {# {% endif %} #}

  <script src="https://cdn.embedly.com/widgets/platform.js" charset="UTF-8"></script>
  <div id="content-player">
    <content-player id="{{ chapter.id }}" text-next="{{ 'Next'|trans({}, 'messages') }}" />
  </div>

  <script>
        {% if token is not null %}
        localStorage.setItem('token', '{{ token }}');
        {%  else %}
        localStorage.setItem('token', localStorage.getItem('user-token'));
        {% endif %}
    </script>
{% endblock %}
