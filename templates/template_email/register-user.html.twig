{% extends 'template_email/layout.html.twig' %}

{% block main %}

    <div class="containerEmail">
        <h3> {{ 'email.template_email.greet'|trans({}, 'email', locale) }} {{ user.firstName }}</h3>
        <p>
            {{ 'email.template_email.email_register'|trans({}, 'email', locale) }}
        </p>
        <p><code>{{ email.to[0].address }}</code></p>
        <p>  {{ 'email.template_email.message_active_account'|trans({}, 'email', locale) }}</p>
        <p>
            <a class="button" href="{{app.request.getSchemeAndHttpHost()}}{{path('active-account',{'id':user.id, 'hash':codigo})}}" >
                {{ 'email.template_email.active_account'|trans({}, 'email', locale) }}
            </a>
        </p>
    </div>
{% endblock main %}
