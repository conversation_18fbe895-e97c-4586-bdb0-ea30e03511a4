{% extends 'template_email/layout.html.twig' %}

{% block main %}
    <div class="containerEmail">       
          
        <h3>  {{ 'email.template_email.valued_user'|trans({}, 'email', locale) }} {{ user.firstName }}</h3>    

         {{ 'email.template_email.approved_application'|trans({}, 'email', locale)|raw }}  
        <p>
            <a class="button" href="{{app.request.getSchemeAndHttpHost()}}{{path('active-account',{'id':user.id, 'hash':codigo})}}" >
        {{ 'email.template_email.active_account'|trans({}, 'email', locale) }}
        </a>  
        </p>

        <p><code>{{ email.to[0].address }}</code></p>
         <p class="content">  {{ 'email.template_email.best_regards'|trans({}, 'email', locale) }}</p>
         <p class="content">  {{appFromName}}</p>   
</div>

{% endblock main %}