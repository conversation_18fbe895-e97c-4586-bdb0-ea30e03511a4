{% extends 'template_email/layout.html.twig' %}

{% block stylesheets %}
  {{ parent() }}
 
 {{ include('template_email/share/stylesAttend.html.twig')}}
{% endblock %}

{% block main %}
  <div class="containerEmail">
    <h3>{{ 'email.template_email.greet'|trans({}, 'email', locale) }} {{ user }}</h3>

    {% set linkCourse = '/campus/course/' ~ dataCourse.id %}
    {{ 'email.template_email.content_announcement_fundae'|trans({ '%nameTutor%': tutor, '%platform%': appFromName, '%announcement%': course, '%dateStart%': dateStart|date('Y/m/d'), '%dateFinish%': dateFinish|date('Y/m/d'), '%url%': app.request.getSchemeAndHttpHost(), '%route%': '/campus', '%linkCourse%': linkCourse }, 'email', locale)|raw }}
    {% if codigo != '' %}
      {% set path = path('recover-password-user', { id: dataUser.id, hash: codigo }) %}
      {{ 'email.template_email.active_account_fundae'|trans({ '%url%': app.request.getSchemeAndHttpHost(), '%path%': path }, 'email', locale)|raw }}
    {% endif %}

    {{ include('template_email/share/attend.html.twig')}}

    <p class="content">{{ 'email.template_email.best_regards'|trans({}, 'email', locale) }}</p>
  </div>
{% endblock %}
