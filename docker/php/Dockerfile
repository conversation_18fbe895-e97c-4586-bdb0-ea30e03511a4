FROM php:8.3-fpm

WORKDIR /var/www/html

RUN apt-get update && apt-get install -y \
      curl \
      git  \
      bash  \
      libicu-dev \
      libpq-dev \
      zlib1g-dev \
      libzip-dev \
      zip \
      unzip \
      libxml2-dev \
      vim \
      wget \
      tzdata \
      libldap2-dev \
      libjpeg-dev \
      libpng-dev \
      libfreetype6-dev \
      libonig-dev \
    && docker-php-ext-install \
      intl \
      exif \
      pcntl \
      pdo \
      pdo_mysql \
      opcache \
      zip \
      xml \
      bcmath \
      ldap \
      mysqli \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd

# Install Xdebug
RUN pecl install xdebug-3.3.0 \
  && docker-php-ext-enable xdebug

# Install Symfony CLI and Composer
RUN curl -sS https://get.symfony.com/cli/installer | bash -s -- --install-dir /usr/local/bin
RUN curl -sS https://getcomposer.org/composer.phar -o /usr/bin/composer && chmod +x /usr/bin/composer


ARG USER_ID=1000
ARG GROUP_ID=1000
RUN groupadd -g ${GROUP_ID} user && \
    useradd -u ${USER_ID} -g ${GROUP_ID} -m user

# Set default user
USER user

CMD ["php-fpm"]
