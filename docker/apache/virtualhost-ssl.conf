<VirtualHost *:443>
    ServerName  localhost
    ServerAlias easylearning.local
    DocumentRoot /var/www/html/public

    SSLEngine on
    SSLCertificateFile    /etc/ssl/private/apache-selfsigned.crt
    SSLCertificateKeyFile /etc/ssl/private/apache-selfsigned.key

    <FilesMatch "\.(cgi|shtml|phtml|php)$">
        SSLOptions +StdEnvVars
    </FilesMatch>

    <Directory /usr/lib/cgi-bin>
        SSLOptions +StdEnvVars
    </Directory>

    BrowserMatch "MSIE [2-6]" \
        nokeepalive ssl-unclean-shutdown \
        downgrade-1.0 force-response-1.0

    BrowserMatch "MSIE [17-9]" ssl-unclean-shutdown

    <Directory "/var/www/html">
            Options FollowSymLinks
            AllowOverride All
            Require all granted
            FallbackResource /index.php
    </Directory>

    <FilesMatch \.php$>
        SetHandler "proxy:fcgi://easylearning-backend-php:9000"
    </FilesMatch>
</VirtualHost>
