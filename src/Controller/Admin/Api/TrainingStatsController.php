<?php

declare(strict_types=1);

namespace App\Controller\Admin\Api;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\Itinerary;
use App\Repository\ItineraryRepository;
use App\Service\Course\Stats\General\CourseStatsService;
use App\Service\Itinerary\Stats\StatsGeneralService;
use App\V2\Application\Service\Course\CourseAuthorizationServiceInterface;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Service\SettingsService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;



/**
 * @Route("/admin/api/v1/statistics")
 * @Security("is_granted('ROLE_SUPER_ADMIN') or is_granted('ROLE_ADMIN') or is_granted('ROLE_MANAGER')")
 */
class TrainingStatsController extends AbstractController
{
    use SerializerTrait;


    private CourseStatsService $generalCourseStatsService;
    private LoggerInterface $logger;
    private ItineraryRepository $itineraryRepository;
    private StatsGeneralService $statsGeneralService;
    private EntityManagerInterface $em;
    private SettingsService $settings;
    private CourseAuthorizationServiceInterface $courseAuthorizationService;


    public function __construct(
        CourseStatsService $generalCourseStatsService,
        LoggerInterface $logger,
        ItineraryRepository $itineraryRepository,
        StatsGeneralService $statsGeneralService,
        EntityManagerInterface $em,
        SettingsService $settings,
        CourseAuthorizationServiceInterface $courseAuthorizationService

    ) {
        $this->generalCourseStatsService = $generalCourseStatsService;
        $this->logger = $logger;
        $this->itineraryRepository = $itineraryRepository;
        $this->statsGeneralService = $statsGeneralService;
        $this->em = $em;
        $this->settings = $settings;
        $this->courseAuthorizationService = $courseAuthorizationService;
    }

     /**
     * @Rest\Route("/courses/{id}/chapters", methods={"GET","POST"})
     */
    public function getChaptersStats(Course $course): Response
    {
        try {
            $chapters = $this->generalCourseStatsService->getChaptersStats($course);
            $enableReports = $this->settings->get('app.course.stats.reports');

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $chapters,
                'enableReports' => $enableReports
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error in getChaptersStatsV2', [
                'exception' => $e,
                'courseId' => $course->getId()
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'An unexpected error occurred. Please try again later.',
                'trace' => $_ENV['APP_ENV'] === 'dev' ? $e->getTrace() : [],
            ]);
        }
    }

    /**
    * @Rest\Route("/chapters/{id}/progress", methods={"GET","POST"})
     */
    public function getChapterStats(Chapter $chapter, Request $request): Response
    {
        try {

            $content = json_decode($request->getContent(), true);

            $itineraryId = $content['itineraryId'] ?? null;
            $itinerary = $itineraryId ? $this->em->getRepository(Itinerary::class)->find($itineraryId) : null;

            $chapters = $this->generalCourseStatsService->getChapterStats($chapter, $itinerary);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'courseId' => $chapter->getCourse()->getId(),
                'data' => $chapters,

            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error in getChaptersStats', [
                'exception' => $e,
                'courseId' => $chapter->getId()
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'An unexpected error occurred. Please try again later.',
                'trace' => $_ENV['APP_ENV'] === 'dev' ? $e->getTrace() : [],
            ]);
        }
    }

    /**
     * @Rest\Route("/itineraries/{itinerary}/courses/{course}/chapters/summary", methods={"GET","POST"})
     */
    public function getCourseSummaryItinerary(Itinerary $itinerary, Course $course): Response
    {
        try {
            $summary = $this->generalCourseStatsService->getCourseSummary($course, $itinerary);
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $summary,

            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error in getChaptersSummary', [
                'exception' => $e,
                'courseId' => $course->getId()
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'An unexpected error occurred. Please try again later.',
                'trace' => $_ENV['APP_ENV'] === 'dev' ? $e->getTrace() : [],
            ]);
        }
    }

    /**
     * @Rest\Route("/courses/{course}/chapters/summary", methods={"GET","POST"})
     */
    public function getCourseSummary(Course $course): Response
    {
        try {
            /** @var User $user */
            $user = $this->getUser();

            // Check if user can access course statistics
            $this->courseAuthorizationService->ensureUserCanAccessCourseStats($user, $course);

            $summary = $this->generalCourseStatsService->getCourseSummary($course);
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $summary,

            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error in getChaptersSummary', [
                'exception' => $e,
                'courseId' => $course->getId()
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'An unexpected error occurred. Please try again later.',
                'trace' => $_ENV['APP_ENV'] === 'dev' ? $e->getTrace() : [],
            ]);
        }
    }

    /**
     * @Rest\Post("/itineraries/{id}/summary")
     */
    public function getItinerarySummary(Itinerary $itinerary): Response{
        try {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $this->statsGeneralService->getItineraryStatistics($itinerary),
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error in getChaptersSummary', [
                'exception' => $e,
                'courseId' => $itinerary->getId()
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'An unexpected error occurred. Please try again later.',
                'trace' => $_ENV['APP_ENV'] === 'dev' ? $e->getTrace() : [],
            ]);
        }
    }

    /**
     * @Rest\Route("/itineraries/{id}/courses", methods={"GET","POST"})
     */
    public function getCoursesItineraries(Itinerary $itinerary): Response
    {
        try {
            $courses = $this->generalCourseStatsService->getCoursesByItinerary($itinerary);
            $enableReports = $this->settings->get('app.course.stats.reports');

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $courses,
                'enableReports' => $enableReports
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error in getChaptersStats', [
                'exception' => $e,
                'courseId' => $itinerary->getId()
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'An unexpected error occurred. Please try again later.',
                'trace' => $_ENV['APP_ENV'] === 'dev' ? $e->getTrace() : [],
            ]);
        }
    }

    /**
     * @Rest\Route("/itineraries/{itinerary}/courses/{course}/chapters", methods={"GET","POST"})
     */
    public function getItineraryCourseStat(Itinerary $itinerary, Course $course): Response
    {
        try {
            $chapters = $this->generalCourseStatsService->getChaptersStats($course, $itinerary);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $chapters,
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error in getItineraryCourseStat', [
                'exception' => $e,
                'courseId' => $course->getId()
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'An unexpected error occurred. Please try again later.',
                'trace' => $_ENV['APP_ENV'] === 'dev' ? $e->getTrace() : [],
            ]);
        }
    }
}
