<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\MaterialCourse;
use App\Enum\MaterialsCourseEnum;
use App\Service\SettingsService;
use App\Service\Vimeo\VimeoService;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Vimeo\Vimeo;

class MaterialCourseCrudController extends AbstractCrudController
{
    use SerializerTrait;

    private $em;
    private $context;
    private $logger;
    private $jwt;
    protected $translator;
    private $router;
    private $requestStack;
    private $adminUrlGenerator;
    protected $settings;
    protected $announcementAuthorizationService;

    public function __construct(
        EntityManagerInterface $em,
        RouterInterface $router,
        RequestStack $requestStack,
        AdminContextProvider $context,
        LoggerInterface $logger,
        JWTManager $jwt,
        TranslatorInterface $translator,
        AdminUrlGenerator $adminUrlGenerator,
        SettingsService $settings,
        AnnouncementAuthorizationService $announcementAuthorizationService
    ) {
        $this->em = $em;
        $this->context = $context;
        $this->logger = $logger;
        $this->jwt = $jwt;
        $this->translator = $translator;
        $this->router = $router;
        $this->requestStack = $requestStack;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->settings = $settings;
        $this->announcementAuthorizationService = $announcementAuthorizationService;
    }

    public static function getEntityFqcn(): string
    {
        return MaterialCourse::class;
    }

    /**
     * @Route("/admin/material-course", name="new-material-course",methods={"POST"})
     *
     * @return Response
     */
    public function newMaterialCourse(Request $request)
    {
        try {
            $idCourse = $request->get('course');
            $typeMaterial = $request->get('typeMaterial');
            $count_files = $request->get('count-files');
            $idAnnouncement = $request->get('announcement');

            $course = $this->em->getRepository(Course::class)->find($idCourse);

            $this->logger->error('El announcemente recibido es ' . $idAnnouncement);

            if (2 != $typeMaterial) {
                for ($i = 0; $i < $count_files; ++$i) {
                    $materialCourse = new MaterialCourse();
                    $file = $request->files->get('file' . $i);
                    $materialCourse->setFilename($file->getClientOriginalName());
                    $materialCourse->setName($file->getClientOriginalName());
                    $materialCourse->setFilenameFile($file);
                    $materialCourse->setCourse($course);
                    $materialCourse->setTypeMaterial($typeMaterial);
                    $materialCourse->setType(MaterialsCourseEnum::MATERIALS_COURSE);
                    $materialCourse->setIsDownload(true);
                    $materialCourse->setIsVisible(\in_array($typeMaterial, [1, 2, 4]));
                    $materialCourse->setIsActive(true);

                    if (isset($idAnnouncement) && '' != $idAnnouncement) {
                        $announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
                        $materialCourse->setAnnouncement($announcement);
                        $materialCourse->setType(MaterialsCourseEnum::MATERIALS_ANNOUNCEMENT);
                    }

                    $this->em->persist($materialCourse);
                    $this->em->flush($materialCourse);

                    if (empty($idAnnouncement) || '' == $idAnnouncement) {
                        $this->newMaterialsCourseAnnouncement($materialCourse, $course);
                    }
                }
            }

            if (2 == $typeMaterial) {
                $file = $request->files->get('file' . 0);
                $this->uploadVideoToVimeo($file, $course, $idAnnouncement);
            }

            $materialsCourse = $this->em->getRepository(MaterialCourse::class)->findBy(['course' => $course]);

            if ('' != $idAnnouncement) {
                $announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
                $route = $this->redirectAfterAddMaterialFromAnnouncement($announcement);
            } else {
                $route = $this->redirectAfterAddMaterialFromCourse($course);
            }

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'message' => $this->translator->trans('material_course.configureFields.save'),
                    'route' => $route,
                    'materials' => $materialsCourse,
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the video - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['material', 'fileable', 'detail']]);
    }

    private function redirectAfterAddMaterialFromCourse(Course $course)
    {
        return $this->adminUrlGenerator
            ->unsetAll()
            ->setController(CourseCrudController::class)
            ->setAction('detail')
            ->set('tab', 'materials')
            ->setEntityId($course->getId())
            ->generateUrl();
    }

    private function redirectAfterAddMaterialFromAnnouncement(Announcement $announcement)
    {
        return $this->adminUrlGenerator
            ->unsetAll()
            ->setController(AnnouncementCrudController::class)
            ->setAction('detail')
            ->set('tab', 'materials')
            ->setEntityId($announcement->getId())
            ->generateUrl();
    }

    private function uploadVideoToVimeo($file, Course $course, $idAnnouncement)
    {
        try {
            $client = $this->dataClientVimeo();

            $response_video = '';
            $user_id = '';
            $project_id = '';

            $announcement = '' != $idAnnouncement ? $this->em->getRepository(Announcement::class)->find($idAnnouncement) : '';

            if ($file) {
                $response_video = $client->upload($file, [
                    'name' => $file->getClientOriginalName(),
                    'privacy' => [
                        'embed' => 'whitelist',
                    ],
                ]);

                $linkVimeo = $client->request($response_video . '?fields=link');
                $user_id = $this->settings->get('app.userIdVimeo');
                $project_id = $this->settings->get('app.projectIdResourceCourse'); // Es el identificador de la carpeta
                $video_id = substr($response_video, 7); // Id del video que se esta subiendo

                $client->request("/users/$user_id/projects/$project_id/videos/$video_id", [], 'PUT');
            }

            if ($response_video) {
                $materialCourse = new MaterialCourse();
                /*  $file = $request->files->get('file' . $i); */
                $materialCourse->setFilename($file->getClientOriginalName());
                $materialCourse->setName($file->getClientOriginalName());
                /*    $materialCourse->setFilenameFile($file); */
                $materialCourse->setCourse($course);
                $materialCourse->setType(MaterialsCourseEnum::MATERIALS_COURSE);
                $materialCourse->setIsDownload(false);
                $materialCourse->setIsVisible(true);
                $materialCourse->setIsActive(true);
                if ($announcement) {
                    $materialCourse->setAnnouncement($announcement);
                    $materialCourse->setType(MaterialsCourseEnum::MATERIALS_ANNOUNCEMENT);
                } else {
                    $this->newMaterialsCourseAnnouncement($materialCourse, $course);
                }
                $materialCourse->setMimeType($file->getClientmimeType());
                $materialCourse->setTypeMaterial('2');
                $materialCourse->setUrlMaterial($linkVimeo['body']['link']);

                $this->em->persist($materialCourse);
            }

            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $this->translator->trans('material_course.configureFields.save'),
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the video - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    private function dataClientVimeo()
    {
        $client_id = $this->settings->get('app.clientIdVimeo');
        $client_secret = $this->settings->get('app.clientSecretVimeo');
        $access_token = $this->settings->get('app.accessTokenVimeo');

        $client = new Vimeo($client_id, $client_secret, $access_token);

        return $client;
    }

    /**
     * @Route("/admin/fetch-material-course/{course}", name="fetch-material-course",methods={"GET"})
     *
     * @return Response
     */
    public function fetchMaterialCourse(Course $course)
    {
        try {
            $materialCourse = $this->em->getRepository(MaterialCourse::class)->findBy(['course' => $course, 'announcement' => null]);

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $materialCourse,
                'basePath' => 'uploads/material_course',
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the video - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['material', 'fileable', 'detail']]);
    }

    /**
     * @Route("/admin/fetch-material-announcement/{announcement}", name="fetch-material-announcement",methods={"GET"})
     *
     * @return Response
     */
    public function fetchMaterialAnnoucement(Announcement $announcement)
    {
        try {
            $materialCourse = $this->em->getRepository(MaterialCourse::class)->findBy(['course' => $announcement->getCourse(), 'announcement' => null]);
            $materialAnnouncement = $this->em->getRepository(MaterialCourse::class)->findBy(['announcement' => $announcement]);

            $material = array_merge($materialCourse, $materialAnnouncement);

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $material,
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the video - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['material', 'fileable', 'detail']]);
    }

    /**
     * @Route("/admin/delete-material-course", name="delete-material-course",methods={"POST"})
     *
     * @return Response
     */
    public function deleteMaterialCourse(Request $request)
    {
        try {
            $requesData = json_decode($request->getContent(), true);
            $idCourse = $requesData['course'];
            $idMaterialCourse = $requesData['idMaterialCourse'];

            $course = $this->em->getRepository(Course::class)->find($idCourse);
            $materialCourse = $this->em->getRepository(MaterialCourse::class)->find($idMaterialCourse);
            $this->deleteMaterialCourseAnnouncement($idMaterialCourse);

            if ('2' != $materialCourse->getTypeMaterial()) {
                $this->deleteMaterialFile($materialCourse->getFilename());
                $this->em->remove($materialCourse);
                /*   $this->em->persist($materialCourse); */
                $this->em->flush();
            }

            if (2 == $materialCourse->getTypeMaterial()) {
                $this->deleteVideoFromVimeo($materialCourse);
            }

            if (isset($requesData['announcement'])) {
                $idAnnouncement = $requesData['announcement'];
                $announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
                $route = $this->redirectAfterAddMaterialFromAnnouncement($announcement);
            } else {
                $route = $this->redirectAfterAddMaterialFromCourse($course);
            }

            $materialCourse = $this->em->getRepository(MaterialCourse::class)->findBy(['course' => $course, 'announcement' => null]);

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'message' => $this->translator->trans('material_course.configureFields.save'),
                    'route' => $route,
                    'materials' => $materialCourse,
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to deleter the material - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['material', 'fileable', 'detail']]);
    }

    private function deleteMaterialFile($file)
    {
        $filename = $this->settings->get('app.material_course_path') . $file;

        if (file_exists($filename)) {
            $success = unlink($filename);

            if (!$success) {
                throw $this->createNotFoundException("Cannot delete $filename");
            }
        }
    }

    private function deleteVideoFromVimeo(MaterialCourse $materialCourse)
    {
        try {
            $client = $this->dataClientVimeo();

            $user_id = $this->settings->get('app.userIdVimeo');
            $project_id = $this->settings->get('app.projectIdResourceCourse'); // Es el identificador de la carpeta

            $identifierVideo = $materialCourse->getIdentifierVideo();
            $client->request("/users/$user_id/projects/$project_id/videos/$identifierVideo", [], 'DELETE');

            $this->em->remove($materialCourse);
            $this->em->flush();

            $response = [
                'status' => 200,
                'error' => false,
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to delete the video - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Rest\Put("/admin/material-course/{id}/downloadable")
     */
    public function changeMaterialDownloadStatus(Request $request, MaterialCourse $materialCourse): Response
    {
        $downloadable = $request->get('downloadable', null);
        if (null === $downloadable) {
            $content = json_decode($request->getContent(), true);
            $downloadable = $content['downloadable'];
        }

        $materialCourse->setIsDownload($downloadable);
        $this->em->flush($materialCourse);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'UPDATED',
        ]);
    }

    /**
     * @Rest\Put("/admin/material-course/{id}/visibitily")
     */
    public function changeMaterialVisibitilytatus(Request $request, MaterialCourse $materialCourse): Response
    {
        $downloadable = $request->get('visibitily', null);
        if (null === $downloadable) {
            $content = json_decode($request->getContent(), true);
            $downloadable = $content['visibitily'];
        }

        $materialCourse->setIsVisible($downloadable);
        $this->em->flush($materialCourse);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'UPDATED',
        ]);
    }

    /**
     * @Rest\Put("/admin/material-course/{id}/active")
     */
    public function changeMaterialActiveStatus(Request $request, MaterialCourse $materialCourse): Response
    {
        $isActive = $request->get('isActive');
        if (null === $isActive) {
            $content = json_decode($request->getContent(), true);
            $isActive = $content['isActive'];
        }

        $materialCourse->setIsActive((bool) $isActive);
        $this->em->flush($materialCourse);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'UPDATED',
        ]);
    }

    /**
     * @Rest\Put("/admin/announcement-material-course/{id}/visibitily")
     */
    public function changeAnnouncementMaterialVisibitilytatus(Request $request, MaterialCourse $materialCourse): Response
    {
        $visibitily = $request->get('visibitily', null);
        if (null === $visibitily) {
            $content = json_decode($request->getContent(), true);
            $visibitily = $content['visibitily'];
        }

        $idAnnouncement = $request->get('announcement', null);
        if (null === $idAnnouncement) {
            $content = json_decode($request->getContent(), true);
            $idAnnouncement = $content['announcement'];
        }
        /** @var Announcement $announcement */
        $announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
        $result = $this->em->getRepository(MaterialCourse::class)->findBy(['id' => $materialCourse->getId(), 'announcement' => $announcement]);

        if ($result) {
            /** @var MaterialCourse $announcementMaterialCourse */
            $announcementMaterialCourse = $result[0];
            $announcementMaterialCourse->setIsVisible($visibitily);
            $this->em->persist($announcementMaterialCourse);
            $this->em->flush($announcementMaterialCourse);
        } else {
            $cloneMaterialCourse = clone $materialCourse;
            $cloneMaterialCourse->setIsVisible($visibitily);
            $cloneMaterialCourse->setAnnouncement($announcement);
            $cloneMaterialCourse->setType(MaterialsCourseEnum::CLONE_MATERIALS_COURSE);
            $this->em->persist($cloneMaterialCourse);
            $this->em->flush();
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'UPDATED',
        ]);
    }

    /**
     * @Rest\Put("/admin/announcement-material-course/{id}/active")
     */
    public function changeAnnouncementMaterialActiveStatus(Request $request, MaterialCourse $materialCourse): Response
    {
        $isActive = $request->get('isActive', null);
        if (null === $isActive) {
            $content = json_decode($request->getContent(), true);
            $isActive = $content['isActive'];
        }

        $idAnnouncement = $request->get('announcement', null);
        if (null === $idAnnouncement) {
            $content = json_decode($request->getContent(), true);
            $idAnnouncement = $content['announcement'];
        }
        /** @var Announcement $announcement */
        $announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
        $result = $this->em->getRepository(MaterialCourse::class)->findBy(['id' => $materialCourse->getId(), 'announcement' => $announcement]);

        if ($result) {
            /** @var MaterialCourse $announcementMaterialCourse */
            $announcementMaterialCourse = $result[0];
            $announcementMaterialCourse->setIsActive($isActive);
            $this->em->persist($announcementMaterialCourse);
            $this->em->flush($announcementMaterialCourse);
        } else {
            $cloneMaterialCourse = clone $materialCourse;
            $cloneMaterialCourse->setIsActive($isActive);
            $cloneMaterialCourse->setAnnouncement($announcement);
            $cloneMaterialCourse->setType(MaterialsCourseEnum::CLONE_MATERIALS_COURSE);
            $this->em->persist($cloneMaterialCourse);
            $this->em->flush();
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'UPDATED',
        ]);
    }

    /**
     * @Rest\Delete("/admin/material-course/{id}")
     */
    public function restDeleteMaterialCourse(MaterialCourse $materialCourse, VimeoService $vimeoService): Response
    {
        try {
            if (2 === $materialCourse->getTypeMaterial()) {
                $vimeoService->deleteFromVimeo($materialCourse->getIdentifierVideo());
            } else {
                $fullPath = $this->settings->get('app.material_course_path') . DIRECTORY_SEPARATOR . $materialCourse->getFilename();
                if (!file_exists($fullPath) || !unlink($fullPath)) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_OK,
                        'error' => true,
                        'data' => 'Failed to delete',
                    ]);
                }
            }

            $this->em->remove($materialCourse);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'Material course has been deleted',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Delete("/admin/announcement-material-course/{id}")
     */
    public function restDeleteAnnouncementMaterialCourse(MaterialCourse $materialCourse, VimeoService $vimeoService): Response
    {
        try {
            $user = $this->getUser();
            $this->announcementAuthorizationService->ensureUserCanManageAnnouncementMaterials(user: $user, materialCourse: $materialCourse);
            if (MaterialsCourseEnum::CLONE_MATERIALS_COURSE !== $materialCourse->getType()) {
                if (2 === $materialCourse->getTypeMaterial()) {
                    $vimeoService->deleteFromVimeo($materialCourse->getIdentifierVideo());
                } else {
                    $fullPath = $this->settings->get('app.material_course_path') . DIRECTORY_SEPARATOR . $materialCourse->getFilename();
                    if (!file_exists($fullPath) || !unlink($fullPath)) {
                        return $this->sendResponse([
                            'status' => Response::HTTP_OK,
                            'error' => true,
                            'data' => 'Failed to delete',
                        ]);
                    }
                }

                $this->em->remove($materialCourse);
                $this->em->flush();
                $message = 'Material course has been deleted';
            } else {
                $message = 'Announcement clone materials course has been deleted';
                $this->em->remove($materialCourse);
                $this->em->flush();
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $message,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/admin/update-material-course", name="update-material-course",methods={"POST"})
     *
     * @return Response
     */
    public function allowDownloadMaterial(Request $request)
    {
        try {
            $requesData = json_decode($request->getContent(), true);
            $idMaterialCourse = $requesData['idMaterialCourse'];
            $isDownload = $requesData['isDonwload'];
            $materialCourse = $this->em->getRepository(MaterialCourse::class)->find($idMaterialCourse);

            $this->logger->error('tipo boolean' . $materialCourse->getIsDownload());

            if ($materialCourse) {
                $materialCourse->setIsDownload($isDownload);

                $this->em->persist($materialCourse);
                $this->em->flush();
            }

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'message' => $this->translator->trans('material_course.configureFields.save'),
                    'materials' => $materialCourse,
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to upate the material course - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['material', 'fileable', 'detail']]);
    }

    private function newMaterialsCourseAnnouncement(MaterialCourse $materialCourse, Course $course)
    {
        /** @var Announcement[] $announcements */
        $announcements = $course->getAnnouncements();
        if ($announcements) {
            $cloneMaterialCourse = clone $materialCourse;
            foreach ($announcements as $announcement) {
                $announcementMaterials = $cloneMaterialCourse;
                $announcementMaterials->setAnnouncement($announcement);
                $announcementMaterials->setType(MaterialsCourseEnum::CLONE_MATERIALS_COURSE);
                $announcementMaterials->setParentId($materialCourse->getId());
                $this->em->persist($announcementMaterials);
            }
            $this->em->flush();
        }
    }

    private function deleteMaterialCourseAnnouncement($idMaterialCourse)
    {
        /* @var Announcement[] $announcements */
        $announcements = $this->em->getRepository(MaterialCourse::class)->findBy(['parentId' => $idMaterialCourse]);
        foreach ($announcements as $announcement) {
            $this->em->remove($announcement);
        }
        $this->em->flush();
    }
}
