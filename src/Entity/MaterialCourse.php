<?php

declare(strict_types=1);

namespace App\Entity;

use App\Behavior\Blamable;
use App\Behavior\Fileable;
use App\Behavior\Timestampable;
use App\Repository\MaterialCourseRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=MaterialCourseRepository::class)
 *
 * @Vich\Uploadable()
 */
class MaterialCourse
{
    use Blamable;
    use Timestampable;
    use Fileable;

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"material"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="materialCourses")
     */
    private $course;

    /**
     * @ORM\ManyToOne(targetEntity=Announcement::class, inversedBy="materialCourses")
     *
     * @Groups({"material"})
     */
    private $announcement;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"material"})
     */
    private $name;

    /**
     * @Vich\UploadableField(mapping="material_course", fileNameProperty="filename", originalName="originalName", size="fileSize", mimeType="mimeType")
     */
    private $filenameFile;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"material"})
     */
    private $mimeType;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"material"})
     */
    private $typeMaterial;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"material"})
     */
    private $urlMaterial;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     *
     * @Groups({"material"})
     */
    private $isDownload;

    /**
     * @ORM\OneToMany(targetEntity=MaterialDownloadHistory::class, mappedBy="material", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $materialDownloadHistories;

    /**
     * @ORM\OneToMany(targetEntity=HistorySeenMaterial::class, mappedBy="materialCourse", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $historySeenMaterials;

    /**
     * @ORM\Column(type="boolean", nullable=false, options={"default":"1"})
     *
     * @Groups({"material"})
     */
    private $isActive;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     *
     * @Groups({"material"})
     */
    private $isVisible;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $type;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $parentId;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="materialCourses")
     *
     * @ORM\JoinColumn(name="created_by_id", referencedColumnName="id", nullable=false)
     */
    private $createdBy;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        $this->isDownload = 1;
        $this->isVisible = 1;
        $this->materialDownloadHistories = new ArrayCollection();
        $this->historySeenMaterials = new ArrayCollection();
        $this->isActive = true;
    }

    public function __toString()
    {
        return $this->name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): self
    {
        $this->course = $course;

        return $this;
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(?Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(?string $mimeType): self
    {
        $this->mimeType = $mimeType;

        return $this;
    }

    public function getTypeMaterial(): ?string
    {
        return $this->typeMaterial;
    }

    public function setTypeMaterial(string $typeMaterial): self
    {
        $this->typeMaterial = $typeMaterial;

        return $this;
    }

    public function getUrlMaterial(): ?string
    {
        return $this->urlMaterial;
    }

    public function setUrlMaterial(?string $urlMaterial): self
    {
        $this->urlMaterial = $urlMaterial;

        return $this;
    }

    /**
     * @return string|null
     *
     * @Groups({"material"})
     */
    public function getIdentifierVideo()
    {
        if (!empty($this->getUrlMaterial())) {
            $urlVideo = $this->getUrlMaterial();

            $identierVideo = substr($urlVideo, 18);
            $codigoVideo = strstr($identierVideo, '/', true);
        } else {
            $codigoVideo = '';
        }

        return $codigoVideo;
    }

    /**
     * @return string|null
     *
     * @Groups({"material"})
     */
    public function getCodeVideo()
    {
        if (!empty($this->getUrlMaterial())) {
            $urlVideo = $this->getUrlMaterial();

            $identierVideo = substr($urlVideo, -10);
            $codigoVideo = $identierVideo;
        } else {
            $codigoVideo = '';
        }

        return $codigoVideo;
    }

    public function setIsDownload(?bool $isDownload): self
    {
        $this->isDownload = $isDownload;

        return $this;
    }

    public function getIsDownload(): ?bool
    {
        if (2 == $this->typeMaterial) {
            $this->isDownload = false;
        }

        return $this->isDownload;
    }

    /**
     * @return Collection<int, MaterialDownloadHistory>
     */
    public function getMaterialDownloadHistories(): Collection
    {
        return $this->materialDownloadHistories;
    }

    public function addMaterialDownloadHistory(MaterialDownloadHistory $materialDownloadHistory): self
    {
        if (!$this->materialDownloadHistories->contains($materialDownloadHistory)) {
            $this->materialDownloadHistories[] = $materialDownloadHistory;
            $materialDownloadHistory->setMaterial($this);
        }

        return $this;
    }

    public function removeMaterialDownloadHistory(MaterialDownloadHistory $materialDownloadHistory): self
    {
        if ($this->materialDownloadHistories->removeElement($materialDownloadHistory)) {
            // set the owning side to null (unless already changed)
            if ($materialDownloadHistory->getMaterial() === $this) {
                $materialDownloadHistory->setMaterial(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, HistorySeenMaterial>
     */
    public function getHistorySeenMaterials(): Collection
    {
        return $this->historySeenMaterials;
    }

    public function addHistorySeenMaterial(HistorySeenMaterial $historySeenMaterial): self
    {
        if (!$this->historySeenMaterials->contains($historySeenMaterial)) {
            $this->historySeenMaterials[] = $historySeenMaterial;
            $historySeenMaterial->setMaterialCourse($this);
        }

        return $this;
    }

    public function removeHistorySeenMaterial(HistorySeenMaterial $historySeenMaterial): self
    {
        if ($this->historySeenMaterials->removeElement($historySeenMaterial)) {
            // set the owning side to null (unless already changed)
            if ($historySeenMaterial->getMaterialCourse() === $this) {
                $historySeenMaterial->setMaterialCourse(null);
            }
        }

        return $this;
    }

    public function getIsVisible(): ?bool
    {
        return $this->isVisible;
    }

    public function getIsActive(): bool
    {
        return $this->isActive;
    }

    public function setIsVisible(?bool $isVisible): self
    {
        $this->isVisible = $isVisible;

        return $this;
    }

    public function setIsActive(bool $isActive): self
    {
        $this->isActive = $isActive;

        return $this;
    }

    public function __clone()
    {
        $this->id = null;
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        $this->materialDownloadHistories = new ArrayCollection();
        $this->historySeenMaterials = new ArrayCollection();

        if ($this->getUploadsFolder()) {
            $this->cloneFile();
        }
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getParentId(): ?int
    {
        return $this->parentId;
    }

    public function setParentId(?int $parentId): self
    {
        $this->parentId = $parentId;

        return $this;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy): self
    {
        $this->createdBy = $createdBy;

        return $this;
    }
}
