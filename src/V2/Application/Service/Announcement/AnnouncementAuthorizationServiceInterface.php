<?php

declare(strict_types=1);

namespace App\V2\Application\Service\Announcement;

use App\Entity\Announcement as LegacyAnnouncement;
use App\Entity\Course as LegacyCourse;
use App\Entity\MaterialCourse;
use App\Entity\User;
use App\V2\Domain\Announcement\Exception\UserNotAuthorizedException;
use App\V2\Domain\Shared\Exception\InfrastructureException;

interface AnnouncementAuthorizationServiceInterface
{
    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanManageAnnouncement(User $user, LegacyAnnouncement $announcement): void;

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanCreateAnnouncement(User $user, LegacyCourse $course): void;

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanDeleteAnnouncement(User $user, LegacyAnnouncement $announcement): void;

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanCreateMaterial(User $user, LegacyAnnouncement $announcement): void;

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanManageAnnouncementMaterials(User $user, MaterialCourse $materialCourse): void;
}
