<?php

declare(strict_types=1);

namespace App\V2\Application\Service\Course;

use App\Entity\Course as LegacyCourse;
use App\Entity\User;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Exceptions\UserNotAuthorizedException;
use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerCriteria;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;

readonly class CourseAuthorizationService implements CourseAuthorizationServiceInterface
{
    public function __construct(
        private CourseCreatorRepository $courseCreatorRepository,
        private CourseManagerRepository $courseManagerRepository,
    ) {
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanManageCourseContent(User $user, LegacyCourse $course): void
    {
        if ($user->isAdmin()) {
            return;
        }

        if ($user->isCreator()) {
            if ($course->getCreatedBy()?->getId() === $user->getId()) {
                return;
            }

            try {
                $this->courseCreatorRepository->findOneBy(
                    CourseCreatorCriteria::createEmpty()
                        ->filterByUserId(new Id($user->getId()))
                        ->filterByCourseId(new Id($course->getId())),
                );

                return;
            } catch (CourseCreatorNotFoundException) {
            }
        }

        throw UserNotAuthorizedException::userNotAllowedToManageCourseContent($user->getEmail());
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanAccessCourseStats(User $user, LegacyCourse $course): void
    {
        if ($user->isAdmin()) {
            return;
        }

        if ($user->isManager()) {
            $managerCourses = $this->courseManagerRepository->findBy(
                CourseManagerCriteria::createEmpty()
                    ->filterByUserId(new Id($user->getId())),
            );

            // If manager has no assigned courses, they can access all courses (maintaining user filter scope)
            if ($managerCourses->isEmpty()) {
                return;
            }

            $hasAccessToCourse = $managerCourses->filter(
                fn (CourseManager $courseManager) => $courseManager->getCourseId()->value() === $course->getId()
            );

            if (!$hasAccessToCourse->isEmpty()) {
                return;
            }
        }

        throw UserNotAuthorizedException::userNotAllowedToAccessCourseStats($user->getEmail());
    }
}
