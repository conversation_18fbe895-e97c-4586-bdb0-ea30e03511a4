<?php

declare(strict_types=1);

namespace App\V2\Application\Service\Course;

use App\Entity\Course as LegacyCourse;
use App\Entity\User;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Exceptions\UserNotAuthorizedException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;

readonly class CourseAuthorizationService implements CourseAuthorizationServiceInterface
{
    public function __construct(
        private CourseCreatorRepository $courseCreatorRepository,
    ) {
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanManageCourseContent(User $user, LegacyCourse $course): void
    {
        if ($user->isAdmin()) {
            return;
        }

        if ($user->isCreator()) {
            if ($course->getCreatedBy()?->getId() === $user->getId()) {
                return;
            }

            try {
                $this->courseCreatorRepository->findOneBy(
                    CourseCreatorCriteria::createEmpty()
                        ->filterByUserId(new Id($user->getId()))
                        ->filterByCourseId(new Id($course->getId())),
                );

                return;
            } catch (CourseCreatorNotFoundException) {
            }
        }

        throw UserNotAuthorizedException::userNotAllowedToManageCourseContent($user->getEmail());
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function ensureUserCanAccessCourseStats(User $user, LegacyCourse $course): void
    {
        if ($user->isAdmin()) {
            return;
        }

        if ($user->isManager()) {
            if ($course->getCreatedBy()?->getId() === $user->getId()) {
                return;
            }

            try {
                $this->courseCreatorRepository->findOneBy(
                    CourseCreatorCriteria::createEmpty()
                        ->filterByUserId(new Id($user->getId()))
                        ->filterByCourseId(new Id($course->getId())),
                );

                return;
            } catch (CourseCreatorNotFoundException) {
            }
        }

        throw UserNotAuthorizedException::userNotAllowedToAccessCourseStats($user->getEmail());
    }
}
