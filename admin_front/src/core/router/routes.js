import { initValidations } from '@/core/router/guards.js'
import { LoaderService } from '@/core/services/loader.service.js'
import { USER_ROLE_LIST } from '@/contexts/shared/constants/user.constants.js'
import { ROUTE_NAMES, ROUTES_WITHOUT_FOOTER, ROUTES_WITHOUT_HEADER } from '@/core/constants/router.constants.js'
import {
  LAYOUT_PERMISSION_BY_ROLE,
  LAYOUT_PERMISSION_LIST,
} from '@/contexts/layout/constants/layoutPermission.constants.js'

function getPermissionList(contextPermission = {}) {
  const newPermissionList = {}
  Object.values(USER_ROLE_LIST).forEach((key) => {
    if (contextPermission[key] || LAYOUT_PERMISSION_BY_ROLE[key]) {
      newPermissionList[key] = [...(contextPermission[key] || []), ...(LAYOUT_PERMISSION_BY_ROLE[key] || [])]
    }
  })
  return newPermissionList
}

function routeFactory(path = '', name = '', view = '', meta = {}) {
  const app = LoaderService.importView(view)
  const withHeader = $settings.SHOW_HEADER && !ROUTES_WITHOUT_HEADER.includes(name)
  const withFooter = $settings.SHOW_FOOTER && !ROUTES_WITHOUT_FOOTER.includes(name)

  return {
    path,
    name,
    meta: {
      isPublic: false,
      requiresAuth: false,
      withHeader,
      withFooter,
      viewPermission: '',
      ...meta,
      permissions: getPermissionList(meta?.permissions || {}),
    },
    components: {
      default: () => app,
      ...(withHeader ? { header: () => LoaderService.importComponent('AppHeader') } : {}),
      ...(withFooter ? { footer: () => LoaderService.importComponent('AppFooter') } : {}),
    },
    beforeEnter: initValidations,
  }
}

export const routes = [
  routeFactory('/login', ROUTE_NAMES.NO_AUTH.LOGIN, '/LoginView'),
  routeFactory('/404', ROUTE_NAMES.NOT_FOUND, '/Page404', {
    requiresAuth: true,
    viewPermission: ROUTE_NAMES.NOT_FOUND,
  }),
  routeFactory('/users', ROUTE_NAMES.AUTH.USERS.HOME, '/UserHomeView', {
    requiresAuth: true,
    viewPermission: LAYOUT_PERMISSION_LIST.USERS,
  }),
  routeFactory('/user-form/:id(\\d+)?', ROUTE_NAMES.AUTH.USERS.FORM, '/UserFormView', {
    requiresAuth: true,
    viewPermission: LAYOUT_PERMISSION_LIST.USERS,
  }),
  routeFactory('/user-profile/:id(\\d+)', ROUTE_NAMES.AUTH.USERS.PROFILE, '/UserProfileView', {
    requiresAuth: true,
    viewPermission: LAYOUT_PERMISSION_LIST.USERS,
  }),
  routeFactory('/dev/lti', ROUTE_NAMES.SUPER_ADMIN.LTI.HOME, '/SALtiHomeView', {
    requiresAuth: true,
    viewPermission: LAYOUT_PERMISSION_LIST.SUPER_ADMIN,
  }),
  routeFactory('/dev/lti-config/:id?', ROUTE_NAMES.SUPER_ADMIN.LTI.CONFIG, '/SALtiFormView', {
    requiresAuth: true,
    viewPermission: LAYOUT_PERMISSION_LIST.SUPER_ADMIN,
  }),
]
