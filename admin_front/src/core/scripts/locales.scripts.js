import fs from 'fs'
import path from 'path'
import axios from 'axios'

const LOCALE_PATH = './src/core/locales'
const LOCALE_API_KEY = 'u-8l-rykYW5bwO4Y0YqfOTKczNmi456B'

if (LOCALE_API_KEY.length && !fs.existsSync(LOCALE_PATH)) {
  console.log(`✅  Directorio creado ${LOCALE_PATH}!`)
  fs.mkdirSync(LOCALE_PATH)
}

async function downloadLocales() {
  if (!LOCALE_API_KEY) {
    return null
  }

  const response = await axios.get(`https://localise.biz/api/export/all.json?status=translated&key=${LOCALE_API_KEY}`)
  Object.entries(response.data).forEach(([key, values]) => {
    const [locale, clientName] = key.replace(/-\w+$/, '').split('_')
    let filePath

    if (clientName) filePath = path.join(LOCALE_PATH, 'clients', clientName, `${locale}.json`)
    else filePath = path.join(LOCALE_PATH, `${locale}.json`)

    if (!fs.existsSync(path.dirname(filePath))) fs.mkdirSync(path.dirname(filePath))
    fs.writeFile(filePath, JSON.stringify(values).replaceAll('@', "'{@}'"), (err) => {
      if (err) {
        return console.log(`❌ Error ${key ? 'procesando ' + key : ''}`)
      }
      console.log(`✅ Locale ${key} guardado correctamente en ${filePath}!`)
    })
  })
}

downloadLocales().catch(() => console.log('❌  Locales Error'))
