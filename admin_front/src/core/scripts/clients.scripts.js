import * as mainConfig from '../../contexts/shared/assets/js/settings.js'
import { globSync } from 'glob'

export async function loadClientConfig(folderName) {
  const clientList = globSync('./src/clients/*/shared/assets/js/settings.js')
  const configUrl = clientList.find((route) => route.includes(`src/clients/${folderName}/`))
  let clientConfig = { default: {} }
  if (configUrl) {
    clientConfig = await import(configUrl.replace('src/', '../../src/'))
  }
  return {
    ...mainConfig.default,
    ...clientConfig.default,
  }
}
