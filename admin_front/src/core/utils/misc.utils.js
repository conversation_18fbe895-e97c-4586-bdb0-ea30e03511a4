export function objToGetString(params = {}) {
  const paramsFormatted = Object.entries(params).map(([key, value]) => {
    if (value === null || value === undefined) {
      return null
    }
    const param = encodeURIComponent(String(value).trim())
    return `${key}=${param}`
  })

  return `?${paramsFormatted.filter((value) => value).join('&')}`
}

export function getBrowserLocales() {
  return (navigator.languages || []).map((l) => l.replace(/-\w+/gi, ''))
}

export function deepObjectReplace(baseObject, partial) {
  for (let key in partial) {
    if (typeof partial[key] === 'object' && partial[key] !== null && !Array.isArray(partial[key])) {
      if (!baseObject[key]) baseObject[key] = {}
      deepObjectReplace(baseObject[key], partial[key])
    } else {
      baseObject[key] = partial[key]
    }
  }
  return baseObject
}

export function stringTo<PERSON>son(data = '', defaultValue = {}) {
  try {
    return JSON.parse(data)
  } catch (e) {
    return defaultValue
  }
}
