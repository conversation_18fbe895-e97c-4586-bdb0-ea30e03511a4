<template>
  <div class="RolesBadges">
    <div
      v-for="role in roles"
      :key="role.key"
      class="roles"
    >
      <div class="badge">
        {{ getRoleLetter($t(role.code)) }}
        <span class="message">{{ $t(role.code) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  roles: { type: Array, default: () => [] },
})
function getRoleLetter(role = '') {
  return role.trim().split('')[0]
}
</script>

<style scoped lang="scss">
.RolesBadges {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;

  .badge {
    display: grid;
    place-items: center;
    position: relative;
    cursor: pointer;
    background-color: var(--color-neutral-mid);
    padding: 0.15rem 0.35rem;
    font-size: 0.8rem;
    border-radius: 3px;

    &:hover {
      .message {
        display: initial;
      }
    }
  }

  .message {
    display: none;
    background-color: var(--color-primary-lighter);
    position: absolute;
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
    border-radius: 3px;
    bottom: 110%;
    z-index: 1;
  }
}
</style>
