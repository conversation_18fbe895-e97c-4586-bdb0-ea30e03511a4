<template>
  <div class="UserHomeView">
    <LayoutSearchInput
      v-model="searchText"
      :disabled="isLoading"
    />
    <HomeHeader
      :links="links"
      :name="$t('USER.LABEL_IN_PLURAL')"
      :description="$t('USERS.HOME.DESCRIPTION')"
      :image="image"
    >
      <BaseButton @click="openReportModal"><Icon icon="download" /> {{ $t('DOWNLOAD_REPORT') }}</BaseButton>
      <BaseButton @click="openForm"><Icon icon="plus" /> {{ $t('USERS.HOME.NEW') }}</BaseButton>
      <BaseButton @click="toggleFilters"><Icon icon="filter" /> {{ $t('FILTERS') }}</BaseButton>
    </HomeHeader>

    <HomeFilters
      v-show="showFilters"
      :filters="filterData.dynamic"
      :slot-filters="filterData.static"
      :disabled="isLoading"
      @apply="applyFilter"
      @clear="resetListFilters"
    >
      <div>
        <DynamicInput
          v-model="filterData.static.role.value"
          :item="filterData.static.role"
          :disabled="isLoading"
        />
      </div>
      <div class="dateRangeFilter">
        <label>{{ $t('FILTER.CREATED_AT_IN') }}</label>
        <DynamicInput
          v-model="filterData.static.dateFrom.value"
          :item="filterData.static.dateFrom"
          :disabled="isLoading"
          :clearable="true"
        />
        <DynamicInput
          v-model="filterData.static.dateTo.value"
          :item="filterData.static.dateTo"
          :disabled="isLoading"
          :clearable="true"
        />
      </div>
      <div class="activeFilter">
        <BaseSwitch
          v-model="filterData.static.isActive.value"
          :disabled="isLoading"
        />{{ $t('ACTIVE') }}
      </div>
    </HomeFilters>

    <main>
      <TableContainer
        :total="userListData.data.length"
        :pagination="userListData.pagination"
        :disabled="isLoading"
        @page-change="loadUserData"
      >
        <table>
          <thead>
            <tr>
              <th>{{ $t('AVATAR') }}</th>
              <th>{{ $t('SUBSCRIPTION.EMAIL') }}</th>
              <th>{{ $t('SUBSCRIPTION.NAME') }}</th>
              <th>{{ $t('SUBSCRIPTION.LAST_NAME') }}</th>
              <th>{{ $t('USER.ROLES.TITLE') }}</th>
              <th>{{ $t('LIBRARY.ACTIVE') }}</th>
              <th>{{ $t('COURSE.SCORE') }}</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="userListData.isLoading || !userListData.data.length">
              <td colspan="8">{{ $t(userListData.isLoading ? 'LOADING' : 'NO_DATA') }}</td>
            </tr>
            <tr
              v-for="user in userListData.data"
              v-else
              :key="user.key"
            >
              <td>
                <img
                  :src="user.avatar"
                  class="userAvatar"
                  :alt="user.name"
                />
              </td>
              <td>
                <span
                  class="link"
                  @click="goToProfile(user.id)"
                  >{{ user.email }}</span
                >
              </td>
              <td>{{ user.firstName }}</td>
              <td>{{ user.lastName }}</td>
              <td><RolesBadges :roles="user.roles" /></td>
              <td>
                <BaseSwitch
                  v-model="user.isActive"
                  :disabled="user.isUpdating || !user.actions.editable"
                  @change="toggleUserActive(user)"
                />
              </td>
              <td>{{ user.score }}</td>
              <td>
                <DropdownMenu :disabled="isLoading">
                  <span @click="goToProfile(user.id)">{{ $t('VIEW') }}</span>
                  <span
                    v-if="user.actions.impersonate"
                    @click="impersonate(user.id)"
                    >{{ $t('ANNOUNCEMENT.STUDENTTAB.IMPERSONATE') }}</span
                  >
                  <span
                    v-if="user.actions.editable"
                    @click="openForm(user.id)"
                    >{{ $t('EDIT') }}</span
                  >
                  <span
                    v-if="user.actions.deletable"
                    class="danger"
                    @click="toggleRemove(user.id)"
                    >{{ $t('DELETE') }}</span
                  >
                </DropdownMenu>
              </td>
            </tr>
          </tbody>
        </table>
      </TableContainer>
    </main>
    <BaseDialog
      :open-dialog="!!selectedId"
      :title="$t('ANNOUNCEMENT.DELETE_CALLED_USER.CONFIRM.TITLE')"
      @confirm="handlingRemoveUser"
      @close="selectedId = 0"
    />
    <ExportUsersDataModal
      v-if="reportModalOptions.open"
      v-model="reportModalOptions.form"
      @close="reportModalOptions.open = false"
      @reset="resetModalFilters"
      @submit="exportUserData"
    />
  </div>
</template>

<script setup>
import LayoutSearchInput from '@/contexts/shared/components/LayoutSearchInput.vue'
import HomeHeader from '@/contexts/shared/components/HomeHeader.vue'
import image from '@/contexts/users/assets/images/home.svg'
import { useUserHomeComposable } from '@/contexts/users/composables/userHome.composable.js'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import RolesBadges from '@/contexts/users/components/RolesBadges.vue'
import BaseSwitch from '@/contexts/shared/components/BaseSwitch.vue'
import DropdownMenu from '@/contexts/shared/components/DropdownMenu.vue'
import BaseDialog from '@/contexts/shared/components/BaseDialog.vue'
import ExportUsersDataModal from '@/contexts/users/components/ExportUsersDataModal.vue'
import DynamicInput from '@/contexts/shared/components/DynamicInput.vue'
import HomeFilters from '@/contexts/shared/components/HomeFilters.vue'
import TableContainer from '@/contexts/shared/components/TableContainer.vue'

const {
  searchText,
  links,
  reportModalOptions,
  filterData,
  showFilters,
  userListData,
  selectedId,
  isLoading,
  toggleUserActive,
  openReportModal,
  openForm,
  toggleFilters,
  goToProfile,
  toggleRemove,
  impersonate,
  handlingRemoveUser,
  resetModalFilters,
  exportUserData,
  applyFilter,
  loadUserData,
  resetListFilters,
} = useUserHomeComposable()
</script>

<style scoped lang="scss">
.UserHomeView {
  main {
    padding: 0 3rem 2rem;
  }
  :deep(.tableContainer) {
    table {
      td,
      th {
        text-align: center;
      }
    }

    .userAvatar {
      width: 3rem;
      padding: 0.25rem;
    }

    .userAvatar,
    .BaseSwitch,
    .DropdownMenu {
      margin: 0 auto;
    }

    .link {
      cursor: pointer;
      color: var(--color-primary);
    }
  }

  .activeFilter {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .dateRangeFilter {
    display: grid;
    grid-template-columns: auto auto;
    gap: 0 1rem;

    label {
      grid-column-start: 1;
      grid-column-end: 3;
    }
  }
}
</style>
