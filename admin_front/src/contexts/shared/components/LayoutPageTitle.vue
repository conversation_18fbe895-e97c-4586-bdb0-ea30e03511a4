<template>
  <Teleport
    defer
    to="#BreadcrumbTitle"
  >
    <div class="LayoutPageTitle">
      <template
        v-for="(link, index) in links"
        :key="link.key"
      >
        <span
          :class="{ link: link.name.length }"
          @click="openRoute({ name: link.name, params: link.params })"
        >
          {{ link.title }}
        </span>
        <span class="separator">{{ index < links.length - 1 ? '/' : '' }}</span>
      </template>
    </div>
  </Teleport>
</template>

<script setup>
import { inject, onMounted } from 'vue'

const props = defineProps({
  links: {
    type: Array,
    default: () => [],
  },
  name: {
    type: String,
    default: '',
  },
})

onMounted(() => {
  document.title = props.name || $settings.TITLE
})

const { openRoute } = inject('LayoutEmits')
</script>

<style scoped lang="scss">
.LayoutPageTitle {
  gap: 0.5rem;
  white-space: pre-wrap;
  word-break: break-word;
  user-select: none;

  .link {
    cursor: pointer;
    text-wrap: balance;

    &:hover {
      color: var(--color-primary);
      text-decoration: underline;
    }
  }

  .separator:not(:empty) {
    padding-inline: 0.5rem;
  }
}
</style>
