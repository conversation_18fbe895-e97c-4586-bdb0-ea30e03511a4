<template>
  <LayoutPageTitle
    :links="links"
    :name="name"
  />
  <div class="HomeHeader">
    <header>
      <div class="container">
        <div class="textContainer">
          <h1>{{ name }}</h1>
          <p>{{ description }}</p>
        </div>
        <div
          v-if="image"
          class="imageContainer"
        >
          <img
            :src="image"
            :alt="name"
          />
        </div>
      </div>
    </header>
    <section class="filters">
      <slot />
    </section>
  </div>
</template>

<script setup>
import LayoutPageTitle from '@/contexts/shared/components/LayoutPageTitle.vue'

const props = defineProps({
  links: {
    type: Array,
    default: () => [],
  },
  name: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  image: {
    type: String,
    default: '',
  },
})
</script>

<style scoped lang="scss">
@use '@/contexts/shared/assets/styles/_breakpoints' as breakpoint;
.HomeHeader {
  header {
    padding: 3rem 2rem;
    background-color: var(--color-neutral-lightest);

    .container {
      display: grid;
      grid-template-columns: 1fr 350px;
      gap: 0.5rem;

      @media #{breakpoint.$breakpoint-lg} {
        grid-template-columns: 1fr 150px;
      }

      @media #{breakpoint.$breakpoint-md} {
        grid-template-columns: 1fr;

        .imageContainer {
          display: none;
        }
      }
    }

    .textContainer,
    .imageContainer {
      display: flex;
      flex-direction: column;
    }

    .textContainer {
      h1 {
        font-size: 1.25rem;
        color: var(--color-text);
      }
      p {
        max-width: 650px;
        text-wrap: balance;
        word-break: break-word;
      }
    }

    .imageContainer {
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: end;
    gap: 0.5rem;
    padding: 2rem 3rem;
  }
}
</style>
